/** @type {import('next').NextConfig} */
const path = require('path');

const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  distDir: 'build',
  // Add rewrites to handle /health endpoint directly
  async rewrites() {
    return [
      {
        source: '/health',
        destination: '/api/health',
      },
    ];
  },
  webpack: (config, { isServer }) => {
    // Add the dist directory to the module resolution paths
    config.resolve.alias = {
      ...config.resolve.alias,
      '@lib': path.resolve(__dirname, 'dist'),
      // Add core-sdk alias to allow importing from the core-sdk directory
      'core-sdk': path.resolve(__dirname, 'dist/core-sdk'),
    };

    // Add the core-sdk directory to the module resolution paths
    config.resolve.modules = [
      ...(config.resolve.modules || []),
      path.resolve(__dirname, 'dist')
    ];

    return config;
  },
};

module.exports = nextConfig;
