# eKYC SDK Installation Guide

## 📦 Local File Distribution Model

The eKYC SDK by SCB TechX uses a **local file distribution model** where you copy the compiled SDK files directly to your project instead of installing via npm or downloading from a repository.

## 🚀 Installation Steps

### Step 1: Obtain the SDK Files

Contact SCB TechX to obtain the compiled eKYC SDK files, or build them from the source project:

```bash
# If you have access to the source project
cd /path/to/ekyc-sdk-source
npm run build:sdk
```

### Step 2: Copy SDK Files to Your Project

Copy the entire `dist/` folder from the SDK project to your application directory:

```bash
# Copy the dist folder to your project
cp -r /path/to/ekyc-sdk-project/dist ./ekyc-sdk

# Alternative: Create a dedicated SDK directory
mkdir -p ./vendor/scbtechx
cp -r /path/to/ekyc-sdk-project/dist ./vendor/scbtechx/ekyc-sdk
```

### Step 3: Verify File Structure

Ensure your project has the following structure:

```
your-project/
├── ekyc-sdk/                    # SDK files location
│   ├── ekyc-sdk.js             # Main SDK bundle
│   ├── ekyc-sdk.js.map         # Source map for debugging
│   ├── ekyc-api.js             # API handlers bundle
│   ├── facetec.js              # FaceTec module only
│   ├── utils.js                # Utilities only
│   ├── api/                    # Individual API handlers
│   │   ├── session-token.js
│   │   ├── facetec-session-token.js
│   │   ├── idscan-only.js
│   │   └── health.js
│   ├── core-sdk/               # FaceTec SDK assets
│   │   ├── FaceTecSDK.js/
│   │   └── FaceTec_images/
│   └── types/                  # TypeScript definitions
│       └── index.d.ts
├── src/                        # Your application code
└── package.json
```

## 🔧 Framework-Specific Setup

### React Projects

1. **Copy SDK files** to your React project:
```bash
cp -r /path/to/ekyc-sdk/dist ./public/ekyc-sdk
# or
cp -r /path/to/ekyc-sdk/dist ./src/ekyc-sdk
```

2. **Import in your components**:
```jsx
import EkycSDK from './ekyc-sdk/ekyc-sdk.js';
// or if placed in public folder
import EkycSDK from '/ekyc-sdk/ekyc-sdk.js';
```

### Vue Projects

1. **Copy SDK files** to your Vue project:
```bash
cp -r /path/to/ekyc-sdk/dist ./public/ekyc-sdk
# or
cp -r /path/to/ekyc-sdk/dist ./src/assets/ekyc-sdk
```

2. **Import in your components**:
```javascript
import EkycSDK from './assets/ekyc-sdk/ekyc-sdk.js';
```

### Angular Projects

1. **Copy SDK files** to your Angular project:
```bash
cp -r /path/to/ekyc-sdk/dist ./src/assets/ekyc-sdk
```

2. **Import in your services**:
```typescript
import EkycSDK from './assets/ekyc-sdk/ekyc-sdk.js';
```

3. **Update angular.json** to include SDK assets:
```json
{
  "assets": [
    "src/favicon.ico",
    "src/assets",
    {
      "glob": "**/*",
      "input": "src/assets/ekyc-sdk",
      "output": "/ekyc-sdk"
    }
  ]
}
```

### Node.js/Express Projects

1. **Copy SDK files** to your Node.js project:
```bash
cp -r /path/to/ekyc-sdk/dist ./lib/ekyc-sdk
```

2. **Import API handlers**:
```javascript
const { sessionTokenHandler } = require('./lib/ekyc-sdk/api/session-token.js');
```

## 🌐 Web Server Configuration

### Static File Serving

Ensure your web server can serve the SDK files:

#### Nginx
```nginx
location /ekyc-sdk/ {
    alias /path/to/your/project/ekyc-sdk/;
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

#### Apache
```apache
Alias /ekyc-sdk /path/to/your/project/ekyc-sdk
<Directory "/path/to/your/project/ekyc-sdk">
    Options Indexes FollowSymLinks
    AllowOverride None
    Require all granted
</Directory>
```

## 📝 TypeScript Support

If using TypeScript, copy the type definitions:

```bash
# Copy TypeScript definitions
cp -r /path/to/ekyc-sdk/dist/types ./src/@types/ekyc-sdk
```

Add to your `tsconfig.json`:
```json
{
  "compilerOptions": {
    "typeRoots": ["./node_modules/@types", "./src/@types"]
  }
}
```

## 🔄 Updates and Versioning

### Updating the SDK

1. **Obtain new SDK files** from SCB TechX
2. **Backup current SDK** (optional):
```bash
mv ./ekyc-sdk ./ekyc-sdk-backup
```
3. **Copy new SDK files**:
```bash
cp -r /path/to/new-ekyc-sdk/dist ./ekyc-sdk
```
4. **Test your application** with the new SDK version

### Version Management

Create a version tracking file:
```bash
echo "1.0.0" > ./ekyc-sdk/VERSION
```

## 🚨 Important Notes

1. **No npm Installation**: Do not attempt to install via npm - the SDK is distributed as compiled files only
2. **File Paths**: Always use relative paths to reference SDK files
3. **CORS**: Ensure your web server allows loading of JavaScript modules
4. **Updates**: Contact SCB TechX for SDK updates and new versions
5. **Support**: For technical support, contact SCB TechX directly

## 📞 Support

- **Technical Support**: <EMAIL>
- **Documentation**: [https://docs.scbtechx.io/ekyc-sdk](https://docs.scbtechx.io/ekyc-sdk)
- **Issues**: Contact SCB TechX support team
