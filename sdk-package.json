{"name": "@scbtechx/ekyc-sdk", "version": "1.0.0", "description": "Framework-agnostic eKYC SDK with FaceTec integration for identity verification by SCB TechX", "main": "dist/ekyc-sdk.js", "module": "dist/ekyc-sdk.js", "types": "dist/types/index.d.ts", "files": ["dist/", "README.md", "LICENSE"], "exports": {".": {"import": "./dist/ekyc-sdk.js", "require": "./dist/ekyc-sdk.js", "default": "./dist/ekyc-sdk.js"}, "./api": {"import": "./dist/ekyc-api.js", "require": "./dist/ekyc-api.js", "default": "./dist/ekyc-api.js"}, "./api/session-token": {"import": "./dist/api/session-token.js", "require": "./dist/api/session-token.js", "default": "./dist/api/session-token.js"}, "./api/facetec-session-token": {"import": "./dist/api/facetec-session-token.js", "require": "./dist/api/facetec-session-token.js", "default": "./dist/api/facetec-session-token.js"}, "./api/idscan-only": {"import": "./dist/api/idscan-only.js", "require": "./dist/api/idscan-only.js", "default": "./dist/api/idscan-only.js"}, "./api/enrollment-3d": {"import": "./dist/api/enrollment-3d.js", "require": "./dist/api/enrollment-3d.js", "default": "./dist/api/enrollment-3d.js"}, "./api/health": {"import": "./dist/api/health.js", "require": "./dist/api/health.js", "default": "./dist/api/health.js"}, "./facetec": {"import": "./dist/facetec.js", "require": "./dist/facetec.js", "default": "./dist/facetec.js"}, "./utils": {"import": "./dist/utils.js", "require": "./dist/utils.js", "default": "./dist/utils.js"}, "./core-sdk/*": "./dist/core-sdk/*"}, "scripts": {"build": "webpack --config webpack.sdk.config.js", "build:dev": "webpack --config webpack.sdk.config.js --mode development", "build:watch": "webpack --config webpack.sdk.config.js --watch", "prepublishOnly": "npm run build"}, "keywords": ["ekyc", "identity-verification", "facetec", "biometric", "id-scanning", "authentication", "sdk", "javascript", "typescript", "framework-agnostic"], "author": "SCB TechX", "license": "MIT", "repository": {"type": "git", "url": "https://gitlab.com/scbtechx/techx-ekyc/frontend/ekyc-browser-demo.git"}, "bugs": {"url": "#to be implemented"}, "homepage": "#to be implemented", "peerDependencies": {"react": ">=16.8.0", "vue": ">=2.6.0 || >=3.0.0", "@angular/core": ">=12.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "vue": {"optional": true}, "@angular/core": {"optional": true}}, "engines": {"node": ">=14.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}