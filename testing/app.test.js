const request = require('supertest');
const { app: mainApp, promApp: metricApp } = require('../src/app');

describe('Testing...', () => {

  var app
  var promApp

  beforeAll(() => {
      app = mainApp.listen(8080)
      promApp = metricApp.listen(9090)
  })

  afterAll(() => {
    app.close()
    promApp.close()
  })

  it('GET / : should return a message on the root endpoint', async () => {
    const res = await request(app).get('/');
    expect(res.statusCode).toBe(200);
    expect(res.text).toBe('Hello, World!');
  });

  it('GET /health : should return health status', async () => {
    const res = await request(app).get('/health');
    expect(res.statusCode).toBe(200);
    expect(res.body).toEqual({ status: 'UP' });
  });

  it('GET /actuator/health : should return health status', async () => {
    const res = await request(app).get('/actuator/health');
    expect(res.statusCode).toBe(200);
    expect(res.body).toEqual({ status: 'UP' });
  });

  it('GET /actuator/info : should return app info', async () => {
    const res = await request(app).get('/actuator/info');
    expect(res.statusCode).toBe(200);
    expect(typeof res.body).toBe('object');
  });

  it('GET /actuator/prometheus : should expose Prometheus metrics', async () => {
    const res = await request(promApp).get('/actuator/prometheus');
    expect(res.statusCode).toBe(200);
    expect(res.text).toContain('# HELP');
    expect(res.text).toContain('# TYPE');
  });

});