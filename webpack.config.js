const path = require('path');
const CopyPlugin = require('copy-webpack-plugin'); // You'll need to install this: npm install --save-dev copy-webpack-plugin

module.exports = {
  mode: 'production',
  entry: {
    index: './lib/simple.js',
    facetec: './lib/facetec.js',
    utils: './lib/infrastructure/utils/index.js',
    api: './lib/infrastructure/api/index.js',
    'api/session-token': './lib/infrastructure/api/session-token.js',
    'api/facetec-session-token': './lib/infrastructure/api/facetec-session-token.js',
    'api/idscan-only': './lib/infrastructure/api/idscan-only.js',
    'api/match-3d-2d-idscan-front': './lib/infrastructure/api/match-3d-2d-idscan-front.js',
    'api/match-3d-2d-idscan-back': './lib/infrastructure/api/match-3d-2d-idscan-back.js',
    'api/health': './lib/infrastructure/api/health.js'
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: '[name].js',
    libraryTarget: 'umd',
    library: '[name]',
    umdNamedDefine: true,
    globalObject: 'this'
  },
  // Copy the core-sdk directory to the dist folder
  plugins: [
    new CopyPlugin({
      patterns: [
        {
          from: 'lib/core-sdk',
          to: 'core-sdk',
          globOptions: {
            ignore: ['**/*.d.ts', '**/*.map'] // Ignore TypeScript definition files and source maps
          }
        },
        // Copy the core-sdk directory to the public folder for Next.js to serve
        {
          from: 'lib/core-sdk',
          to: '../public/core-sdk',
          globOptions: {
            ignore: ['**/*.d.ts', '**/*.map'] // Ignore TypeScript definition files and source maps
          }
        }
      ],
    }),
  ],
  // Configure webpack to resolve imports from the core-sdk directory
  resolve: {
    extensions: ['.js'],
    modules: [
      path.resolve(__dirname, 'lib'),
      'node_modules'
    ],
    // Add fallbacks for node core modules
    fallback: {
      fs: false,
      path: false,
      crypto: false
    }
  },
  // Prevent webpack from trying to bundle the FaceTecSDK
  externals: {
    './core-sdk/FaceTecSDK.js/FaceTecSDK': 'FaceTecSDK'
  }
};
