# eKYC SDK

A framework-agnostic JavaScript SDK for identity verification with FaceTec biometric integration by SCB TechX.

## 🚀 Quick Start

### Installation

1. **Copy the SDK files**: Copy the entire `dist/` folder from the eKYC SDK project to your application directory.

```bash
# Copy the dist folder to your project
cp -r /path/to/ekyc-sdk-project/dist ./ekyc-sdk
```

2. **Verify the file structure**: Ensure you have the following files in your project:

```
your-project/
├── ekyc-sdk/
│   ├── ekyc-sdk.js
│   ├── ekyc-api.js
│   ├── facetec.js
│   ├── utils.js
│   ├── api/
│   │   ├── session-token.js
│   │   ├── facetec-session-token.js
│   │   ├── idscan-only.js
│   │   └── health.js
│   ├── core-sdk/
│   └── types/
```

### Basic Usage

```javascript
import EkycSDK from './ekyc-sdk/ekyc-sdk.js';

// Get session token
const sessionToken = await EkycSD<PERSON>.getSessionToken('your-api-key');

// Initialize FaceTec
await EkycSDK.initializeFaceTec(sessionToken);

// Perform ID scan
const scanResult = await EkycSDK.performIDScan('device-key', sessionToken);
```

## 📦 Import Options

### Option A: Single Entry Point (Recommended)
```javascript
import EkycSDK from './ekyc-sdk/ekyc-sdk.js';

// Use namespaced API
await EkycSDK.Auth.getSessionToken({ apiKey: 'your-key' });
await EkycSDK.FaceTec.initialize(deviceKey, encryptionKey);
```

### Option B: Modular Imports
```javascript
import { Auth, FaceTec, Utils } from './ekyc-sdk/ekyc-sdk.js';

await Auth.getSessionToken({ apiKey: 'your-key' });
await FaceTec.initialize(deviceKey, encryptionKey);
```

### Option C: Specific Module Imports
```javascript
import FaceTecModule from './ekyc-sdk/facetec.js';
import { sessionTokenHandler } from './ekyc-sdk/api/session-token.js';
```

## 🏗️ Framework Integration

### React
```jsx
import { useEffect, useState } from 'react';
import EkycSDK from './ekyc-sdk/ekyc-sdk.js';

function EkycComponent() {
  const [sessionToken, setSessionToken] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleGetToken = async () => {
    setIsLoading(true);
    try {
      const token = await EkycSDK.Auth.getSessionToken({
        apiKey: 'your-api-key'
      });
      setSessionToken(token);
    } catch (error) {
      console.error('Failed to get session token:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <button onClick={handleGetToken} disabled={isLoading}>
        {isLoading ? 'Loading...' : 'Get Session Token'}
      </button>
    </div>
  );
}
```

### Vue 3
```vue
<template>
  <div>
    <button @click="getToken" :disabled="isLoading">
      {{ isLoading ? 'Loading...' : 'Get Session Token' }}
    </button>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import EkycSDK from './ekyc-sdk/ekyc-sdk.js';

const sessionToken = ref(null);
const isLoading = ref(false);

const getToken = async () => {
  isLoading.value = true;
  try {
    sessionToken.value = await EkycSDK.Auth.getSessionToken({
      apiKey: 'your-api-key'
    });
  } catch (error) {
    console.error('Failed to get session token:', error);
  } finally {
    isLoading.value = false;
  }
};
</script>
```

### Angular
```typescript
import { Injectable } from '@angular/core';
import EkycSDK from './ekyc-sdk/ekyc-sdk.js';

@Injectable({
  providedIn: 'root'
})
export class EkycService {
  async getSessionToken(apiKey: string) {
    return await EkycSDK.Auth.getSessionToken({ apiKey });
  }

  async initializeFaceTec(sessionToken: any) {
    return await EkycSDK.initializeFaceTec(sessionToken);
  }

  async performIDScan(deviceKey: string, sessionToken: any) {
    return await EkycSDK.performIDScan(deviceKey, sessionToken);
  }
}
```

### Vanilla JavaScript
```html
<script src="./ekyc-sdk/ekyc-sdk.js"></script>
<script>
  // SDK is available as global EkycSDK
  async function initializeEkyc() {
    const sessionToken = await EkycSDK.getSessionToken('your-api-key');
    await EkycSDK.initializeFaceTec(sessionToken);
  }
</script>
```

## 🔧 Server-Side API Integration

### Express.js
```javascript
import express from 'express';
import { sessionTokenHandler, facetecSessionTokenHandler } from './ekyc-sdk/ekyc-api.js';

const app = express();

// Use SDK handlers as middleware
app.get('/api/session-token', sessionTokenHandler);
app.get('/api/facetec-session-token', facetecSessionTokenHandler);
```

### Fastify
```javascript
import Fastify from 'fastify';
import { sessionTokenHandler } from './ekyc-sdk/api/session-token.js';

const fastify = Fastify();

fastify.get('/api/session-token', async (request, reply) => {
  // Adapt Next.js handler to Fastify
  const mockRes = {
    status: (code) => ({ json: (data) => reply.code(code).send(data) }),
    json: (data) => reply.send(data)
  };

  return sessionTokenHandler(request, mockRes);
});
```

### Serverless Functions
```javascript
// Vercel/Netlify Function
import { sessionTokenHandler } from './ekyc-sdk/api/session-token.js';

export default async function handler(req, res) {
  return sessionTokenHandler(req, res);
}
```

## 📚 API Reference

### Auth Namespace

#### `Auth.getSessionToken(options)`
Get a session token from the eKYC API.

**Parameters:**
- `options.apiKey` (string): API key for authentication
- `options.headers` (object): Additional headers
- `options.storeToken` (boolean): Whether to store the token locally

**Returns:** `Promise<SessionTokenResponse>`

#### `Auth.getFaceTecSessionToken(options)`
Get a FaceTec session token using stored eKYC token.

**Parameters:**
- `options.headers` (object): Additional headers
- `options.initializeFaceTec` (boolean): Whether to initialize FaceTec SDK

**Returns:** `Promise<SessionTokenResponse>`

### FaceTec Namespace

#### `FaceTec.initialize(deviceKey, encryptionKey)`
Initialize FaceTec SDK.

**Parameters:**
- `deviceKey` (string): Device key from session token response
- `encryptionKey` (string): Encryption key from session token response

**Returns:** `Promise<boolean>`

#### `FaceTec.performIDScan(options)`
Perform Photo ID Scan.

**Parameters:**
- `options.deviceKey` (string): Device key for scanning
- `options.sessionTokenResponse` (object): Session token response
- `options.headers` (object): Additional headers

**Returns:** `Promise<IDScanResult>`

### Utils Namespace

#### `Utils.generateUUID()`
Generate a new UUID v4.

**Returns:** `string`

#### `Utils.getDeviceId()`
Get or generate a device ID (persisted in localStorage).

**Returns:** `string`

## 🔒 Environment Variables

```bash
# Required for API handlers
API_BASE_URL=https://ekyc-ekyc-dev.np.scbtechx.io
JWT_TOKEN=your-jwt-token

# Optional
NEXT_PUBLIC_API_BASE_URL=https://ekyc-ekyc-dev.np.scbtechx.io
NEXT_PUBLIC_API_KEY=your-public-api-key
```

## 🛠️ SDK File Structure

After copying the `dist/` folder to your project, you'll have access to:

- `ekyc-sdk/ekyc-sdk.js` - Main SDK bundle (browser + Node.js)
- `ekyc-sdk/ekyc-api.js` - API handlers bundle (server-side)
- `ekyc-sdk/facetec.js` - FaceTec module only
- `ekyc-sdk/utils.js` - Utilities only
- `ekyc-sdk/api/` - Individual API handlers
  - `session-token.js` - Session token handler
  - `facetec-session-token.js` - FaceTec session token handler
  - `idscan-only.js` - ID scan handler
  - `health.js` - Health check handler
- `ekyc-sdk/core-sdk/` - FaceTec SDK assets
- `ekyc-sdk/types/` - TypeScript definitions

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Support

- Documentation: [https://docs.scbtechx.io/ekyc-sdk](https://docs.scbtechx.io/ekyc-sdk)
- Issues: [https://github.com/scbtechx/ekyc-sdk/issues](https://github.com/scbtechx/ekyc-sdk/issues)
- Email: <EMAIL>
