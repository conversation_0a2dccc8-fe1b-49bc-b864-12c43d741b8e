# eKYC SDK Usage Examples

## 🚀 Quick Start Examples

### Basic HTML + JavaScript

```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>eKYC SDK Example</title>
</head>
<body>
    <h1>SCB TechX eKYC SDK Example</h1>
    
    <button id="getTokenBtn">Get Session Token</button>
    <button id="initFaceTecBtn" disabled>Initialize FaceTec</button>
    <button id="scanIdBtn" disabled>Scan ID</button>
    
    <div id="status"></div>
    <div id="result"></div>

    <!-- Load the SDK from local files -->
    <script src="./ekyc-sdk/ekyc-sdk.js"></script>
    
    <script>
        // SDK is available as global EkycSDK
        let sessionToken = null;
        let deviceKey = null;
        
        const statusDiv = document.getElementById('status');
        const resultDiv = document.getElementById('result');
        
        // Get Session Token
        document.getElementById('getTokenBtn').addEventListener('click', async () => {
            try {
                statusDiv.textContent = 'Getting session token...';
                
                sessionToken = await EkycSDK.getSessionToken('your-api-key');
                deviceKey = sessionToken.data?.deviceKey;
                
                statusDiv.textContent = 'Session token obtained successfully!';
                document.getElementById('initFaceTecBtn').disabled = false;
                
                resultDiv.innerHTML = `<pre>${JSON.stringify(sessionToken, null, 2)}</pre>`;
            } catch (error) {
                statusDiv.textContent = `Error: ${error.message}`;
            }
        });
        
        // Initialize FaceTec
        document.getElementById('initFaceTecBtn').addEventListener('click', async () => {
            try {
                statusDiv.textContent = 'Initializing FaceTec...';
                
                await EkycSDK.initializeFaceTec(sessionToken);
                
                statusDiv.textContent = 'FaceTec initialized successfully!';
                document.getElementById('scanIdBtn').disabled = false;
            } catch (error) {
                statusDiv.textContent = `Error: ${error.message}`;
            }
        });
        
        // Perform ID Scan
        document.getElementById('scanIdBtn').addEventListener('click', async () => {
            try {
                statusDiv.textContent = 'Starting ID scan...';
                
                const scanResult = await EkycSDK.performIDScan(deviceKey, sessionToken);
                
                statusDiv.textContent = 'ID scan completed!';
                resultDiv.innerHTML = `<pre>${JSON.stringify(scanResult, null, 2)}</pre>`;
            } catch (error) {
                statusDiv.textContent = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
```

### React Component Example

```jsx
// components/EkycDemo.jsx
import React, { useState } from 'react';
import EkycSDK from '../ekyc-sdk/ekyc-sdk.js';

const EkycDemo = () => {
    const [sessionToken, setSessionToken] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [status, setStatus] = useState('');
    const [result, setResult] = useState(null);

    const getSessionToken = async () => {
        setIsLoading(true);
        setStatus('Getting session token...');
        
        try {
            const token = await EkycSDK.Auth.getSessionToken({
                apiKey: 'your-api-key'
            });
            
            setSessionToken(token);
            setStatus('Session token obtained successfully!');
            setResult(token);
        } catch (error) {
            setStatus(`Error: ${error.message}`);
        } finally {
            setIsLoading(false);
        }
    };

    const initializeFaceTec = async () => {
        if (!sessionToken) return;
        
        setIsLoading(true);
        setStatus('Initializing FaceTec...');
        
        try {
            await EkycSDK.initializeFaceTec(sessionToken);
            setStatus('FaceTec initialized successfully!');
        } catch (error) {
            setStatus(`Error: ${error.message}`);
        } finally {
            setIsLoading(false);
        }
    };

    const performIDScan = async () => {
        if (!sessionToken?.data?.deviceKey) return;
        
        setIsLoading(true);
        setStatus('Starting ID scan...');
        
        try {
            const scanResult = await EkycSDK.performIDScan(
                sessionToken.data.deviceKey,
                sessionToken
            );
            
            setStatus('ID scan completed!');
            setResult(scanResult);
        } catch (error) {
            setStatus(`Error: ${error.message}`);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="ekyc-demo">
            <h2>SCB TechX eKYC SDK Demo</h2>
            
            <div className="controls">
                <button 
                    onClick={getSessionToken} 
                    disabled={isLoading}
                >
                    Get Session Token
                </button>
                
                <button 
                    onClick={initializeFaceTec} 
                    disabled={isLoading || !sessionToken}
                >
                    Initialize FaceTec
                </button>
                
                <button 
                    onClick={performIDScan} 
                    disabled={isLoading || !sessionToken}
                >
                    Scan ID
                </button>
            </div>
            
            <div className="status">
                <strong>Status:</strong> {status}
            </div>
            
            {result && (
                <div className="result">
                    <h3>Result:</h3>
                    <pre>{JSON.stringify(result, null, 2)}</pre>
                </div>
            )}
        </div>
    );
};

export default EkycDemo;
```

### Vue 3 Composition API Example

```vue
<!-- components/EkycDemo.vue -->
<template>
  <div class="ekyc-demo">
    <h2>SCB TechX eKYC SDK Demo</h2>
    
    <div class="controls">
      <button @click="getSessionToken" :disabled="isLoading">
        Get Session Token
      </button>
      
      <button @click="initializeFaceTec" :disabled="isLoading || !sessionToken">
        Initialize FaceTec
      </button>
      
      <button @click="performIDScan" :disabled="isLoading || !sessionToken">
        Scan ID
      </button>
    </div>
    
    <div class="status">
      <strong>Status:</strong> {{ status }}
    </div>
    
    <div v-if="result" class="result">
      <h3>Result:</h3>
      <pre>{{ JSON.stringify(result, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import EkycSDK from '../ekyc-sdk/ekyc-sdk.js';

const sessionToken = ref(null);
const isLoading = ref(false);
const status = ref('');
const result = ref(null);

const getSessionToken = async () => {
  isLoading.value = true;
  status.value = 'Getting session token...';
  
  try {
    const token = await EkycSDK.Auth.getSessionToken({
      apiKey: 'your-api-key'
    });
    
    sessionToken.value = token;
    status.value = 'Session token obtained successfully!';
    result.value = token;
  } catch (error) {
    status.value = `Error: ${error.message}`;
  } finally {
    isLoading.value = false;
  }
};

const initializeFaceTec = async () => {
  if (!sessionToken.value) return;
  
  isLoading.value = true;
  status.value = 'Initializing FaceTec...';
  
  try {
    await EkycSDK.initializeFaceTec(sessionToken.value);
    status.value = 'FaceTec initialized successfully!';
  } catch (error) {
    status.value = `Error: ${error.message}`;
  } finally {
    isLoading.value = false;
  }
};

const performIDScan = async () => {
  if (!sessionToken.value?.data?.deviceKey) return;
  
  isLoading.value = true;
  status.value = 'Starting ID scan...';
  
  try {
    const scanResult = await EkycSDK.performIDScan(
      sessionToken.value.data.deviceKey,
      sessionToken.value
    );
    
    status.value = 'ID scan completed!';
    result.value = scanResult;
  } catch (error) {
    status.value = `Error: ${error.message}`;
  } finally {
    isLoading.value = false;
  }
};
</script>

<style scoped>
.ekyc-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.controls {
  margin: 20px 0;
}

.controls button {
  margin-right: 10px;
  padding: 10px 20px;
}

.status {
  margin: 20px 0;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.result {
  margin: 20px 0;
}

.result pre {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
```

### Node.js Express Server Example

```javascript
// server.js
const express = require('express');
const path = require('path');

// Import SDK API handlers from local files
const { sessionTokenHandler, facetecSessionTokenHandler } = require('./ekyc-sdk/ekyc-api.js');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Serve SDK files
app.use('/ekyc-sdk', express.static(path.join(__dirname, 'ekyc-sdk')));

// API routes using SDK handlers
app.get('/api/session-token', sessionTokenHandler);
app.get('/api/facetec-session-token', facetecSessionTokenHandler);

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'UP', service: 'eKYC API Server' });
});

// Serve main page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
  console.log('eKYC SDK endpoints:');
  console.log('  GET /api/session-token');
  console.log('  GET /api/facetec-session-token');
});
```

## 🔧 Configuration Examples

### Environment Variables

```bash
# .env file
API_BASE_URL=https://ekyc-ekyc-dev.np.scbtechx.io
JWT_TOKEN=your-jwt-token-here
EKYC_API_KEY=your-api-key-here
```

### Webpack Configuration (for bundling)

```javascript
// webpack.config.js
const path = require('path');

module.exports = {
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'bundle.js'
  },
  resolve: {
    alias: {
      '@ekyc-sdk': path.resolve(__dirname, 'ekyc-sdk')
    }
  }
};
```

Then import using alias:
```javascript
import EkycSDK from '@ekyc-sdk/ekyc-sdk.js';
```

## 📱 Mobile App Integration

### React Native

```javascript
// Place SDK files in assets folder and import
import EkycSDK from '../assets/ekyc-sdk/ekyc-sdk.js';

// Use in components
const EkycScreen = () => {
  const [sessionToken, setSessionToken] = useState(null);
  
  const handleGetToken = async () => {
    try {
      const token = await EkycSDK.getSessionToken('your-api-key');
      setSessionToken(token);
    } catch (error) {
      console.error('Error:', error);
    }
  };
  
  // ... rest of component
};
```

## 🚨 Important Notes

1. **File Paths**: Always use relative paths to the copied SDK files
2. **CORS**: Ensure your server allows loading JavaScript modules
3. **Updates**: Replace the entire `ekyc-sdk` folder when updating
4. **Support**: Contact SCB TechX for technical assistance

## 📞 Support

- **Email**: <EMAIL>
- **Documentation**: [https://docs.scbtechx.io/ekyc-sdk](https://docs.scbtechx.io/ekyc-sdk)
