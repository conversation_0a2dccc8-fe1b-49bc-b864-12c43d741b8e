var h,crypto;h||(h="undefined"!=typeof Module?Module:{}),h.lc||(h.lc=0),h.lc++,h.ENVIRONMENT_IS_PTHREAD||h.$ww||function(e){function t(){function t(e,t,n){this.start=e,this.end=t,this.audio=n}function n(n){if(!n)throw"Loading data file failed."+Error().stack;if(n.constructor.name!==ArrayBuffer.name)throw"bad input to processPackageData"+Error().stack;n=new Uint8Array(n),t.prototype.fd=n,n=e.files;for(var r=0;r<n.length;++r)t.prototype.uc[n[r].filename].onload();h.removeRunDependency("datafile_011c90516755d702cfb4205ca9d93e21fe6683b8.data")}h.FS_createPath("/","models",!0,!0),t.prototype={uc:{},open:function(e,t){this.name=t,this.uc[t]=this,h.addRunDependency(`fp ${this.name}`)},send:function(){},onload:function(){this.finish(this.fd.subarray(this.start,this.end))},finish:function(e){h.FS_createDataFile(this.name,null,e,!0,!0,!0),h.removeRunDependency(`fp ${this.name}`),this.uc[this.name]=null}};for(var r=e.files,a=0;a<r.length;++a)new t(r[a].start,r[a].end,r[a].audio||0).open("GET",r[a].filename);h.addRunDependency("datafile_011c90516755d702cfb4205ca9d93e21fe6683b8.data"),h.Lc||(h.Lc={}),h.Lc["011c90516755d702cfb4205ca9d93e21fe6683b8.data"]={me:!1},s?(n(s),s=null):u=n}"object"==typeof window?window.encodeURIComponent(window.location.pathname.toString().substring(0,window.location.pathname.toString().lastIndexOf("/"))+"/"):"undefined"==typeof process&&"undefined"!=typeof location&&encodeURIComponent(location.pathname.toString().substring(0,location.pathname.toString().lastIndexOf("/"))+"/"),"function"!=typeof h.locateFilePackage||h.locateFile||(h.locateFile=h.locateFilePackage,aa("warning: you defined Module.locateFilePackage, that has been renamed to Module.locateFile (using your locateFilePackage for now)"));var n,r,a,o,i=h.locateFile?h.locateFile("011c90516755d702cfb4205ca9d93e21fe6683b8.data",""):"011c90516755d702cfb4205ca9d93e21fe6683b8.data",c=e.remote_package_size,u=null,s=h.getPreloadedPackage?h.getPreloadedPackage(i,c):null;s||(n=i,r=c,a=function(e){u?(u(e),u=null):s=e},(o=new XMLHttpRequest).open("GET",n,!0),o.responseType="arraybuffer",o.onprogress=function(e){var t=r;if(e.total&&(t=e.total),e.loaded){o.dd?h.Nb[n].loaded=e.loaded:(o.dd=!0,h.Nb||(h.Nb={}),h.Nb[n]={loaded:e.loaded,total:t});var a,i=t=e=0;for(a in h.Nb){var c=h.Nb[a];e+=c.total,t+=c.loaded,i++}e=Math.ceil(e*h.lc/i),h.setStatus&&h.setStatus(`Downloading data... (${t}/${e})`)}else!h.Nb&&h.setStatus&&h.setStatus("Downloading data...")},o.onerror=function(){throw Error("NetworkError for: "+n)},o.onload=function(){if(!(200==o.status||304==o.status||206==o.status||0==o.status&&o.response))throw Error(o.statusText+" : "+o.responseURL);a(o.response)},o.send(null)),h.calledRun?t():(h.preRun||(h.preRun=[]),h.preRun.push(t))}({files:[{filename:"/models/1f5b84f51ce0fcfbb76e904b7bcaa7560f601e1394a0b29367a09385312287eb",start:0,end:11840},{filename:"/models/2b075ac1a6132b5b8a4c9ef0ba6b0cd84db7838aca9a000e50d907f40770a4ab",start:11840,end:23824},{filename:"/models/4c4774668f9b9333977fc891e7695420a0b4c27cc2c1cd3920ce9e0870e3c0e8",start:23824,end:76848},{filename:"/models/59cc2a9af81aaca2376702c2490650f4da2775fa673274db98aad41b7ef101c0",start:76848,end:129456},{filename:"/models/5b63e98b991aedabb60665503384f30bffd939decf9433883b30b78011ee501a",start:129456,end:182912},{filename:"/models/66388dc76dc16bc6b76b682edd218a575bf45b9b",start:182912,end:231504},{filename:"/models/6b3133f0f39ff89a2a169d61176ee17cafacc5e288f334e2b64ee82892d11ccd",start:231504,end:1731936},{filename:"/models/9077d16225f9314163ef1e7db6fc7d4088bb903d134bd95f23d5591ca4dfbfca",start:1731936,end:1785008},{filename:"/models/a74f2afb9d20f2375ccbd14e67c094b85c89ceb608f7cf8ae04f3f646a6c5672",start:1785008,end:1839904},{filename:"/models/b501893e75f62ee1707643e35b21109927b07ed5b202321c961b424cbc2e4695",start:1839904,end:1892832},{filename:"/models/dbd7a353f0130bb983d6ba05917e9be991d70e8f028df4b74e30bc6497ef7f71",start:1892832,end:1945712},{filename:"/models/f2.xml",start:1945712,end:2073232},{filename:"/models/fd6d368a5658496536e2bfae170d1b823a3629b242cafc09784bfba4e56d8c80",start:2073232,end:2082160},{filename:"/models/vu0ilin6we3lrzo5f83f7qs2jul4aq7v4aoynrmch8zfvpi8ezrfyafa4t0fx87l",start:2082160,end:2091664}],remote_package_size:2091664}),crypto||(crypto={getRandomValues:function(e){for(var t=0;t<e.length;t++)e[t]=h.eJcfPvLCbm.apply(null)}});var ba=Object.assign({},h),ca=[],da="./this.program",ea=(e,t)=>{throw t},fa="object"==typeof window,ha="function"==typeof importScripts,ia="",ja,ka,ma;(fa||ha)&&(ha?ia=self.location.href:"undefined"!=typeof document&&document.currentScript&&(ia=document.currentScript.src),ia=0!==ia.indexOf("blob:")?ia.substr(0,ia.replace(/[?#].*/,"").lastIndexOf("/")+1):"",ja=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},ha&&(ma=e=>{var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),ka=(e,t,n)=>{var r=new XMLHttpRequest;r.open("GET",e,!0),r.responseType="arraybuffer",r.onload=()=>{200==r.status||0==r.status&&r.response?t(r.response):n()},r.onerror=n,r.send(null)});var na=h.print||console.log.bind(console),aa=h.printErr||console.error.bind(console),oa;Object.assign(h,ba),ba=null,h.arguments&&(ca=h.arguments),h.thisProgram&&(da=h.thisProgram),h.quit&&(ea=h.quit),h.wasmBinary&&(oa=h.wasmBinary);var noExitRuntime=h.noExitRuntime||!0;"object"!=typeof WebAssembly&&u("no native wasm support detected");var pa,qa=!1,w,x,y,ra,A,B,sa,ta;function ua(){var e=pa.buffer;h.HEAP8=w=new Int8Array(e),h.HEAP16=y=new Int16Array(e),h.HEAP32=A=new Int32Array(e),h.HEAPU8=x=new Uint8Array(e),h.HEAPU16=ra=new Uint16Array(e),h.HEAPU32=B=new Uint32Array(e),h.HEAPF32=sa=new Float32Array(e),h.HEAPF64=ta=new Float64Array(e)}var C,va=[],wa=[],xa=[],ya=[];function za(){var e=h.preRun.shift();va.unshift(e)}var Aa=0,Ba=null,Ca=null,Ga,Ma,Na;function Da(){Aa++,h.monitorRunDependencies&&h.monitorRunDependencies(Aa)}function Ea(){if(Aa--,h.monitorRunDependencies&&h.monitorRunDependencies(Aa),0==Aa&&(null!==Ba&&(clearInterval(Ba),Ba=null),Ca)){var e=Ca;Ca=null,e()}}function u(e){throw h.onAbort&&h.onAbort(e),aa(e="Aborted("+e+")"),qa=!0,new WebAssembly.RuntimeError(e+". Build with -sASSERTIONS for more info.")}function Fa(e){return e.startsWith("data:application/octet-stream;base64,")}if(Ga="011c90516755d702cfb4205ca9d93e21fe6683b8.wasm",!Fa(Ga)){var Ha=Ga;Ga=h.locateFile?h.locateFile(Ha,ia):ia+Ha}function Ia(e){try{if(e==Ga&&oa)return new Uint8Array(oa);if(ma)return ma(e);throw"both async and sync fetching of the wasm failed"}catch(e){u(e)}}function Ja(e){return oa||!fa&&!ha||"function"!=typeof fetch?Promise.resolve().then((()=>Ia(e))):fetch(e,{credentials:"same-origin"}).then((t=>{if(!t.ok)throw"failed to load wasm binary file at '"+e+"'";return t.arrayBuffer()})).catch((()=>Ia(e)))}function Ka(e,t,n){return Ja(e).then((e=>WebAssembly.instantiate(e,t))).then((e=>e)).then(n,(e=>{aa("failed to asynchronously prepare wasm: "+e),u(e)}))}function La(e,t){var n=Ga;oa||"function"!=typeof WebAssembly.instantiateStreaming||Fa(n)||"function"!=typeof fetch?Ka(n,e,t):fetch(n,{credentials:"same-origin"}).then((r=>WebAssembly.instantiateStreaming(r,e).then(t,(function(r){return aa("wasm streaming compile failed: "+r),aa("falling back to ArrayBuffer instantiation"),Ka(n,e,t)}))))}function Oa(e){this.name="ExitStatus",this.message=`Program terminated with exit(${e})`,this.status=e}var Pa=e=>{for(;0<e.length;)e.shift()(h)},Qa=[],Ra=0,D=0;function Sa(e){this.Rb=e,this.mb=e-24,this.Id=function(e){B[this.mb+4>>2]=e},this.Gc=function(){return B[this.mb+4>>2]},this.Hd=function(e){B[this.mb+8>>2]=e},this.Tc=function(e){w[this.mb+12|0]=e?1:0},this.od=function(){return 0!=w[this.mb+12|0]},this.Uc=function(e){w[this.mb+13|0]=e?1:0},this.qd=function(){return 0!=w[this.mb+13|0]},this.Hc=function(e,t){this.Sc(0),this.Id(e),this.Hd(t)},this.Sc=function(e){B[this.mb+16>>2]=e},this.nd=function(){return B[this.mb+16>>2]},this.pd=function(){if(Ta(this.Gc()))return B[this.Rb>>2];var e=this.nd();return 0!==e?e:this.Rb}}function Ua(){var e=D;if(!e)return Va(0),0;var t=new Sa(e);t.Sc(e);var n=t.Gc();if(!n)return Va(0),e;for(var r=0;r<arguments.length;r++){var a=arguments[r];if(0===a||a===n)break;if(Wa(a,n,t.mb+16))return Va(a),e}return Va(n),e}var Xa=(e,t)=>{for(var n=0,r=e.length-1;0<=r;r--){var a=e[r];"."===a?e.splice(r,1):".."===a?(e.splice(r,1),n++):n&&(e.splice(r,1),n--)}if(t)for(;n;n--)e.unshift("..");return e},E=e=>{var t="/"===e.charAt(0),n="/"===e.substr(-1);return(e=Xa(e.split("/").filter((e=>!!e)),!t).join("/"))||t||(e="."),e&&n&&(e+="/"),(t?"/":"")+e},Ya=e=>{var t=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(e).slice(1);return e=t[0],t=t[1],e||t?(t&&(t=t.substr(0,t.length-1)),e+t):"."},Za=e=>{if("/"===e)return"/";var t=(e=(e=E(e)).replace(/\/$/,"")).lastIndexOf("/");return-1===t?e:e.substr(t+1)},$a=()=>{if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return e=>crypto.getRandomValues(e);u("initRandomDevice")},ab=e=>(ab=$a())(e);function bb(){for(var e="",t=!1,n=arguments.length-1;-1<=n&&!t;n--){if("string"!=typeof(t=0<=n?arguments[n]:"/"))throw new TypeError("Arguments to path.resolve must be strings");if(!t)return"";e=t+"/"+e,t="/"===t.charAt(0)}return(t?"/":"")+(e=Xa(e.split("/").filter((e=>!!e)),!t).join("/"))||"."}var cb=e=>{for(var t=0,n=0;n<e.length;++n){var r=e.charCodeAt(n);127>=r?t++:2047>=r?t+=2:55296<=r&&57343>=r?(t+=4,++n):t+=3}return t},db=(e,t,n,r)=>{if(!(0<r))return 0;var a=n;r=n+r-1;for(var o=0;o<e.length;++o){var i=e.charCodeAt(o);if(55296<=i&&57343>=i&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++o)),127>=i){if(n>=r)break;t[n++]=i}else{if(2047>=i){if(n+1>=r)break;t[n++]=192|i>>6}else{if(65535>=i){if(n+2>=r)break;t[n++]=224|i>>12}else{if(n+3>=r)break;t[n++]=240|i>>18,t[n++]=128|i>>12&63}t[n++]=128|i>>6&63}t[n++]=128|63&i}}return t[n]=0,n-a};function eb(e,t){var n=Array(cb(e)+1);return e=db(e,n,0,n.length),t&&(n.length=e),n}var fb="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,G=(e,t,n)=>{var r=t+n;for(n=t;e[n]&&!(n>=r);)++n;if(16<n-t&&e.buffer&&fb)return fb.decode(e.subarray(t,n));for(r="";t<n;){var a=e[t++];if(128&a){var o=63&e[t++];if(192==(224&a))r+=String.fromCharCode((31&a)<<6|o);else{var i=63&e[t++];65536>(a=224==(240&a)?(15&a)<<12|o<<6|i:(7&a)<<18|o<<12|i<<6|63&e[t++])?r+=String.fromCharCode(a):(a-=65536,r+=String.fromCharCode(55296|a>>10,56320|1023&a))}}else r+=String.fromCharCode(a)}return r},gb=[];function hb(e,t){gb[e]={input:[],tb:[],Eb:t},ib(e,jb)}var jb={open:function(e){var t=gb[e.node.dc];if(!t)throw new I(43);e.pb=t,e.seekable=!1},close:function(e){e.pb.Eb.Zb(e.pb)},Zb:function(e){e.pb.Eb.Zb(e.pb)},read:function(e,t,n,r){if(!e.pb||!e.pb.Eb.Fc)throw new I(60);for(var a=0,o=0;o<r;o++){try{var i=e.pb.Eb.Fc(e.pb)}catch(e){throw new I(29)}if(void 0===i&&0===a)throw new I(6);if(null==i)break;a++,t[n+o]=i}return a&&(e.node.timestamp=Date.now()),a},write:function(e,t,n,r){if(!e.pb||!e.pb.Eb.rc)throw new I(60);try{for(var a=0;a<r;a++)e.pb.Eb.rc(e.pb,t[n+a])}catch(e){throw new I(29)}return r&&(e.node.timestamp=Date.now()),a}},kb={Fc:function(e){if(!e.input.length){var t=null;if("undefined"!=typeof window&&"function"==typeof window.prompt?null!==(t=window.prompt("Input: "))&&(t+="\n"):"function"==typeof readline&&null!==(t=readline())&&(t+="\n"),!t)return null;e.input=eb(t,!0)}return e.input.shift()},rc:function(e,t){null===t||10===t?(na(G(e.tb,0)),e.tb=[]):0!=t&&e.tb.push(t)},Zb:function(e){e.tb&&0<e.tb.length&&(na(G(e.tb,0)),e.tb=[])},vd:function(){return{he:25856,je:5,ge:191,ie:35387,fe:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},wd:function(){return 0},xd:function(){return[24,80]}},lb={rc:function(e,t){null===t||10===t?(aa(G(e.tb,0)),e.tb=[]):0!=t&&e.tb.push(t)},Zb:function(e){e.tb&&0<e.tb.length&&(aa(G(e.tb,0)),e.tb=[])}},J={Bb:null,Db:function(){return J.createNode(null,"/",16895,0)},createNode:function(e,t,n,r){if(24576==(61440&n)||4096==(61440&n))throw new I(63);return J.Bb||(J.Bb={dir:{node:{Hb:J.lb.Hb,wb:J.lb.wb,Sb:J.lb.Sb,cc:J.lb.cc,Pc:J.lb.Pc,kc:J.lb.kc,ec:J.lb.ec,Nc:J.lb.Nc,hc:J.lb.hc},stream:{Kb:J.nb.Kb}},file:{node:{Hb:J.lb.Hb,wb:J.lb.wb},stream:{Kb:J.nb.Kb,read:J.nb.read,write:J.nb.write,Wb:J.nb.Wb,qc:J.nb.qc,Kc:J.nb.Kc}},link:{node:{Hb:J.lb.Hb,wb:J.lb.wb,Ub:J.lb.Ub},stream:{}},yc:{node:{Hb:J.lb.Hb,wb:J.lb.wb},stream:mb}}),16384==(61440&(n=nb(e,t,n,r)).mode)?(n.lb=J.Bb.dir.node,n.nb=J.Bb.dir.stream,n.kb={}):32768==(61440&n.mode)?(n.lb=J.Bb.file.node,n.nb=J.Bb.file.stream,n.qb=0,n.kb=null):40960==(61440&n.mode)?(n.lb=J.Bb.link.node,n.nb=J.Bb.link.stream):8192==(61440&n.mode)&&(n.lb=J.Bb.yc.node,n.nb=J.Bb.yc.stream),n.timestamp=Date.now(),e&&(e.kb[t]=n,e.timestamp=n.timestamp),n},ne:function(e){return e.kb?e.kb.subarray?e.kb.subarray(0,e.qb):new Uint8Array(e.kb):new Uint8Array(0)},Bc:function(e,t){var n=e.kb?e.kb.length:0;n>=t||(t=Math.max(t,n*(1048576>n?2:1.125)>>>0),0!=n&&(t=Math.max(t,256)),n=e.kb,e.kb=new Uint8Array(t),0<e.qb&&e.kb.set(n.subarray(0,e.qb),0))},Ed:function(e,t){if(e.qb!=t)if(0==t)e.kb=null,e.qb=0;else{var n=e.kb;e.kb=new Uint8Array(t),n&&e.kb.set(n.subarray(0,Math.min(t,e.qb))),e.qb=t}},lb:{Hb:function(e){var t={};return t.le=8192==(61440&e.mode)?e.id:1,t.pe=e.id,t.mode=e.mode,t.se=1,t.uid=0,t.oe=0,t.dc=e.dc,t.size=16384==(61440&e.mode)?4096:32768==(61440&e.mode)?e.qb:40960==(61440&e.mode)?e.link.length:0,t.de=new Date(e.timestamp),t.re=new Date(e.timestamp),t.ke=new Date(e.timestamp),t.ed=4096,t.ee=Math.ceil(t.size/t.ed),t},wb:function(e,t){void 0!==t.mode&&(e.mode=t.mode),void 0!==t.timestamp&&(e.timestamp=t.timestamp),void 0!==t.size&&J.Ed(e,t.size)},Sb:function(){throw ob[44]},cc:function(e,t,n,r){return J.createNode(e,t,n,r)},Pc:function(e,t,n){if(16384==(61440&e.mode)){try{var r=pb(t,n)}catch(e){}if(r)for(var a in r.kb)throw new I(55)}delete e.parent.kb[e.name],e.parent.timestamp=Date.now(),e.name=n,t.kb[n]=e,t.timestamp=e.parent.timestamp,e.parent=t},kc:function(e,t){delete e.kb[t],e.timestamp=Date.now()},ec:function(e,t){var n,r=pb(e,t);for(n in r.kb)throw new I(55);delete e.kb[t],e.timestamp=Date.now()},Nc:function(e){var t,n=[".",".."];for(t in e.kb)e.kb.hasOwnProperty(t)&&n.push(t);return n},hc:function(e,t,n){return(e=J.createNode(e,t,41471,0)).link=n,e},Ub:function(e){if(40960!=(61440&e.mode))throw new I(28);return e.link}},nb:{read:function(e,t,n,r,a){var o=e.node.kb;if(a>=e.node.qb)return 0;if(8<(e=Math.min(e.node.qb-a,r))&&o.subarray)t.set(o.subarray(a,a+e),n);else for(r=0;r<e;r++)t[n+r]=o[a+r];return e},write:function(e,t,n,r,a,o){if(t.buffer===w.buffer&&(o=!1),!r)return 0;if((e=e.node).timestamp=Date.now(),t.subarray&&(!e.kb||e.kb.subarray)){if(o)return e.kb=t.subarray(n,n+r),e.qb=r;if(0===e.qb&&0===a)return e.kb=t.slice(n,n+r),e.qb=r;if(a+r<=e.qb)return e.kb.set(t.subarray(n,n+r),a),r}if(J.Bc(e,a+r),e.kb.subarray&&t.subarray)e.kb.set(t.subarray(n,n+r),a);else for(o=0;o<r;o++)e.kb[a+o]=t[n+o];return e.qb=Math.max(e.qb,a+r),r},Kb:function(e,t,n){if(1===n?t+=e.position:2===n&&32768==(61440&e.node.mode)&&(t+=e.node.qb),0>t)throw new I(28);return t},Wb:function(e,t,n){J.Bc(e.node,t+n),e.node.qb=Math.max(e.node.qb,t+n)},qc:function(e,t,n,r,a){if(32768!=(61440&e.node.mode))throw new I(43);if(e=e.node.kb,2&a||e.buffer!==w.buffer){if((0<n||n+t<e.length)&&(e=e.subarray?e.subarray(n,n+t):Array.prototype.slice.call(e,n,n+t)),n=!0,u(),!(t=void 0))throw new I(48);w.set(e,t)}else n=!1,t=e.byteOffset;return{mb:t,zb:n}},Kc:function(e,t,n,r){return J.nb.write(e,t,0,r,n,!1),0}}},qb=(e,t,n)=>{var r=`al ${e}`;ka(e,(n=>{n||u(`Loading data file "${e}" failed (no arrayBuffer).`),t(new Uint8Array(n)),r&&Ea(r)}),(()=>{if(!n)throw`Loading data file "${e}" failed.`;n()})),r&&Da(r)},rb=h.preloadPlugins||[];function sb(e,t,n,r){"undefined"!=typeof Browser&&Browser.Hc();var a=!1;return rb.forEach((function(o){!a&&o.canHandle(t)&&(o.handle(e,t,n,r),a=!0)})),a}function tb(e,t,n,r,a,o,i,c,u,s){function b(n){function b(n){s&&s(),c||ub(e,t,n,r,a,u),o&&o(),Ea(d)}sb(n,f,b,(()=>{i&&i(),Ea(d)}))||b(n)}var f=t?bb(E(e+"/"+t)):e,d=`cp ${f}`;Da(d),"string"==typeof n?qb(n,(e=>b(e)),i):b(n)}function vb(e,t){var n=0;return e&&(n|=365),t&&(n|=146),n}var wb=null,xb={},yb=[],zb=1,Ab=null,Bb=!0,I=null,ob={},L=(e,t={})=>{if(!(e=bb(e)))return{path:"",node:null};if(8<(t=Object.assign({Dc:!0,tc:0},t)).tc)throw new I(32);e=e.split("/").filter((e=>!!e));for(var n=wb,r="/",a=0;a<e.length;a++){var o=a===e.length-1;if(o&&t.parent)break;if(n=pb(n,e[a]),r=E(r+"/"+e[a]),n.Pb&&(!o||o&&t.Dc)&&(n=n.Pb.root),!o||t.mc)for(o=0;40960==(61440&n.mode);)if(n=Cb(r),r=bb(Ya(r),n),n=L(r,{tc:t.tc+1}).node,40<o++)throw new I(32)}return{path:r,node:n}},Db=e=>{for(var t;;){if(e===e.parent)return e=e.Db.Jc,t?"/"!==e[e.length-1]?`${e}/${t}`:e+t:e;t=t?`${e.name}/${t}`:e.name,e=e.parent}},Eb=(e,t)=>{for(var n=0,r=0;r<t.length;r++)n=(n<<5)-n+t.charCodeAt(r)|0;return(e+n>>>0)%Ab.length},Fb=e=>{var t=Eb(e.parent.id,e.name);if(Ab[t]===e)Ab[t]=e.Qb;else for(t=Ab[t];t;){if(t.Qb===e){t.Qb=e.Qb;break}t=t.Qb}},pb=(e,t)=>{var n;if(n=(n=Gb(e,"x"))?n:e.lb.Sb?0:2)throw new I(n,e);for(n=Ab[Eb(e.id,t)];n;n=n.Qb){var r=n.name;if(n.parent.id===e.id&&r===t)return n}return e.lb.Sb(e,t)},nb=(e,t,n,r)=>(e=new Hb(e,t,n,r),t=Eb(e.parent.id,e.name),e.Qb=Ab[t],Ab[t]=e),Ib=e=>{var t=["r","w","rw"][3&e];return 512&e&&(t+="w"),t},Gb=(e,t)=>Bb?0:!t.includes("r")||292&e.mode?t.includes("w")&&!(146&e.mode)||t.includes("x")&&!(73&e.mode)?2:0:2,Jb=(e,t)=>{try{return pb(e,t),20}catch(e){}return Gb(e,"wx")},Kb=(e,t,n)=>{try{var r=pb(e,t)}catch(e){return e.Ab}if(e=Gb(e,"wx"))return e;if(n){if(16384!=(61440&r.mode))return 54;if(r===r.parent||"/"===Db(r))return 10}else if(16384==(61440&r.mode))return 31;return 0},Lb=()=>{for(var e=0;4096>=e;e++)if(!yb[e])return e;throw new I(33)},Mb=e=>{if(!(e=yb[e]))throw new I(8);return e},Ob=(e,t=-1)=>(Nb||((Nb=function(){this.fc={}}).prototype={},Object.defineProperties(Nb.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},flags:{get:function(){return this.fc.flags},set:function(e){this.fc.flags=e}},position:{get:function(){return this.fc.position},set:function(e){this.fc.position=e}}})),e=Object.assign(new Nb,e),-1==t&&(t=Lb()),e.Gb=t,yb[t]=e),mb={open:e=>{e.nb=xb[e.node.dc].nb,e.nb.open&&e.nb.open(e)},Kb:()=>{throw new I(70)}},ib=(e,t)=>{xb[e]={nb:t}},Pb=(e,t)=>{var n="/"===t,r=!t;if(n&&wb)throw new I(10);if(!n&&!r){var a=L(t,{Dc:!1});if(t=a.path,(a=a.node).Pb)throw new I(10);if(16384!=(61440&a.mode))throw new I(54)}t={type:e,ue:{},Jc:t,Ad:[]},(e=e.Db(t)).Db=t,t.root=e,n?wb=e:a&&(a.Pb=t,a.Db&&a.Db.Ad.push(t))},M=(e,t,n)=>{var r=L(e,{parent:!0}).node;if(!(e=Za(e))||"."===e||".."===e)throw new I(28);var a=Jb(r,e);if(a)throw new I(a);if(!r.lb.cc)throw new I(63);return r.lb.cc(r,e,t,n)},Qb=(e,t,n)=>(void 0===n&&(n=t,t=438),M(e,8192|t,n)),Rb=(e,t)=>{if(!bb(e))throw new I(44);var n=L(t,{parent:!0}).node;if(!n)throw new I(44);t=Za(t);var r=Jb(n,t);if(r)throw new I(r);if(!n.lb.hc)throw new I(63);n.lb.hc(n,t,e)},Sb=e=>{var t=L(e,{parent:!0}).node;e=Za(e);var n=pb(t,e),r=Kb(t,e,!0);if(r)throw new I(r);if(!t.lb.ec)throw new I(63);if(n.Pb)throw new I(10);t.lb.ec(t,e),Fb(n)},Tb=e=>{var t=L(e,{parent:!0}).node;if(!t)throw new I(44);e=Za(e);var n=pb(t,e),r=Kb(t,e,!1);if(r)throw new I(r);if(!t.lb.kc)throw new I(63);if(n.Pb)throw new I(10);t.lb.kc(t,e),Fb(n)},Cb=e=>{if(!(e=L(e).node))throw new I(44);if(!e.lb.Ub)throw new I(28);return bb(Db(e.parent),e.lb.Ub(e))},Ub=(e,t)=>{if(!(e="string"==typeof e?L(e,{mc:!0}).node:e).lb.wb)throw new I(63);e.lb.wb(e,{mode:4095&t|-4096&e.mode,timestamp:Date.now()})},Wb=(e,t,n)=>{if(""===e)throw new I(44);if("string"==typeof t){var r={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[t];if(void 0===r)throw Error(`Unknown file open mode: ${t}`);t=r}if(n=64&t?4095&(void 0===n?438:n)|32768:0,"object"==typeof e)var a=e;else{e=E(e);try{a=L(e,{mc:!(131072&t)}).node}catch(e){}}if(r=!1,64&t)if(a){if(128&t)throw new I(20)}else a=M(e,n,0),r=!0;if(!a)throw new I(44);if(8192==(61440&a.mode)&&(t&=-513),65536&t&&16384!=(61440&a.mode))throw new I(54);if(!r&&(n=a?40960==(61440&a.mode)?32:16384==(61440&a.mode)&&("r"!==Ib(t)||512&t)?31:Gb(a,Ib(t)):44))throw new I(n);if(512&t&&!r){if(!(n="string"==typeof(n=a)?L(n,{mc:!0}).node:n).lb.wb)throw new I(63);if(16384==(61440&n.mode))throw new I(31);if(32768!=(61440&n.mode))throw new I(28);if(r=Gb(n,"w"))throw new I(r);n.lb.wb(n,{size:0,timestamp:Date.now()})}return t&=-131713,(a=Ob({node:a,path:Db(a),flags:t,seekable:!0,position:0,nb:a.nb,Rd:[],error:!1})).nb.open&&a.nb.open(a),!h.logReadFiles||1&t||(Vb||(Vb={}),e in Vb||(Vb[e]=1)),a},Xb=e=>{if(null===e.Gb)throw new I(8);e.nc&&(e.nc=null);try{e.nb.close&&e.nb.close(e)}catch(e){throw e}finally{yb[e.Gb]=null}e.Gb=null},Yb=(e,t,n)=>{if(null===e.Gb)throw new I(8);if(!e.seekable||!e.nb.Kb)throw new I(70);if(0!=n&&1!=n&&2!=n)throw new I(28);e.position=e.nb.Kb(e,t,n),e.Rd=[]},Zb=(e,t,n,r,a,o)=>{if(0>r||0>a)throw new I(28);if(null===e.Gb)throw new I(8);if(!(2097155&e.flags))throw new I(8);if(16384==(61440&e.node.mode))throw new I(31);if(!e.nb.write)throw new I(28);e.seekable&&1024&e.flags&&Yb(e,0,2);var i=void 0!==a;if(i){if(!e.seekable)throw new I(70)}else a=e.position;return t=e.nb.write(e,t,n,r,a,o),i||(e.position+=t),t},$b=()=>{I||((I=function(e,t){this.name="ErrnoError",this.node=t,this.Gd=function(e){this.Ab=e},this.Gd(e),this.message="FS error"}).prototype=Error(),I.prototype.constructor=I,[44].forEach((e=>{ob[e]=new I(e),ob[e].stack="<generic error, no stack>"})))},ac,bc=(e,t)=>{for(e="string"==typeof e?e:Db(e),t=t.split("/").reverse();t.length;){var n=t.pop();if(n){var r=E(e+"/"+n);try{M(r,16895,0)}catch(e){}e=r}}return r},cc=(e,t,n,r)=>(e=E(("string"==typeof e?e:Db(e))+"/"+t),n=vb(n,r),M(e,4095&(void 0!==n?n:438)|32768,0)),ub=(e,t,n,r,a,o)=>{var i=t;if(e&&(e="string"==typeof e?e:Db(e),i=t?E(e+"/"+t):e),e=vb(r,a),i=M(i,4095&(void 0!==e?e:438)|32768,0),n){if("string"==typeof n){for(t=Array(n.length),r=0,a=n.length;r<a;++r)t[r]=n.charCodeAt(r);n=t}Ub(i,146|e),t=Wb(i,577),Zb(t,n,0,n.length,0,o),Xb(t),Ub(i,e)}return i},N=(e,t,n,r)=>{e=E(("string"==typeof e?e:Db(e))+"/"+t),t=vb(!!n,!!r),N.Ic||(N.Ic=64);var a=N.Ic++<<8;return ib(a,{open:e=>{e.seekable=!1},close:()=>{r&&r.buffer&&r.buffer.length&&r(10)},read:(e,t,r,a)=>{for(var o=0,i=0;i<a;i++){try{var c=n()}catch(e){throw new I(29)}if(void 0===c&&0===o)throw new I(6);if(null==c)break;o++,t[r+i]=c}return o&&(e.node.timestamp=Date.now()),o},write:(e,t,n,a)=>{for(var o=0;o<a;o++)try{r(t[n+o])}catch(e){throw new I(29)}return a&&(e.node.timestamp=Date.now()),o}}),Qb(e,t,a)},dc=e=>{if(!(e.yd||e.zd||e.link||e.kb)){if("undefined"!=typeof XMLHttpRequest)throw Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");if(!ja)throw Error("Cannot load without read() or XMLHttpRequest.");try{e.kb=eb(ja(e.url),!0),e.qb=e.kb.length}catch(e){throw new I(29)}}},ec=(e,t,n,r,a)=>{function o(){this.pc=!1,this.Yb=[]}if(o.prototype.get=function(e){if(!(e>this.length-1||0>e)){var t=e%this.zc;return this.$b(e/this.zc|0)[t]}},o.prototype.Fd=function(e){this.$b=e},o.prototype.xc=function(){var e=new XMLHttpRequest;if(e.open("HEAD",n,!1),e.send(null),!(200<=e.status&&300>e.status||304===e.status))throw Error("Couldn't load "+n+". Status: "+e.status);var t,r=Number(e.getResponseHeader("Content-length")),a=(t=e.getResponseHeader("Accept-Ranges"))&&"bytes"===t;e=(t=e.getResponseHeader("Content-Encoding"))&&"gzip"===t;var o=1048576;a||(o=r);var i=this;i.Fd((e=>{var t=e*o,a=(e+1)*o-1;if(a=Math.min(a,r-1),void 0===i.Yb[e]){var c=i.Yb;if(t>a)throw Error("invalid range ("+t+", "+a+") or no bytes requested!");if(a>r-1)throw Error("only "+r+" bytes available! programmer error!");var u=new XMLHttpRequest;if(u.open("GET",n,!1),r!==o&&u.setRequestHeader("Range","bytes="+t+"-"+a),u.responseType="arraybuffer",u.overrideMimeType&&u.overrideMimeType("text/plain; charset=x-user-defined"),u.send(null),!(200<=u.status&&300>u.status||304===u.status))throw Error("Couldn't load "+n+". Status: "+u.status);t=void 0!==u.response?new Uint8Array(u.response||[]):eb(u.responseText||"",!0),c[e]=t}if(void 0===i.Yb[e])throw Error("doXHR failed!");return i.Yb[e]})),!e&&r||(o=r=1,o=r=this.$b(0).length,na("LazyFiles on gzip forces download of the whole file when length is accessed")),this.cd=r,this.bd=o,this.pc=!0},"undefined"!=typeof XMLHttpRequest){if(!ha)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var i=new o;Object.defineProperties(i,{length:{get:function(){return this.pc||this.xc(),this.cd}},zc:{get:function(){return this.pc||this.xc(),this.bd}}});var c=void 0}else c=n,i=void 0;var s=cc(e,t,r,a);i?s.kb=i:c&&(s.kb=null,s.url=c),Object.defineProperties(s,{qb:{get:function(){return this.kb.length}}});var b={};return Object.keys(s.nb).forEach((e=>{var t=s.nb[e];b[e]=function(){return dc(s),t.apply(null,arguments)}})),b.read=(e,t,n,r,a)=>{if(dc(s),a>=(e=e.node.kb).length)t=0;else{if(r=Math.min(e.length-a,r),e.slice)for(var o=0;o<r;o++)t[n+o]=e[a+o];else for(o=0;o<r;o++)t[n+o]=e.get(a+o);t=r}return t},b.qc=()=>{throw dc(s),u(),new I(48)},s.nb=b,s},O={},Nb,Vb;function fc(e,t){if("/"===t.charAt(0))return t;if(e=-100===e?"/":Mb(e).path,0==t.length)throw new I(44);return E(e+"/"+t)}var gc=void 0;function P(){return A[(gc+=4)-4>>2]}var hc={};function ic(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function jc(e){return this.fromWireType(A[e>>2])}var kc={},lc={},mc={};function nc(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=t&&57>=t?`_${e}`:e}function oc(e,t){return e=nc(e),{[e]:function(){return t.apply(this,arguments)}}[e]}function pc(e){var t=Error,n=oc(e,(function(t){this.name=e,this.message=t,void 0!==(t=Error(t).stack)&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))}));return n.prototype=Object.create(t.prototype),n.prototype.constructor=n,n.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`},n}var qc=void 0;function rc(e){throw new qc(e)}function sc(e,t,n){function r(t){(t=n(t)).length!==e.length&&rc("Mismatched type converter count");for(var r=0;r<e.length;++r)Q(e[r],t[r])}e.forEach((function(e){mc[e]=t}));var a=Array(t.length),o=[],i=0;t.forEach(((e,t)=>{lc.hasOwnProperty(e)?a[t]=lc[e]:(o.push(e),kc.hasOwnProperty(e)||(kc[e]=[]),kc[e].push((()=>{a[t]=lc[e],++i===o.length&&r(a)})))})),0===o.length&&r(a)}function tc(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${e}`)}}var uc=void 0;function R(e){for(var t="";x[e];)t+=uc[x[e++]];return t}var vc=void 0;function S(e){throw new vc(e)}function Q(e,t,n={}){if(!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var r=t.name;if(e||S(`type "${r}" must have a positive integer typeid pointer`),lc.hasOwnProperty(e)){if(n.td)return;S(`Cannot register type '${r}' twice`)}lc[e]=t,delete mc[e],kc.hasOwnProperty(e)&&(t=kc[e],delete kc[e],t.forEach((e=>e())))}function wc(e){S(e.jb.rb.ob.name+" instance already deleted")}var xc=!1;function yc(){}function zc(e){--e.count.value,0===e.count.value&&(e.vb?e.yb.Fb(e.vb):e.rb.ob.Fb(e.mb))}function Ac(e,t,n){return t===n?e:void 0===n.sb||null===(e=Ac(e,t,n.sb))?null:n.hd(e)}var Bc={},Cc=[];function Dc(){for(;Cc.length;){var e=Cc.pop();e.jb.Ob=!1,e.delete()}}var Ec=void 0,Fc={};function Gc(e,t){for(void 0===t&&S("ptr should not be undefined");e.sb;)t=e.Vb(t),e=e.sb;return Fc[t]}function Hc(e,t){return t.rb&&t.mb||rc("makeClassHandle requires ptr and ptrType"),!!t.yb!=!!t.vb&&rc("Both smartPtrType and smartPtr must be specified"),t.count={value:1},Ic(Object.create(e,{jb:{value:t}}))}function Ic(e){return"undefined"==typeof FinalizationRegistry?(Ic=e=>e,e):(xc=new FinalizationRegistry((e=>{zc(e.jb)})),yc=e=>{xc.unregister(e)},(Ic=e=>{var t=e.jb;return t.vb&&xc.register(e,{jb:t},e),e})(e))}function Jc(){}function Kc(e,t,n){if(void 0===e[t].ub){var r=e[t];e[t]=function(){return e[t].ub.hasOwnProperty(arguments.length)||S(`Function '${n}' called with an invalid number of arguments (${arguments.length}) - expects one of (${e[t].ub})!`),e[t].ub[arguments.length].apply(this,arguments)},e[t].ub=[],e[t].ub[r.Xb]=r}}function Lc(e,t,n){h.hasOwnProperty(e)?((void 0===n||void 0!==h[e].ub&&void 0!==h[e].ub[n])&&S(`Cannot register public name '${e}' twice`),Kc(h,e,e),h.hasOwnProperty(n)&&S(`Cannot register multiple overloads of a function with the same number of arguments (${n})!`),h[e].ub[n]=t):(h[e]=t,void 0!==n&&(h[e].te=n))}function Mc(e,t,n,r,a,o,i,c){this.name=e,this.constructor=t,this.Ib=n,this.Fb=r,this.sb=a,this.ld=o,this.Vb=i,this.hd=c,this.Cd=[]}function Nc(e,t,n){for(;t!==n;)t.Vb||S(`Expected null or instance of ${n.name}, got an instance of ${t.name}`),e=t.Vb(e),t=t.sb;return e}function Oc(e,t){return null===t?(this.oc&&S(`null is not a valid ${this.name}`),0):(t.jb||S(`Cannot pass "${Pc(t)}" as a ${this.name}`),t.jb.mb||S(`Cannot pass deleted object as a pointer of type ${this.name}`),Nc(t.jb.mb,t.jb.rb.ob,this.ob))}function Qc(e,t){if(null===t){if(this.oc&&S(`null is not a valid ${this.name}`),this.bc){var n=this.sc();return null!==e&&e.push(this.Fb,n),n}return 0}if(t.jb||S(`Cannot pass "${Pc(t)}" as a ${this.name}`),t.jb.mb||S(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.ac&&t.jb.rb.ac&&S(`Cannot convert argument of type ${t.jb.yb?t.jb.yb.name:t.jb.rb.name} to parameter type ${this.name}`),n=Nc(t.jb.mb,t.jb.rb.ob,this.ob),this.bc)switch(void 0===t.jb.vb&&S("Passing raw pointer to smart pointer is illegal"),this.Md){case 0:t.jb.yb===this?n=t.jb.vb:S(`Cannot convert argument of type ${t.jb.yb?t.jb.yb.name:t.jb.rb.name} to parameter type ${this.name}`);break;case 1:n=t.jb.vb;break;case 2:if(t.jb.yb===this)n=t.jb.vb;else{var r=t.clone();n=this.Dd(n,Rc((function(){r.delete()}))),null!==e&&e.push(this.Fb,n)}break;default:S("Unsupporting sharing policy")}return n}function Sc(e,t){return null===t?(this.oc&&S(`null is not a valid ${this.name}`),0):(t.jb||S(`Cannot pass "${Pc(t)}" as a ${this.name}`),t.jb.mb||S(`Cannot pass deleted object as a pointer of type ${this.name}`),t.jb.rb.ac&&S(`Cannot convert argument of type ${t.jb.rb.name} to parameter type ${this.name}`),Nc(t.jb.mb,t.jb.rb.ob,this.ob))}function Tc(e,t,n,r){this.name=e,this.ob=t,this.oc=n,this.ac=r,this.bc=!1,this.Fb=this.Dd=this.sc=this.Mc=this.Md=this.Bd=void 0,void 0!==t.sb?this.toWireType=Qc:(this.toWireType=r?Oc:Sc,this.xb=null)}function Uc(e,t,n){h.hasOwnProperty(e)||rc("Replacing nonexistant public symbol"),void 0!==h[e].ub&&void 0!==n?h[e].ub[n]=t:(h[e]=t,h[e].Xb=n)}var Vc=(e,t)=>{var n=[];return function(){if(n.length=0,Object.assign(n,arguments),e.includes("j")){var r=h["dynCall_"+e];r=n&&n.length?r.apply(null,[t].concat(n)):r.call(null,t)}else r=C.get(t).apply(null,n);return r}};function T(e,t){var n=(e=R(e)).includes("j")?Vc(e,t):C.get(t);return"function"!=typeof n&&S(`unknown function pointer with signature ${e}: ${t}`),n}var Wc=void 0;function Xc(e){var t=R(e=Yc(e));return U(e),t}function Zc(e,t){var n=[],r={};throw t.forEach((function e(t){r[t]||lc[t]||(mc[t]?mc[t].forEach(e):(n.push(t),r[t]=!0))})),new Wc(`${e}: `+n.map(Xc).join([", "]))}function $c(e,t){for(var n=[],r=0;r<e;r++)n.push(B[t+4*r>>2]);return n}function ad(e){var t=Function;if(!(t instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof t} which is not a function`);var n=oc(t.name||"unknownFunctionName",(function(){}));return n.prototype=t.prototype,n=new n,(e=t.apply(n,e))instanceof Object?e:n}function bd(e,t,n,r,a,o){var i=t.length;2>i&&S("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var c=null!==t[1]&&null!==n,u=!1,s=1;s<t.length;++s)if(null!==t[s]&&void 0===t[s].xb){u=!0;break}var b="void"!==t[0].name,f="",d="";for(s=0;s<i-2;++s)f+=(0!==s?", ":"")+"arg"+s,d+=(0!==s?", ":"")+"arg"+s+"Wired";f=`\n        return function ${nc(e)}(${f}) {\n        if (arguments.length !== ${i-2}) {\n          throwBindingError('function ${e} called with ${arguments.length} arguments, expected ${i-2} args!');\n        }`,u&&(f+="var destructors = [];\n");var h=u?"destructors":"null",l="throwBindingError invoker fn runDestructors retType classParam".split(" "),p=[S,r,a,ic,t[0],t[1]];for(c&&(f+="var thisWired = classParam.toWireType("+h+", this);\n"),s=0;s<i-2;++s)f+="var arg"+s+"Wired = argType"+s+".toWireType("+h+", arg"+s+"); // "+t[s+2].name+"\n",l.push("argType"+s),p.push(t[s+2]);if(c&&(d="thisWired"+(0<d.length?", ":"")+d),f+=(b||o?"var rv = ":"")+"invoker(fn"+(0<d.length?", ":"")+d+");\n",u)f+="runDestructors(destructors);\n";else for(s=c?1:2;s<t.length;++s)i=1===s?"thisWired":"arg"+(s-2)+"Wired",null!==t[s].xb&&(f+=i+"_dtor("+i+"); // "+t[s].name+"\n",l.push(i+"_dtor"),p.push(t[s].xb));return b&&(f+="var ret = retType.fromWireType(rv);\nreturn ret;\n"),l.push(f+"}\n"),ad(l).apply(null,p)}function cd(e,t,n){return e instanceof Object||S(`${n} with invalid "this": ${e}`),e instanceof t.ob.constructor||S(`${n} incompatible with "this" of type ${e.constructor.name}`),e.jb.mb||S(`cannot call emscripten binding method ${n} on deleted object`),Nc(e.jb.mb,e.jb.rb.ob,t.ob)}var V=new function(){this.zb=[void 0],this.Ec=[],this.get=function(e){return this.zb[e]},this.has=function(e){return void 0!==this.zb[e]},this.Wb=function(e){var t=this.Ec.pop()||this.zb.length;return this.zb[t]=e,t},this.kd=function(e){this.zb[e]=void 0,this.Ec.push(e)}};function dd(e){e>=V.Qc&&0==--V.get(e).Oc&&V.kd(e)}var ed=e=>(e||S("Cannot use deleted val. handle = "+e),V.get(e).value),Rc=e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return V.Wb({Oc:1,value:e})}};function fd(e,t,n){switch(t){case 0:return function(e){return this.fromWireType((n?w:x)[e])};case 1:return function(e){return this.fromWireType((n?y:ra)[e>>1])};case 2:return function(e){return this.fromWireType((n?A:B)[e>>2])};default:throw new TypeError("Unknown integer type: "+e)}}function gd(e,t){var n=lc[e];return void 0===n&&S(t+" has unknown type "+Xc(e)),n}function Pc(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function hd(e,t){switch(t){case 2:return function(e){return this.fromWireType(sa[e>>2])};case 3:return function(e){return this.fromWireType(ta[e>>3])};default:throw new TypeError("Unknown float type: "+e)}}function jd(e,t,n){switch(t){case 0:return n?function(e){return w[e]}:function(e){return x[e]};case 1:return n?function(e){return y[e>>1]}:function(e){return ra[e>>1]};case 2:return n?function(e){return A[e>>2]}:function(e){return B[e>>2]};default:throw new TypeError("Unknown integer type: "+e)}}var kd="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,ld=(e,t)=>{for(var n=e>>1,r=n+t/2;!(n>=r)&&ra[n];)++n;if(32<(n<<=1)-e&&kd)return kd.decode(x.subarray(e,n));for(n="",r=0;!(r>=t/2);++r){var a=y[e+2*r>>1];if(0==a)break;n+=String.fromCharCode(a)}return n},md=(e,t,n)=>{if(void 0===n&&(n=2147483647),2>n)return 0;var r=t;n=(n-=2)<2*e.length?n/2:e.length;for(var a=0;a<n;++a)y[t>>1]=e.charCodeAt(a),t+=2;return y[t>>1]=0,t-r},nd=e=>2*e.length,od=(e,t)=>{for(var n=0,r="";!(n>=t/4);){var a=A[e+4*n>>2];if(0==a)break;++n,65536<=a?(a-=65536,r+=String.fromCharCode(55296|a>>10,56320|1023&a)):r+=String.fromCharCode(a)}return r},pd=(e,t,n)=>{if(void 0===n&&(n=2147483647),4>n)return 0;var r=t;n=r+n-4;for(var a=0;a<e.length;++a){var o=e.charCodeAt(a);if(55296<=o&&57343>=o&&(o=65536+((1023&o)<<10)|1023&e.charCodeAt(++a)),A[t>>2]=o,(t+=4)+4>n)break}return A[t>>2]=0,t-r},qd=e=>{for(var t=0,n=0;n<e.length;++n){var r=e.charCodeAt(n);55296<=r&&57343>=r&&++n,t+=4}return t},rd=e=>0==e%4&&(0!=e%100||0==e%400),sd=[0,31,60,91,121,152,182,213,244,274,305,335],td=[0,31,59,90,120,151,181,212,243,273,304,334],vd=e=>{var t=cb(e)+1,n=ud(t);return n&&db(e,x,n,t),n},W=a=>{if(a=eval(a?G(x,a):""),null==a)return 0;a+="";var b=cb(a);return(!W.bufferSize||W.bufferSize<b+1)&&(W.bufferSize&&U(W.buffer),W.bufferSize=b+1,W.buffer=ud(W.bufferSize)),db(a,x,W.buffer,W.bufferSize),W.buffer},wd={},yd=()=>{if(!xd){var e,t={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:da||"./this.program"};for(e in wd)void 0===wd[e]?delete t[e]:t[e]=wd[e];var n=[];for(e in t)n.push(`${e}=${t[e]}`);xd=n}return xd},xd,zd=e=>{noExitRuntime||(h.onExit&&h.onExit(e),qa=!0),ea(e,new Oa(e))},Ad=(e,t)=>{for(var n=0,r=0;r<=t;n+=e[r++]);return n},Bd=[31,29,31,30,31,30,31,31,30,31,30,31],Cd=[31,28,31,30,31,30,31,31,30,31,30,31],Dd=(e,t)=>{for(e=new Date(e.getTime());0<t;){var n=e.getMonth(),r=(rd(e.getFullYear())?Bd:Cd)[n];if(!(t>r-e.getDate())){e.setDate(e.getDate()+t);break}t-=r-e.getDate()+1,e.setDate(1),11>n?e.setMonth(n+1):(e.setMonth(0),e.setFullYear(e.getFullYear()+1))}return e},Ed=(e,t,n,r)=>{function a(e,t,n){for(e="number"==typeof e?e.toString():e||"";e.length<t;)e=n[0]+e;return e}function o(e,t){return a(e,t,"0")}function i(e,t){function n(e){return 0>e?-1:0<e?1:0}var r;return 0===(r=n(e.getFullYear()-t.getFullYear()))&&0===(r=n(e.getMonth()-t.getMonth()))&&(r=n(e.getDate()-t.getDate())),r}function c(e){switch(e.getDay()){case 0:return new Date(e.getFullYear()-1,11,29);case 1:return e;case 2:return new Date(e.getFullYear(),0,3);case 3:return new Date(e.getFullYear(),0,2);case 4:return new Date(e.getFullYear(),0,1);case 5:return new Date(e.getFullYear()-1,11,31);case 6:return new Date(e.getFullYear()-1,11,30)}}function u(e){e=Dd(new Date(e.Mb+1900,0,1),e.Lb);var t=new Date(e.getFullYear()+1,0,4),n=c(new Date(e.getFullYear(),0,4));return t=c(t),0>=i(n,e)?0>=i(t,e)?e.getFullYear()+1:e.getFullYear():e.getFullYear()-1}var s=A[r+40>>2];for(var b in r={Pd:A[r>>2],Od:A[r+4>>2],ic:A[r+8>>2],vc:A[r+12>>2],jc:A[r+16>>2],Mb:A[r+20>>2],Cb:A[r+24>>2],Lb:A[r+28>>2],ve:A[r+32>>2],Nd:A[r+36>>2],Qd:s&&s?G(x,s):""},n=n?G(x,n):"",s={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"})n=n.replace(new RegExp(b,"g"),s[b]);var f="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),d="January February March April May June July August September October November December".split(" ");for(b in s={"%a":e=>f[e.Cb].substring(0,3),"%A":e=>f[e.Cb],"%b":e=>d[e.jc].substring(0,3),"%B":e=>d[e.jc],"%C":e=>o((e.Mb+1900)/100|0,2),"%d":e=>o(e.vc,2),"%e":e=>a(e.vc,2," "),"%g":e=>u(e).toString().substring(2),"%G":e=>u(e),"%H":e=>o(e.ic,2),"%I":e=>(0==(e=e.ic)?e=12:12<e&&(e-=12),o(e,2)),"%j":e=>o(e.vc+Ad(rd(e.Mb+1900)?Bd:Cd,e.jc-1),3),"%m":e=>o(e.jc+1,2),"%M":e=>o(e.Od,2),"%n":()=>"\n","%p":e=>0<=e.ic&&12>e.ic?"AM":"PM","%S":e=>o(e.Pd,2),"%t":()=>"\t","%u":e=>e.Cb||7,"%U":e=>o(Math.floor((e.Lb+7-e.Cb)/7),2),"%V":e=>{var t=Math.floor((e.Lb+7-(e.Cb+6)%7)/7);if(2>=(e.Cb+371-e.Lb-2)%7&&t++,t)53==t&&(4==(n=(e.Cb+371-e.Lb)%7)||3==n&&rd(e.Mb)||(t=1));else{t=52;var n=(e.Cb+7-e.Lb-1)%7;(4==n||5==n&&rd(e.Mb%400-1))&&t++}return o(t,2)},"%w":e=>e.Cb,"%W":e=>o(Math.floor((e.Lb+7-(e.Cb+6)%7)/7),2),"%y":e=>(e.Mb+1900).toString().substring(2),"%Y":e=>e.Mb+1900,"%z":e=>{var t=0<=(e=e.Nd);return e=Math.abs(e)/60,(t?"+":"-")+String("0000"+(e/60*100+e%60)).slice(-4)},"%Z":e=>e.Qd,"%%":()=>"%"},n=n.replace(/%%/g,"\0\0"),s)n.includes(b)&&(n=n.replace(new RegExp(b,"g"),s[b](r)));return(b=eb(n=n.replace(/\0\0/g,"%"),!1)).length>t?0:(w.set(b,e),b.length-1)};function Hb(e,t,n,r){e||(e=this),this.parent=e,this.Db=e.Db,this.Pb=null,this.id=zb++,this.name=t,this.mode=n,this.lb={},this.nb={},this.dc=r}Object.defineProperties(Hb.prototype,{read:{get:function(){return!(365&~this.mode)},set:function(e){e?this.mode|=365:this.mode&=-366}},write:{get:function(){return!(146&~this.mode)},set:function(e){e?this.mode|=146:this.mode&=-147}},zd:{get:function(){return 16384==(61440&this.mode)}},yd:{get:function(){return 8192==(61440&this.mode)}}}),$b(),Ab=Array(4096),Pb(J,"/"),M("/tmp",16895,0),M("/home",16895,0),M("/home/<USER>",16895,0),(()=>{M("/dev",16895,0),ib(259,{read:()=>0,write:(e,t,n,r)=>r}),Qb("/dev/null",259),hb(1280,kb),hb(1536,lb),Qb("/dev/tty",1280),Qb("/dev/tty1",1536);var e=new Uint8Array(1024),t=0,n=()=>(0===t&&(t=ab(e).byteLength),e[--t]);N("/dev","random",n),N("/dev","urandom",n),M("/dev/shm",16895,0),M("/dev/shm/tmp",16895,0)})(),(()=>{M("/proc",16895,0);var e=M("/proc/self",16895,0);M("/proc/self/fd",16895,0),Pb({Db:()=>{var t=nb(e,"fd",16895,73);return t.lb={Sb:(e,t)=>{var n=Mb(+t);return(e={parent:null,Db:{Jc:"fake"},lb:{Ub:()=>n.path}}).parent=e}},t}},"/proc/self/fd")})(),h.FS_createPath=bc,h.FS_createDataFile=ub,h.FS_createPreloadedFile=tb,h.FS_unlink=Tb,h.FS_createLazyFile=ec,h.FS_createDevice=N,qc=h.InternalError=pc("InternalError");for(var Fd=Array(256),Gd=0;256>Gd;++Gd)Fd[Gd]=String.fromCharCode(Gd);uc=Fd,vc=h.BindingError=pc("BindingError"),Jc.prototype.isAliasOf=function(e){if(!(this instanceof Jc&&e instanceof Jc))return!1;var t=this.jb.rb.ob,n=this.jb.mb,r=e.jb.rb.ob;for(e=e.jb.mb;t.sb;)n=t.Vb(n),t=t.sb;for(;r.sb;)e=r.Vb(e),r=r.sb;return t===r&&n===e},Jc.prototype.clone=function(){if(this.jb.mb||wc(this),this.jb.Tb)return this.jb.count.value+=1,this;var e=Ic,t=Object,n=t.create,r=Object.getPrototypeOf(this),a=this.jb;return(e=e(n.call(t,r,{jb:{value:{count:a.count,Ob:a.Ob,Tb:a.Tb,mb:a.mb,rb:a.rb,vb:a.vb,yb:a.yb}}}))).jb.count.value+=1,e.jb.Ob=!1,e},Jc.prototype.delete=function(){this.jb.mb||wc(this),this.jb.Ob&&!this.jb.Tb&&S("Object already scheduled for deletion"),yc(this),zc(this.jb),this.jb.Tb||(this.jb.vb=void 0,this.jb.mb=void 0)},Jc.prototype.isDeleted=function(){return!this.jb.mb},Jc.prototype.deleteLater=function(){return this.jb.mb||wc(this),this.jb.Ob&&!this.jb.Tb&&S("Object already scheduled for deletion"),Cc.push(this),1===Cc.length&&Ec&&Ec(Dc),this.jb.Ob=!0,this},h.getInheritedInstanceCount=function(){return Object.keys(Fc).length},h.getLiveInheritedInstances=function(){var e,t=[];for(e in Fc)Fc.hasOwnProperty(e)&&t.push(Fc[e]);return t},h.flushPendingDeletes=Dc,h.setDelayFunction=function(e){Ec=e,Cc.length&&Ec&&Ec(Dc)},Tc.prototype.md=function(e){return this.Mc&&(e=this.Mc(e)),e},Tc.prototype.Ac=function(e){this.Fb&&this.Fb(e)},Tc.prototype.argPackAdvance=8,Tc.prototype.readValueFromPointer=jc,Tc.prototype.deleteObject=function(e){null!==e&&e.delete()},Tc.prototype.fromWireType=function(e){function t(){return this.bc?Hc(this.ob.Ib,{rb:this.Bd,mb:n,yb:this,vb:e}):Hc(this.ob.Ib,{rb:this,mb:e})}var n=this.md(e);if(!n)return this.Ac(e),null;var r=Gc(this.ob,n);if(void 0!==r)return 0===r.jb.count.value?(r.jb.mb=n,r.jb.vb=e,r.clone()):(r=r.clone(),this.Ac(e),r);if(r=this.ob.ld(n),!(r=Bc[r]))return t.call(this);r=this.ac?r.gd:r.pointerType;var a=Ac(n,this.ob,r.ob);return null===a?t.call(this):this.bc?Hc(r.ob.Ib,{rb:r,mb:a,yb:this,vb:e}):Hc(r.ob.Ib,{rb:r,mb:a})},Wc=h.UnboundTypeError=pc("UnboundTypeError"),V.zb.push({value:void 0},{value:null},{value:!0},{value:!1}),V.Qc=V.zb.length,h.count_emval_handles=function(){for(var e=0,t=V.Qc;t<V.zb.length;++t)void 0!==V.zb[t]&&++e;return e};var oe={q:function(e){return(e=new Sa(e)).od()||(e.Tc(!0),Ra--),e.Uc(!1),Qa.push(e),Hd(e.Rb),e.pd()},t:function(){X(0),Id(Qa.pop().Rb),D=0},b:Ua,g:Ua,ba:function(){var e=Qa.pop();e||u("no exception to throw");var t=e.Rb;throw e.qd()||(Qa.push(e),e.Uc(!0),e.Tc(!1),Ra++),D=t},a:function(e,t,n){throw new Sa(e).Hc(t,n),Ra++,D=e},Ia:function(){return Ra},e:function(e){throw D||(D=e),D},aa:function(e,t,n){gc=n;try{var r=Mb(e);switch(t){case 0:var a=P();return 0>a?-28:Ob(r,a).Gb;case 1:case 2:case 6:case 7:return 0;case 3:return r.flags;case 4:return a=P(),r.flags|=a,0;case 5:return a=P(),y[a+0>>1]=2,0;case 16:case 8:default:return-28;case 9:return A[Jd()>>2]=28,-1}}catch(e){if(void 0===O||"ErrnoError"!==e.name)throw e;return-e.Ab}},Ha:function(e,t,n){gc=n;try{var r=Mb(e);switch(t){case 21509:case 21510:case 21511:case 21512:case 21524:case 21515:return r.pb?0:-59;case 21505:if(!r.pb)return-59;if(r.pb.Eb.vd){t=[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];var a=P();A[a>>2]=25856,A[a+4>>2]=5,A[a+8>>2]=191,A[a+12>>2]=35387;for(var o=0;32>o;o++)w[a+o+17|0]=t[o]||0}return 0;case 21506:case 21507:case 21508:if(!r.pb)return-59;if(r.pb.Eb.wd)for(a=P(),t=[],o=0;32>o;o++)t.push(w[a+o+17|0]);return 0;case 21519:return r.pb?(a=P(),A[a>>2]=0):-59;case 21520:return r.pb?-28:-59;case 21531:if(a=P(),!r.nb.ud)throw new I(59);return r.nb.ud(r,t,a);case 21523:return r.pb?(r.pb.Eb.xd&&(o=[24,80],a=P(),y[a>>1]=o[0],y[a+2>>1]=o[1]),0):-59;default:return-28}}catch(e){if(void 0===O||"ErrnoError"!==e.name)throw e;return-e.Ab}},$:function(e,t,n,r){gc=r;try{t=fc(e,t=t?G(x,t):"");var a=r?P():0;return Wb(t,n,a).Gb}catch(e){if(void 0===O||"ErrnoError"!==e.name)throw e;return-e.Ab}},Ga:function(e){try{return e=e?G(x,e):"",Sb(e),0}catch(e){if(void 0===O||"ErrnoError"!==e.name)throw e;return-e.Ab}},Fa:function(e,t,n){try{return t=fc(e,t=t?G(x,t):""),0===n?Tb(t):512===n?Sb(t):u("Invalid flags passed to unlinkat"),0}catch(e){if(void 0===O||"ErrnoError"!==e.name)throw e;return-e.Ab}},I:function(e){var t=hc[e];delete hc[e];var n=t.sc,r=t.Fb,a=t.Cc;sc([e],a.map((e=>e.sd)).concat(a.map((e=>e.Kd))),(e=>{var o={};return a.forEach(((t,n)=>{var r=e[n],i=t.$b,c=t.rd,u=e[n+a.length],s=t.Jd,b=t.Ld;o[t.jd]={read:e=>r.fromWireType(i(c,e)),write:(e,t)=>{var n=[];s(b,e,u.toWireType(n,t)),ic(n)}}})),[{name:t.name,fromWireType:function(e){var t,n={};for(t in o)n[t]=o[t].read(e);return r(e),n},toWireType:function(e,t){for(var a in o)if(!(a in t))throw new TypeError(`Missing field: "${a}"`);var i=n();for(a in o)o[a].write(i,t[a]);return null!==e&&e.push(r,i),i},argPackAdvance:8,readValueFromPointer:jc,xb:r}]}))},ia:function(){},Ca:function(e,t,n,r,a){var o=tc(n);Q(e,{name:t=R(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?r:a},argPackAdvance:8,readValueFromPointer:function(e){if(1===n)var r=w;else if(2===n)r=y;else{if(4!==n)throw new TypeError("Unknown boolean type size: "+t);r=A}return this.fromWireType(r[e>>o])},xb:null})},u:function(e,t,n,r,a,o,i,c,u,s,b,f,d){b=R(b),o=T(a,o),c&&(c=T(i,c)),s&&(s=T(u,s)),d=T(f,d);var h=nc(b);Lc(h,(function(){Zc(`Cannot construct ${b} due to unbound types`,[r])})),sc([e,t,n],r?[r]:[],(function(t){if(t=t[0],r)var n=t.ob,a=n.Ib;else a=Jc.prototype;t=oc(h,(function(){if(Object.getPrototypeOf(this)!==i)throw new vc("Use 'new' to construct "+b);if(void 0===u.Jb)throw new vc(b+" has no accessible constructor");var e=u.Jb[arguments.length];if(void 0===e)throw new vc(`Tried to invoke ctor of ${b} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(u.Jb).toString()}) parameters instead!`);return e.apply(this,arguments)}));var i=Object.create(a,{constructor:{value:t}});t.prototype=i;var u=new Mc(b,t,i,d,n,o,c,s);u.sb&&(void 0===u.sb.wc&&(u.sb.wc=[]),u.sb.wc.push(u)),n=new Tc(b,u,!0,!1),a=new Tc(b+"*",u,!1,!1);var f=new Tc(b+" const*",u,!1,!0);return Bc[e]={pointerType:a,gd:f},Uc(h,t),[n,a,f]}))},E:function(e,t,n,r,a,o){0<t||u();var i=$c(t,n);a=T(r,a),sc([],[e],(function(e){var n=`constructor ${(e=e[0]).name}`;if(void 0===e.ob.Jb&&(e.ob.Jb=[]),void 0!==e.ob.Jb[t-1])throw new vc(`Cannot register multiple constructors with identical number of parameters (${t-1}) for class '${e.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return e.ob.Jb[t-1]=()=>{Zc(`Cannot construct ${e.name} due to unbound types`,i)},sc([],i,(function(r){return r.splice(1,0,null),e.ob.Jb[t-1]=bd(n,r,null,a,o),[]})),[]}))},m:function(e,t,n,r,a,o,i,c,u){var s=$c(n,r);t=R(t),o=T(a,o),sc([],[e],(function(e){function r(){Zc(`Cannot call ${a} due to unbound types`,s)}var a=`${(e=e[0]).name}.${t}`;t.startsWith("@@")&&(t=Symbol[t.substring(2)]),c&&e.ob.Cd.push(t);var b=e.ob.Ib,f=b[t];return void 0===f||void 0===f.ub&&f.className!==e.name&&f.Xb===n-2?(r.Xb=n-2,r.className=e.name,b[t]=r):(Kc(b,t,a),b[t].ub[n-2]=r),sc([],s,(function(r){return r=bd(a,r,e,o,i,u),void 0===b[t].ub?(r.Xb=n-2,b[t]=r):b[t].ub[n-2]=r,[]})),[]}))},j:function(e,t,n,r,a,o,i,c,u,s){t=R(t),a=T(r,a),sc([],[e],(function(e){var r=`${(e=e[0]).name}.${t}`,b={get:function(){Zc(`Cannot access ${r} due to unbound types`,[n,i])},enumerable:!0,configurable:!0};return b.set=u?()=>{Zc(`Cannot access ${r} due to unbound types`,[n,i])}:()=>{S(r+" is a read-only property")},Object.defineProperty(e.ob.Ib,t,b),sc([],u?[n,i]:[n],(function(n){var i=n[0],b={get:function(){var t=cd(this,e,r+" getter");return i.fromWireType(a(o,t))},enumerable:!0};if(u){u=T(c,u);var f=n[1];b.set=function(t){var n=cd(this,e,r+" setter"),a=[];u(s,n,f.toWireType(a,t)),ic(a)}}return Object.defineProperty(e.ob.Ib,t,b),[]})),[]}))},Ba:function(e,t){Q(e,{name:t=R(t),fromWireType:function(e){var t=ed(e);return dd(e),t},toWireType:function(e,t){return Rc(t)},argPackAdvance:8,readValueFromPointer:jc,xb:null})},D:function(e,t,n,r){function a(){}n=tc(n),t=R(t),a.values={},Q(e,{name:t,constructor:a,fromWireType:function(e){return this.constructor.values[e]},toWireType:function(e,t){return t.value},argPackAdvance:8,readValueFromPointer:fd(t,n,r),xb:null}),Lc(t,a)},l:function(e,t,n){var r=gd(e,"enum");t=R(t),e=r.constructor,r=Object.create(r.constructor.prototype,{value:{value:n},constructor:{value:oc(`${r.name}_${t}`,(function(){}))}}),e.values[n]=r,e[t]=r},Z:function(e,t,n){n=tc(n),Q(e,{name:t=R(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:hd(t,n),xb:null})},p:function(e,t,n,r,a,o,i){var c=$c(t,n);e=R(e),a=T(r,a),Lc(e,(function(){Zc(`Cannot call ${e} due to unbound types`,c)}),t-1),sc([],c,(function(n){return Uc(e,bd(e,[n[0],null].concat(n.slice(1)),null,a,o,i),t-1),[]}))},v:function(e,t,n,r,a){t=R(t),-1===a&&(a=4294967295),a=tc(n);var o=e=>e;if(0===r){var i=32-8*n;o=e=>e<<i>>>i}n=t.includes("unsigned")?function(e,t){return t>>>0}:function(e,t){return t},Q(e,{name:t,fromWireType:o,toWireType:n,argPackAdvance:8,readValueFromPointer:jd(t,a,0!==r),xb:null})},s:function(e,t,n){function r(e){e>>=2;var t=B;return new a(t.buffer,t[e+1],t[e])}var a=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];Q(e,{name:n=R(n),fromWireType:r,argPackAdvance:8,readValueFromPointer:r},{td:!0})},Y:function(e,t){var n="std::string"===(t=R(t));Q(e,{name:t,fromWireType:function(e){var t=B[e>>2],r=e+4;if(n)for(var a=r,o=0;o<=t;++o){var i=r+o;if(o==t||0==x[i]){if(a=a?G(x,a,i-a):"",void 0===c)var c=a;else c+=String.fromCharCode(0),c+=a;a=i+1}}else{for(c=Array(t),o=0;o<t;++o)c[o]=String.fromCharCode(x[r+o]);c=c.join("")}return U(e),c},toWireType:function(e,t){t instanceof ArrayBuffer&&(t=new Uint8Array(t));var r="string"==typeof t;r||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||S("Cannot pass non-string to std::string");var a=n&&r?cb(t):t.length,o=ud(4+a+1),i=o+4;if(B[o>>2]=a,n&&r)db(t,x,i,a+1);else if(r)for(r=0;r<a;++r){var c=t.charCodeAt(r);255<c&&(U(i),S("String has UTF-16 code units that do not fit in 8 bits")),x[i+r]=c}else for(r=0;r<a;++r)x[i+r]=t[r];return null!==e&&e.push(U,o),o},argPackAdvance:8,readValueFromPointer:jc,xb:function(e){U(e)}})},Q:function(e,t,n){if(n=R(n),2===t)var r=ld,a=md,o=nd,i=()=>ra,c=1;else 4===t&&(r=od,a=pd,o=qd,i=()=>B,c=2);Q(e,{name:n,fromWireType:function(e){for(var n,a=B[e>>2],o=i(),u=e+4,s=0;s<=a;++s){var b=e+4+s*t;s!=a&&0!=o[b>>c]||(u=r(u,b-u),void 0===n?n=u:(n+=String.fromCharCode(0),n+=u),u=b+t)}return U(e),n},toWireType:function(e,r){"string"!=typeof r&&S(`Cannot pass non-string to C++ string type ${n}`);var i=o(r),u=ud(4+i+t);return B[u>>2]=i>>c,a(r,u+4,i+t),null!==e&&e.push(U,u),u},argPackAdvance:8,readValueFromPointer:jc,xb:function(e){U(e)}})},H:function(e,t,n,r,a,o){hc[e]={name:R(t),sc:T(n,r),Fb:T(a,o),Cc:[]}},C:function(e,t,n,r,a,o,i,c,u,s){hc[e].Cc.push({jd:R(t),sd:n,$b:T(r,a),rd:o,Kd:i,Jd:T(c,u),Ld:s})},Aa:function(e,t){Q(e,{qe:!0,name:t=R(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},za:()=>!0,ya:()=>{throw 1/0},B:dd,P:function(e){4<e&&(V.get(e).Oc+=1)},X:function(){return Rc([])},xa:function(e,t,n){e=ed(e),t=ed(t),n=ed(n),e[t]=n},G:function(e,t){return e=(e=gd(e,"_emval_take_value")).readValueFromPointer(t),Rc(e)},wa:e=>{var t=new Date(A[e+20>>2]+1900,A[e+16>>2],A[e+12>>2],A[e+8>>2],A[e+4>>2],A[e>>2],0),n=A[e+32>>2],r=t.getTimezoneOffset(),a=new Date(t.getFullYear(),6,1).getTimezoneOffset(),o=new Date(t.getFullYear(),0,1).getTimezoneOffset(),i=Math.min(o,a);return 0>n?A[e+32>>2]=Number(a!=o&&i==r):0<n!=(i==r)&&(a=Math.max(o,a),t.setTime(t.getTime()+6e4*((0<n?i:a)-r))),A[e+24>>2]=t.getDay(),A[e+28>>2]=(rd(t.getFullYear())?sd:td)[t.getMonth()]+t.getDate()-1|0,A[e>>2]=t.getSeconds(),A[e+4>>2]=t.getMinutes(),A[e+8>>2]=t.getHours(),A[e+12>>2]=t.getDate(),A[e+16>>2]=t.getMonth(),A[e+20>>2]=t.getYear(),t.getTime()/1e3|0},va:(e,t,n)=>{function r(e){return(e=e.toTimeString().match(/\(([A-Za-z ]+)\)$/))?e[1]:"GMT"}var a=(new Date).getFullYear(),o=new Date(a,0,1),i=new Date(a,6,1);a=o.getTimezoneOffset();var c=i.getTimezoneOffset();B[e>>2]=60*Math.max(a,c),A[t>>2]=Number(a!=c),e=r(o),t=r(i),e=vd(e),t=vd(t),c<a?(B[n>>2]=e,B[n+4>>2]=t):(B[n>>2]=t,B[n+4>>2]=e)},A:()=>{u("")},O:function(){return Date.now()},ua:()=>2147483648,W:()=>performance.now(),ta:e=>{var t=x.length;if(2147483648<(e>>>=0))return!1;for(var n=1;4>=n;n*=2){var r=t*(1+.2/n);r=Math.min(r,e+100663296);var a=Math;r=Math.max(e,r);e:{a=a.min.call(a,2147483648,r+(65536-r%65536)%65536)-pa.buffer.byteLength+65535>>>16;try{pa.grow(a),ua();var o=1;break e}catch(e){}o=void 0}if(o)return!0}return!1},sa:W,Ea:(e,t)=>{var n=0;return yd().forEach((function(r,a){var o=t+n;for(a=B[e+4*a>>2]=o,o=0;o<r.length;++o)w[0|a++]=r.charCodeAt(o);w[0|a]=0,n+=r.length+1})),0},Da:(e,t)=>{var n=yd();B[e>>2]=n.length;var r=0;return n.forEach((function(e){r+=e.length+1})),B[t>>2]=r,0},ra:zd,S:function(e){try{var t=Mb(e);return Xb(t),0}catch(e){if(void 0===O||"ErrnoError"!==e.name)throw e;return e.Ab}},_:function(e,t,n,r){try{e:{var a=Mb(e);e=t;for(var o,i=t=0;i<n;i++){var c=B[e>>2],u=B[e+4>>2];e+=8;var s=a,b=c,f=u,d=o,h=w;if(0>f||0>d)throw new I(28);if(null===s.Gb)throw new I(8);if(1==(2097155&s.flags))throw new I(8);if(16384==(61440&s.node.mode))throw new I(31);if(!s.nb.read)throw new I(28);var l=void 0!==d;if(l){if(!s.seekable)throw new I(70)}else d=s.position;var p=s.nb.read(s,h,b,f,d);l||(s.position+=p);var v=p;if(0>v){var m=-1;break e}if(t+=v,v<u)break;void 0!==o&&(o+=v)}m=t}return B[r>>2]=m,0}catch(e){if(void 0===O||"ErrnoError"!==e.name)throw e;return e.Ab}},ja:function(e,t,n,r,a){try{if(t=n+2097152>>>0<4194305-!!t?(t>>>0)+4294967296*n:NaN,isNaN(t))return 61;var o=Mb(e);return Yb(o,t,r),Na=[o.position>>>0,(Ma=o.position,1<=+Math.abs(Ma)?0<Ma?+Math.floor(Ma/4294967296)>>>0:~~+Math.ceil((Ma-+(~~Ma>>>0))/4294967296)>>>0:0)],A[a>>2]=Na[0],A[a+4>>2]=Na[1],o.nc&&0===t&&0===r&&(o.nc=null),0}catch(e){if(void 0===O||"ErrnoError"!==e.name)throw e;return e.Ab}},R:function(e,t,n,r){try{e:{var a=Mb(e);e=t;for(var o,i=t=0;i<n;i++){var c=B[e>>2],u=B[e+4>>2];e+=8;var s=Zb(a,w,c,u,o);if(0>s){var b=-1;break e}t+=s,void 0!==o&&(o+=s)}b=t}return B[r>>2]=b,0}catch(e){if(void 0===O||"ErrnoError"!==e.name)throw e;return e.Ab}},N:(e,t)=>(ab(x.subarray(e,e+t)),0),M:Kd,V:Ld,r:Md,c:Nd,f:Od,o:Pd,k:Qd,U:Rd,z:Sd,x:Td,T:Ud,qa:Vd,L:Wd,ha:Xd,ga:Yd,fa:Zd,h:$d,i:ae,d:be,pa:ce,oa:de,na:ee,n:fe,F:ge,K:he,ma:ie,w:je,la:ke,y:le,J:me,ea:ne,ka:function(){var e=location.hostname,t=cb(e)+1,n=ud(t);return db(e,x,n,t+1),n},da:(e,t,n,r)=>Ed(e,t,n,r),ca:(e,t,n)=>{for(var r=t?G(x,t):"",a=0;25>a;++a)r=r.replace(new RegExp("\\"+"\\!@#$^&*()+=-[]/{}|:<>?,."[a],"g"),"\\"+"\\!@#$^&*()+=-[]/{}|:<>?,."[a]);for(var o in t={"%A":"%a","%B":"%b","%c":"%a %b %d %H:%M:%S %Y","%D":"%m\\/%d\\/%y","%e":"%d","%F":"%Y-%m-%d","%h":"%b","%R":"%H\\:%M","%r":"%I\\:%M\\:%S\\s%p","%T":"%H\\:%M\\:%S","%x":"%m\\/%d\\/(?:%y|%Y)","%X":"%H\\:%M\\:%S"})r=r.replace(o,t[o]);a={"%a":"(?:Sun(?:day)?)|(?:Mon(?:day)?)|(?:Tue(?:sday)?)|(?:Wed(?:nesday)?)|(?:Thu(?:rsday)?)|(?:Fri(?:day)?)|(?:Sat(?:urday)?)","%b":"(?:Jan(?:uary)?)|(?:Feb(?:ruary)?)|(?:Mar(?:ch)?)|(?:Apr(?:il)?)|May|(?:Jun(?:e)?)|(?:Jul(?:y)?)|(?:Aug(?:ust)?)|(?:Sep(?:tember)?)|(?:Oct(?:ober)?)|(?:Nov(?:ember)?)|(?:Dec(?:ember)?)","%C":"\\d\\d","%d":"0[1-9]|[1-9](?!\\d)|1\\d|2\\d|30|31","%H":"\\d(?!\\d)|[0,1]\\d|20|21|22|23","%I":"\\d(?!\\d)|0\\d|10|11|12","%j":"00[1-9]|0?[1-9](?!\\d)|0?[1-9]\\d(?!\\d)|[1,2]\\d\\d|3[0-6]\\d","%m":"0[1-9]|[1-9](?!\\d)|10|11|12","%M":"0\\d|\\d(?!\\d)|[1-5]\\d","%n":"\\s","%p":"AM|am|PM|pm|A\\.M\\.|a\\.m\\.|P\\.M\\.|p\\.m\\.","%S":"0\\d|\\d(?!\\d)|[1-5]\\d|60","%U":"0\\d|\\d(?!\\d)|[1-4]\\d|50|51|52|53","%W":"0\\d|\\d(?!\\d)|[1-4]\\d|50|51|52|53","%w":"[0-6]","%y":"\\d\\d","%Y":"\\d\\d\\d\\d","%%":"%","%t":"\\s"};var i={Wd:0,Vd:1,Zd:2,Sd:3,$d:4,Yd:5,Xd:6,Td:7,ce:8,be:9,ae:10,Ud:11};for(var c in o={Yc:0,Wc:1,$c:2,ad:3,Zc:4,Vc:5,Xc:6},t={Wc:0,$c:1,ad:2,Zc:3,Vc:4,Xc:5,Yc:6},a)r=r.replace(c,"("+c+a[c]+")");var u=[];for(a=r.indexOf("%");0<=a;a=r.indexOf("%"))u.push(r[a+1]),r=r.replace(new RegExp("\\%"+r[a+1],"g"),"");var s=new RegExp("^"+r,"i").exec(e?G(x,e):"");if(s){if(c=function(){function e(e,t,n){return"number"!=typeof e||isNaN(e)?t:e>=t?e<=n?e:n:t}return{year:e(A[n+20>>2]+1900,1970,9999),month:e(A[n+16>>2],0,11),day:e(A[n+12>>2],1,31),hour:e(A[n+8>>2],0,23),min:e(A[n+4>>2],0,59),Rc:e(A[n>>2],0,59)}}(),(r=(a=e=>{if(0<=(e=u.indexOf(e)))return s[e+1]})("S"))&&(c.Rc=parseInt(r)),(r=a("M"))&&(c.min=parseInt(r)),r=a("H"))c.hour=parseInt(r);else if(r=a("I")){var b=parseInt(r);(r=a("p"))&&(b+="P"===r.toUpperCase()[0]?12:0),c.hour=b}if((r=a("Y"))?c.year=parseInt(r):(r=a("y"))&&(b=parseInt(r),b=(r=a("C"))?b+100*parseInt(r):b+(69>b?2e3:1900),c.year=b),(r=a("m"))?c.month=parseInt(r)-1:(r=a("b"))&&(c.month=i[r.substring(0,3).toUpperCase()]||0),r=a("d"))c.day=parseInt(r);else if(r=a("j"))for(o=parseInt(r),t=rd(c.year),i=0;12>i;++i)o<=(r=Ad(t?Bd:Cd,i-1))+(t?Bd:Cd)[i]&&(c.day=o-r);else(r=a("a"))&&(i=r.substring(0,3).toUpperCase(),(r=a("U"))?(o=o[i],t=parseInt(r),o=0===(i=new Date(c.year,0,1)).getDay()?Dd(i,o+7*(t-1)):Dd(i,7-i.getDay()+o+7*(t-1)),c.day=o.getDate(),c.month=o.getMonth()):(r=a("W"))&&(o=t[i],t=parseInt(r),o=1===(i=new Date(c.year,0,1)).getDay()?Dd(i,o+7*(t-1)):Dd(i,7-i.getDay()+1+o+7*(t-1)),c.day=o.getDate(),c.month=o.getMonth()));return c=new Date(c.year,c.month,c.day,c.hour,c.min,c.Rc,0),A[n>>2]=c.getSeconds(),A[n+4>>2]=c.getMinutes(),A[n+8>>2]=c.getHours(),A[n+12>>2]=c.getDate(),A[n+16>>2]=c.getMonth(),A[n+20>>2]=c.getFullYear()-1900,A[n+24>>2]=c.getDay(),A[n+28>>2]=Ad(rd(c.getFullYear())?Bd:Cd,c.getMonth()-1)+c.getDate()-1,A[n+32>>2]=0,e+eb(s[0]).length-1}return 0}};function Jd(){return(Jd=h.asm.Ma).apply(null,arguments)}function U(){return(U=h.asm.Na).apply(null,arguments)}function ud(){return(ud=h.asm.Oa).apply(null,arguments)}!function(){function e(e){return e=e.exports,h.asm=e,pa=h.asm.Ja,ua(),C=h.asm.La,wa.unshift(h.asm.Ka),Ea("wasm-instantiate"),e}var t={a:oe};if(Da("wasm-instantiate"),h.instantiateWasm)try{return h.instantiateWasm(t,e)}catch(e){return aa("Module.instantiateWasm callback failed with error: "+e),!1}La(t,(function(t){e(t.instance)}))}();var pe=h._main=function(){return(pe=h._main=h.asm.Pa).apply(null,arguments)};function Va(){return(Va=h.asm.Qa).apply(null,arguments)}function Yc(){return(Yc=h.asm.Ra).apply(null,arguments)}function X(){return(X=h.asm.Ta).apply(null,arguments)}function Y(){return(Y=h.asm.Ua).apply(null,arguments)}function Z(){return(Z=h.asm.Va).apply(null,arguments)}function qe(){return(qe=h.asm.Wa).apply(null,arguments)}function Id(){return(Id=h.asm.Xa).apply(null,arguments)}function Hd(){return(Hd=h.asm.Ya).apply(null,arguments)}function Wa(){return(Wa=h.asm.Za).apply(null,arguments)}function Ta(){return(Ta=h.asm._a).apply(null,arguments)}h.__embind_initialize_bindings=function(){return(h.__embind_initialize_bindings=h.asm.Sa).apply(null,arguments)},h.dynCall_ji=function(){return(h.dynCall_ji=h.asm.$a).apply(null,arguments)};var re=h.dynCall_viijii=function(){return(re=h.dynCall_viijii=h.asm.ab).apply(null,arguments)};h.dynCall_jiji=function(){return(h.dynCall_jiji=h.asm.bb).apply(null,arguments)};var se=h.dynCall_j=function(){return(se=h.dynCall_j=h.asm.cb).apply(null,arguments)},te=h.dynCall_iiiiij=function(){return(te=h.dynCall_iiiiij=h.asm.db).apply(null,arguments)},ue=h.dynCall_jiiii=function(){return(ue=h.dynCall_jiiii=h.asm.eb).apply(null,arguments)},ve;function ae(e,t){var n=Y();try{C.get(e)(t)}catch(e){if(Z(n),e!==e+0)throw e;X(1,0)}}function Nd(e,t){var n=Y();try{return C.get(e)(t)}catch(e){if(Z(n),e!==e+0)throw e;X(1,0)}}function fe(e,t,n,r){var a=Y();try{C.get(e)(t,n,r)}catch(e){if(Z(a),e!==e+0)throw e;X(1,0)}}function Od(e,t,n){var r=Y();try{return C.get(e)(t,n)}catch(e){if(Z(r),e!==e+0)throw e;X(1,0)}}function be(e,t,n){var r=Y();try{C.get(e)(t,n)}catch(e){if(Z(r),e!==e+0)throw e;X(1,0)}}function Qd(e,t,n,r,a){var o=Y();try{return C.get(e)(t,n,r,a)}catch(e){if(Z(o),e!==e+0)throw e;X(1,0)}}function Pd(e,t,n,r){var a=Y();try{return C.get(e)(t,n,r)}catch(e){if(Z(a),e!==e+0)throw e;X(1,0)}}function he(e,t,n,r,a,o){var i=Y();try{C.get(e)(t,n,r,a,o)}catch(e){if(Z(i),e!==e+0)throw e;X(1,0)}}function ie(e,t,n,r,a,o,i){var c=Y();try{C.get(e)(t,n,r,a,o,i)}catch(e){if(Z(c),e!==e+0)throw e;X(1,0)}}function Vd(e,t,n,r,a,o,i,c,u,s){var b=Y();try{return C.get(e)(t,n,r,a,o,i,c,u,s)}catch(e){if(Z(b),e!==e+0)throw e;X(1,0)}}function Sd(e,t,n,r,a,o){var i=Y();try{return C.get(e)(t,n,r,a,o)}catch(e){if(Z(i),e!==e+0)throw e;X(1,0)}}function de(e,t,n,r,a){var o=Y();try{C.get(e)(t,n,r,a)}catch(e){if(Z(o),e!==e+0)throw e;X(1,0)}}function ge(e,t,n,r,a){var o=Y();try{C.get(e)(t,n,r,a)}catch(e){if(Z(o),e!==e+0)throw e;X(1,0)}}function ke(e,t,n,r,a,o,i,c,u,s){var b=Y();try{C.get(e)(t,n,r,a,o,i,c,u,s)}catch(e){if(Z(b),e!==e+0)throw e;X(1,0)}}function $d(e){var t=Y();try{C.get(e)()}catch(e){if(Z(t),e!==e+0)throw e;X(1,0)}}function Td(e,t,n,r,a,o,i){var c=Y();try{return C.get(e)(t,n,r,a,o,i)}catch(e){if(Z(c),e!==e+0)throw e;X(1,0)}}function Rd(e,t,n,r,a,o){var i=Y();try{return C.get(e)(t,n,r,a,o)}catch(e){if(Z(i),e!==e+0)throw e;X(1,0)}}function Ud(e,t,n,r,a,o,i,c){var u=Y();try{return C.get(e)(t,n,r,a,o,i,c)}catch(e){if(Z(u),e!==e+0)throw e;X(1,0)}}function Ld(e,t,n,r){var a=Y();try{return C.get(e)(t,n,r)}catch(e){if(Z(a),e!==e+0)throw e;X(1,0)}}function Kd(e,t,n,r){var a=Y();try{return C.get(e)(t,n,r)}catch(e){if(Z(a),e!==e+0)throw e;X(1,0)}}function Md(e){var t=Y();try{return C.get(e)()}catch(e){if(Z(t),e!==e+0)throw e;X(1,0)}}function je(e,t,n,r,a,o,i,c){var u=Y();try{C.get(e)(t,n,r,a,o,i,c)}catch(e){if(Z(u),e!==e+0)throw e;X(1,0)}}function Wd(e,t,n,r,a,o,i,c,u,s,b,f){var d=Y();try{return C.get(e)(t,n,r,a,o,i,c,u,s,b,f)}catch(e){if(Z(d),e!==e+0)throw e;X(1,0)}}function le(e,t,n,r,a,o,i,c,u,s,b){var f=Y();try{C.get(e)(t,n,r,a,o,i,c,u,s,b)}catch(e){if(Z(f),e!==e+0)throw e;X(1,0)}}function me(e,t,n,r,a,o,i,c,u,s,b,f,d,h,l,p){var v=Y();try{C.get(e)(t,n,r,a,o,i,c,u,s,b,f,d,h,l,p)}catch(e){if(Z(v),e!==e+0)throw e;X(1,0)}}function ee(e,t,n,r){var a=Y();try{C.get(e)(t,n,r)}catch(e){if(Z(a),e!==e+0)throw e;X(1,0)}}function ce(e,t,n,r){var a=Y();try{C.get(e)(t,n,r)}catch(e){if(Z(a),e!==e+0)throw e;X(1,0)}}function Yd(e){var t=Y();try{return se(e)}catch(e){if(Z(t),e!==e+0)throw e;X(1,0)}}function ne(e,t,n,r,a,o,i){var c=Y();try{re(e,t,n,r,a,o,i)}catch(e){if(Z(c),e!==e+0)throw e;X(1,0)}}function Xd(e,t,n,r,a,o,i){var c=Y();try{return te(e,t,n,r,a,o,i)}catch(e){if(Z(c),e!==e+0)throw e;X(1,0)}}function Zd(e,t,n,r,a){var o=Y();try{return ue(e,t,n,r,a)}catch(e){if(Z(o),e!==e+0)throw e;X(1,0)}}function ye(e=[]){var t=pe;e.unshift(da);var n=e.length,r=qe(4*(n+1)),a=r>>2;e.forEach((e=>{var t=A,n=a++,r=cb(e)+1,o=qe(r);db(e,x,o,r),t[n]=o})),A[a]=0;try{var o=t(n,r);zd(o,!0)}catch(e){e instanceof Oa||"unwind"==e||ea(1,e)}}function xe(){var e=ca;function t(){if(!ve&&(ve=!0,h.calledRun=!0,!qa)){if(h.noFSInit||ac||(ac=!0,$b(),h.stdin=h.stdin,h.stdout=h.stdout,h.stderr=h.stderr,h.stdin?N("/dev","stdin",h.stdin):Rb("/dev/tty","/dev/stdin"),h.stdout?N("/dev","stdout",null,h.stdout):Rb("/dev/tty","/dev/stdout"),h.stderr?N("/dev","stderr",null,h.stderr):Rb("/dev/tty1","/dev/stderr"),Wb("/dev/stdin",0),Wb("/dev/stdout",1),Wb("/dev/stderr",1)),Bb=!1,Pa(wa),Pa(xa),h.onRuntimeInitialized&&h.onRuntimeInitialized(),ze&&ye(e),h.postRun)for("function"==typeof h.postRun&&(h.postRun=[h.postRun]);h.postRun.length;){var t=h.postRun.shift();ya.unshift(t)}Pa(ya)}}if(!(0<Aa)){if(h.preRun)for("function"==typeof h.preRun&&(h.preRun=[h.preRun]);h.preRun.length;)za();Pa(va),0<Aa||(h.setStatus?(h.setStatus("Running..."),setTimeout((function(){setTimeout((function(){h.setStatus("")}),1),t()}),1)):t())}}if(h.dynCall_iiiiijj=function(){return(h.dynCall_iiiiijj=h.asm.fb).apply(null,arguments)},h.dynCall_iiiiiijj=function(){return(h.dynCall_iiiiiijj=h.asm.gb).apply(null,arguments)},h.dynCall_iiiij=function(){return(h.dynCall_iiiij=h.asm.hb).apply(null,arguments)},h.dynCall_vij=function(){return(h.dynCall_vij=h.asm.ib).apply(null,arguments)},h.___start_em_js=381888,h.___stop_em_js=382106,h.addRunDependency=Da,h.removeRunDependency=Ea,h.FS_createPath=bc,h.FS_createDataFile=ub,h.FS_createLazyFile=ec,h.FS_createDevice=N,h.FS_unlink=Tb,h.FS_createPreloadedFile=tb,Ca=function e(){ve||xe(),ve||(Ca=e)},h.preInit)for("function"==typeof h.preInit&&(h.preInit=[h.preInit]);0<h.preInit.length;)h.preInit.pop()();var ze=!0;h.noInitialRun&&(ze=!1),xe();