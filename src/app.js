require('dotenv').config();
const express = require('express');
const actuator = require('express-actuator');
const prometheusMiddleware = require('express-prometheus-middleware');

// Create two express apps: one for the web server and one for Prometheus metrics
const app = express();
const promApp = express();

app.disable('x-powered-by');
promApp.disable('x-powered-by');

// Enable prometheus metrics
app.use(prometheusMiddleware({
  metricsPath: '/actuator/prometheus',
  metricsApp: promApp,
  collectDefaultMetrics: true
}));

// Web server setup
app.get('/', (req, res) => {
  res.send('Hello, World!');
});

// Simple health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'UP' });
});

// Enable express-actuator with security configurations
app.use(actuator({
  basePath: '/actuator',
  infoGitMode: 'simple',  // Limits git information
  infoBuildOptions: false, // Disable build information
}));

module.exports = { app, promApp };