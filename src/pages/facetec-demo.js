import React, { useEffect, useState } from 'react';

export default function FaceTecDemo() {
  const [facetecLib, setFacetecLib] = useState(null);
  const [facetecVersion, setFacetecVersion] = useState(null);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Dynamically import the library from the compiled code
    import('@lib/facetec').then(module => {
      console.log('Loaded FaceTec library:', module);
      setFacetecLib(module);
      setLoading(false);

      // Get the FaceTec SDK version
      module.getFaceTecVersion()
        .then(version => {
          console.log('FaceTec SDK version:', version);
          setFacetecVersion(version);
        })
        .catch(err => {
          console.error('Error getting FaceTec version:', err);
          setError('Error getting FaceTec version. Make sure the FaceTec SDK is properly loaded.');
        });
    }).catch(err => {
      console.error('Failed to load FaceTec library:', err);
      setError('Failed to load FaceTec library. Make sure to run webpack build first.');
      setLoading(false);
    });
  }, []);

  const handleInitialize = () => {
    if (!facetecLib) return;

    // Example values - replace with your actual keys
    const deviceKeyIdentifier = 'your-device-key-identifier';
    const publicEncryptionKey = 'your-public-encryption-key';

    facetecLib.initializeFaceTec(deviceKeyIdentifier, publicEncryptionKey)
      .then(() => {
        alert('FaceTec SDK initialized successfully!');
      })
      .catch(err => {
        alert(`Failed to initialize FaceTec SDK: ${err.message}`);
      });
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>FaceTec SDK Demo</h1>
      <p>This page demonstrates how to use the FaceTec SDK in a Next.js application.</p>
      <p>The FaceTec SDK is loaded from the core-sdk directory using a relative import:</p>
      <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '5px' }}>
        {`import FaceTecSDK from './core-sdk/FaceTecSDK.js/FaceTecSDK';`}
      </pre>

      {error && (
        <div style={{ color: 'red', marginTop: '20px' }}>
          <p>{error}</p>
        </div>
      )}

      {loading && !error && (
        <div style={{ marginTop: '20px' }}>
          <p>Loading FaceTec SDK...</p>
        </div>
      )}

      {facetecLib && !loading && (
        <div style={{ marginTop: '20px' }}>
          <p>FaceTec SDK Version: {facetecVersion || 'Loading...'}</p>
          <button
            onClick={handleInitialize}
            style={{
              padding: '10px 20px',
              backgroundColor: '#0070f3',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            Initialize FaceTec SDK
          </button>
        </div>
      )}
    </div>
  );
}
