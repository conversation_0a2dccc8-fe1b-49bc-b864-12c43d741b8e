variables:
  COMPANY_CODE: techx
  PROJECT_CODE: ekyc
  SERVICE_NAME: ekyc-browser-demo
  SERVICE_LANGUAGE: js
  DEPENDENCY_MANAGEMENT: npm
  OVERWRITE_GLOBAL_BASE_IMAGE: false
  DOCKER_BASE_IMAGE_URL: ************.dkr.ecr.ap-southeast-1.amazonaws.com/aella-xplatform/facility/nodejs20
  DOCKER_BASE_IMAGE_VERSION: alpine3.21-nodejs20-************
  DOCKER_BASE_IMAGE_URL_MOUNTEBANK: ************.dkr.ecr.ap-southeast-1.amazonaws.com/aella-xplatform-ecr/facility/mountebank
  DOCKER_BASE_IMAGE_VERSION_MOUNTEBANK: nodejs16-alpine3.17-1.0.1
  GITLAB_CI_TEST_PROFILE: full
  SONAR_BINARY: ./
  SONAR_SOURCE: ./src
  SONAR_TESTS: ./testing
  SONAR_COVERAGE_EXCLUSIONS: src/**
  SONAR_EXCLUSIONS:  src/**,src/**/*,src/**/**/*,src/**/**/**/*
  SONAR_TEST_EXCLUSIONS: ''
  ENABLE_DEPLOY_DEV_ENV: 'true'
  ENABLE_TEST_AFTER_DEPLOY: 'true'
  ENABLE_DELETE_RESOURDE_AFTER_FINISH: 'true'
