{"version": 3, "file": "347.js", "mappings": ";;;;;AAAA,0BAA0B,mBAAO,CAAC,GAA2C;AAC7E,sBAAsB,mBAAO,CAAC,GAA0C;AACxE,gCAAgC,mBAAO,CAAC,GAAoD;;AAE5F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,iBAAiB,iBAAiB;AAClC;AACA,oBAAoB,+CAA+C,qBAAqB;AACxF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kFAAkF,eAAe;AACjG;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,QAAQ;AACzB;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,iBAAiB,QAAQ;AACzB;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,QAAQ;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;ACpJA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;AC1EA,qBAAqB,mBAAO,CAAC,GAAoC;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,uBAAuB;AACtC;AACA,oCAAoC;AACpC;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,uBAAuB;AACtC;AACA,wDAAwD;AACxD;AACA;AACA;AACA;;AAEA;;;;;;;;ACnCA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;AC1CA,yBAAyB,mBAAO,CAAC,GAAiC;AAClE,gCAAgC,mBAAO,CAAC,GAAwC;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,iBAAiB,iBAAiB;AAClC;AACA,6CAA6C;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,iBAAiB,iBAAiB;AAClC;AACA,wDAAwD;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;;;AAGA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;;;;;;;ACjGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iCAAiC,mBAAO,CAAC,GAA6C;AACtF,gCAAgC,mBAAO,CAAC,GAAiD;;AAEzF;AACA;AACA;AACA;AACA,4CAA4C;AAC5C,0DAA0D;AAC1D,wEAAwE;AACxE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2FAA2F;AAC3F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;;;;;;;;AC1IA,0BAA0B,mBAAO,CAAC,GAA2C;;AAE7E;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,UAAU;AACvB,eAAe,iBAAiB;AAChC;AACA;AACA;AACA,cAAc,0DAA0D;AACxE;AACA;AACA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;;;;;;;ACjEA;AACA,8BAA8B,mBAAO,CAAC,GAAyC;AAC/E,qBAAqB,mBAAO,CAAC,GAAgC;AAC7D,+BAA+B,mBAAO,CAAC,GAA0C;AACjF,mDAAmD,mBAAO,CAAC,GAA8D;;AAEzH;AACA,uBAAuB,mBAAO,CAAC,GAAoC;AACnE,0BAA0B,mBAAO,CAAC,GAAsC;;AAExE;AACA,QAAQ,eAAe,EAAE,mBAAO,CAAC,GAAwB;AACzD,QAAQ,gBAAgB,EAAE,mBAAO,CAAC,GAAwB;;;AAG1D;AACA,uBAAuB,mBAAO,CAAC,GAAW;;AAE1C;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA,6BAA6B,mBAAO,CAAC,GAAmC;AACxE;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,aAAa,iBAAiB;AAC9B;AACA,2CAA2C;AAC3C;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,aAAa;AAC1B;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,SAAS;AACtB;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,aAAa,iBAAiB;AAC9B;AACA,+DAA+D;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,iBAAiB;AAC9B;AACA,8CAA8C;;AAE9C;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA;AACA;AACA;AACA,gDAAgD,wCAA+C;AAC/F;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,oBAAoB,oDAAoD;AACxE,UAAU;AACV;AACA;AACA;AACA,KAAK;AACL,IAAI;AACJ;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,iBAAiB;AAC9B;AACA,oCAAoC;AACpC;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,YAAY,gBAAgB,EAAE,mBAAO,CAAC,GAAwB;AAC9D;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iCAAiC,MAAM;AACvC;AACA,uCAAuC,SAAS,GAAG,mEAAmE,GAAG,SAAS,GAAG,uBAAuB;AAC5J;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,WAAW,SAAS;AACpB,WAAW,SAAS;AACpB,WAAW,UAAU;AACrB,aAAa,iBAAiB;AAC9B;AACA,qCAAqC;AACrC;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,uCAAuC,SAAS;AAChD;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,SAAS;AACpB,WAAW,SAAS;AACpB,WAAW,SAAS;AACpB,WAAW,UAAU;AACrB,aAAa,iBAAiB;AAC9B;AACA,iDAAiD;AACjD;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,iBAAiB;AAC9B;AACA,4CAA4C;AAC5C;AACA;AACA;AACA;AACA;AACA,IAAI;;AAEJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,mCAAmC,WAAW,GAAG,wCAAwC;;AAEzF;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,UAAU;AACrB,aAAa,iBAAiB;AAC9B;AACA,yCAAyC;AACzC;AACA;AACA,IAAI;;AAEJ;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA,uCAAuC,SAAS;AAChD;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,oBAAoB,sCAAsC;AAC1D,UAAU;AACV,oBAAoB,sCAAsC;AAC1D,UAAU;AACV;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,mCAAmC,mBAAO,CAAC,GAAqC;AAChF;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,0EAA0E,WAAW;AACrF;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AC5rBA;AACA,uBAAuB,mBAAO,CAAC,GAA0C;;AAEzE;AACA;;AAEA;AACA;AACA,aAAa,iBAAiB;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW,QAAQ;AACnB,WAAW,QAAQ;AACnB,aAAa,kBAAkB;AAC/B;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,iBAAiB;AAC9B;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;ACrCA,sBAAsB,mBAAO,CAAC,GAAiB;AAC/C,qBAAqB,mBAAO,CAAC,GAAgB;;AAE7C;AACA;AACA;AACA;;;;;;;;ACNA,gCAAgC,mBAAO,CAAC,GAAoD;;AAE5F;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,iBAAiB,iBAAiB;AAClC;AACA,+CAA+C;AAC/C;AACA;AACA;AACA;AACA,+BAA+B,aAAa;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4FAA4F,WAAW;AACvG;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,8BAA8B;AAC9B;AACA,gGAAgG,WAAW;AAC3G,wEAAwE,WAAW;AACnF;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;;AAEjB;AACA;AACA,yEAAyE,KAAK,OAAO,8BAA8B;AACnH;;AAEA,cAAc;AACd;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;;;;;;;ACpGA,sBAAsB,mBAAO,CAAC,GAA0C;AACxE,QAAQ,eAAe,EAAE,mBAAO,CAAC,GAA4B;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,iBAAiB;AAChC;AACA,oCAAoC;AACpC;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD,2BAA2B,UAAU;AACrC,oBAAoB,IAAI;AACxB,4BAA4B,cAAc;AAC1C;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA,0DAA0D,gBAAgB;AAC1E;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,iBAAiB;AAChC;AACA,2CAA2C;AAC3C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,yCAAyC,SAAS;AAClD,2BAA2B,UAAU;AACrC,oBAAoB,IAAI;AACxB,4BAA4B,cAAc;AAC1C;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA,0DAA0D,gBAAgB;AAC1E;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,iBAAiB;AAChC;AACA,wDAAwD;AACxD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,0EAA0E,SAAS;AACnF,2BAA2B,UAAU;AACrC,oBAAoB,IAAI;AACxB,4BAA4B,cAAc;AAC1C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA,0DAA0D,gBAAgB;AAC1E;;AAEA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACxLA,iBAAiB,mBAAO,CAAC,GAAsB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACnBA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACjDA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,aAAa;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,SAAS;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,SAAS;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACxGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,4DAA4D;AAC5D;AACA;AACA;AACA;AACA,UAAU;AACV;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,kBAAkB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;AACA;AACA,OAAO;AACP,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACpFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB;AACA;AACA;AACA,2BAA2B,UAAU,KAAK,oBAAoB;AAC9D;AACA;AACA;AACA,iCAAiC,QAAQ,EAAE,QAAQ;AACnD;AACA;AACA,gCAAgC,QAAQ,EAAE,QAAQ;AAClD;AACA;AACA,iCAAiC,QAAQ,EAAE,QAAQ,kBAAkB,kBAAkB;AACvF;AACA;AACA;AACA,+BAA+B,QAAQ,EAAE,QAAQ;AACjD;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA,wCAAwC,OAAO;AAC/C;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB;AACA;AACA,+BAA+B,QAAQ,EAAE,SAAS,IAAI,OAAO;AAC7D;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,OAAO;AACtB;AACA;AACA;AACA;AACA,iCAAiC,cAAc;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB;AACA;AACA,kCAAkC,KAAK;AACvC;AACA,4BAA4B,2BAA2B;AACvD;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB;AACA;AACA,yCAAyC,KAAK;AAC9C;AACA,4BAA4B,2BAA2B;AACvD;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB;AACA;AACA,mCAAmC,UAAU,IAAI,OAAO;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,KAAK;AACpB;AACA;AACA,4BAA4B,MAAM;AAClC;AACA;AACA;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB;AACA;AACA;AACA,wCAAwC,WAAW,OAAO,oBAAoB;AAC9E;AACA;;AAEA;;;;;;;ACtJA,gCAAgC,mBAAO,CAAC,GAAoD;;AAE5F;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B;AAC/B;;AAEA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,iBAAiB,iBAAiB;AAClC;AACA,sDAAsD;AACtD;AACA;AACA;AACA;AACA,+BAA+B,aAAa;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4FAA4F,WAAW;AACvG;AACA;AACA;AACA;AACA;AACA,iCAAiC;AACjC;AACA,8BAA8B;AAC9B;AACA,gGAAgG,WAAW;AAC3G,wEAAwE,WAAW;AACnF;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;;;;;;;;ACnGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,QAAQ;AACvB;AACA;AACA,wBAAwB,KAAK;AAC7B;AACA;;AAEA;;;;;;;;ACfA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,8BAA8B,mBAAO,CAAC,CAA0C;AAChF,gCAAgC,mBAAO,CAAC,GAAiD;;AAEzF;AACA;AACA;AACA;AACA,4CAA4C;AAC5C,0DAA0D;AAC1D,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kBAAkB;AAClB;AACA;AACA;AACA,cAAc;AACd;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gGAAgG;AAChG;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;;;;;;;;ACnKA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,uBAAuB;AACtC;AACA,4BAA4B;AAC5B;AACA;AACA;;AAEA;;;;;;;;ACtBA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,uBAAuB;AACtC;AACA,4BAA4B;AAC5B;AACA;AACA;;AAEA", "sources": ["webpack://ScbTechXEkycSDK/./lib/domain/usecases/PostIDScanOnlyUseCase.js", "webpack://ScbTechXEkycSDK/./lib/domain/entities/SessionToken.js", "webpack://ScbTechXEkycSDK/./lib/data/repositories/AuthRepository.js", "webpack://ScbTechXEkycSDK/./lib/domain/entities/Currency.js", "webpack://ScbTechXEkycSDK/./lib/data/repositories/FaceTecRepository.js", "webpack://ScbTechXEkycSDK/./lib/processors/LivenessCheckProcessor.js", "webpack://ScbTechXEkycSDK/./lib/domain/usecases/PostLivenessCheckUseCase.js", "webpack://ScbTechXEkycSDK/./lib/simple.js", "webpack://ScbTechXEkycSDK/./lib/facetec.js", "webpack://ScbTechXEkycSDK/./lib/infrastructure/utils/index.js", "webpack://ScbTechXEkycSDK/./lib/data/datasources/IDScanDataSource.js", "webpack://ScbTechXEkycSDK/./lib/data/datasources/AuthApiDataSource.js", "webpack://ScbTechXEkycSDK/./lib/domain/usecases/FormatCurrencyUseCase.js", "webpack://ScbTechXEkycSDK/./lib/infrastructure/utils/UuidGenerator.js", "webpack://ScbTechXEkycSDK/./lib/infrastructure/utils/TokenStorage.js", "webpack://ScbTechXEkycSDK/./lib/infrastructure/services/FaceTecService.js", "webpack://ScbTechXEkycSDK/./lib/infrastructure/utils/DeveloperStatusMessages.js", "webpack://ScbTechXEkycSDK/./lib/data/datasources/LivenessCheckDataSource.js", "webpack://ScbTechXEkycSDK/./lib/domain/usecases/GreetUseCase.js", "webpack://ScbTechXEkycSDK/./lib/processors/PhotoIDScanProcessor.js", "webpack://ScbTechXEkycSDK/./lib/domain/usecases/GetFaceTecSessionTokenWithEkycTokenUseCase.js", "webpack://ScbTechXEkycSDK/./lib/domain/usecases/GetSessionTokenUseCase.js"], "sourcesContent": ["const FaceTecRepository = require('../../data/repositories/FaceTecRepository');\nconst UuidGenerator = require('../../infrastructure/utils/UuidGenerator');\nconst DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');\n\n/**\n * Use case for posting ID scan data\n * Contains business logic for ID scanning operations\n */\nclass PostIDScanOnlyUseCase {\n    constructor() {\n        this.faceTecRepository = new FaceTecRepository();\n    }\n\n    /**\n     * Execute the ID scan submission process\n     * @param {Object} params - Parameters for the use case\n     * @param {Object} params.idScanResult - FaceTec ID scan result\n     * @param {string} params.deviceKey - Device key for authentication\n     * @param {Object} params.additionalHeaders - Additional headers for the request\n     * @param {Function} params.onProgress - Progress callback function\n     * @returns {Promise<Object>} - Use case result\n     */\n    async execute({ idScanResult, deviceKey, additionalHeaders = {}, onProgress = null }) {\n        const startTime = performance.now();\n        \n        try {\n            DeveloperStatusMessages.logMessage('Starting PostIDScanOnlyUseCase execution');\n            \n            // Prepare scan data from FaceTec result\n            DeveloperStatusMessages.logMessage('Preparing scan data...');\n            const scanData = this.prepareScanData(idScanResult);\n            DeveloperStatusMessages.logData('Scan Data Keys', Object.keys(scanData));\n            \n            // Prepare headers\n            DeveloperStatusMessages.logMessage('Preparing headers...');\n            const headers = this.prepareHeaders(idScanResult, deviceKey, additionalHeaders);\n            DeveloperStatusMessages.logData('Request Headers', Object.keys(headers));\n            \n            // Validate scan data\n            DeveloperStatusMessages.logMessage('Validating scan data...');\n            this.faceTecRepository.validateScanData(scanData);\n            DeveloperStatusMessages.logSuccess('Scan data validation passed');\n            \n            // Submit to repository with progress tracking\n            DeveloperStatusMessages.logMessage('Submitting to repository...');\n            const response = await this.faceTecRepository.submitIDScan(scanData, headers, onProgress);\n            \n            // Process the response according to business rules\n            DeveloperStatusMessages.logMessage('Processing response...');\n            const result = this.processResponse(response);\n            \n            const endTime = performance.now();\n            DeveloperStatusMessages.logPerformance('PostIDScanOnlyUseCase.execute', startTime, endTime);\n            DeveloperStatusMessages.logSuccess(`UseCase completed successfully: ${result.success}`);\n            \n            return result;\n            \n        } catch (error) {\n            const endTime = performance.now();\n            DeveloperStatusMessages.logPerformance('PostIDScanOnlyUseCase.execute (failed)', startTime, endTime);\n            DeveloperStatusMessages.logError('PostIDScanOnlyUseCase - execute error', error);\n            throw error;\n        }\n    }\n\n    /**\n     * Prepare scan data from FaceTec ID scan result\n     * @param {Object} idScanResult - FaceTec ID scan result\n     * @returns {Object} - Prepared scan data\n     */\n    prepareScanData(idScanResult) {\n        const parameters = {\n            idScan: idScanResult.idScan,\n            enableConfirmInfo: true // default value follow spec\n        };\n\n        // Add front image if available\n        if (idScanResult.frontImages && idScanResult.frontImages[0]) {\n            parameters.idScanFrontImage = idScanResult.frontImages[0];\n        }\n\n        // Add back image if available\n        if (idScanResult.backImages && idScanResult.backImages[0]) {\n            parameters.idScanBackImage = idScanResult.backImages[0];\n        }\n\n        return parameters;\n    }\n\n    /**\n     * Prepare headers for the API request\n     * @param {Object} idScanResult - FaceTec ID scan result\n     * @param {string} deviceKey - Device key\n     * @param {Object} additionalHeaders - Additional headers\n     * @returns {Object} - Prepared headers\n     */\n    prepareHeaders(idScanResult, deviceKey, additionalHeaders) {\n        const headers = {};\n\n        // Add device key if available\n        if (deviceKey) {\n            headers['X-Device-Key'] = deviceKey;\n        }\n\n        // Add FaceTec user agent\n        if (idScanResult.sessionId) {\n            headers['X-User-Agent'] = FaceTecSDK.createFaceTecAPIUserAgentString(idScanResult.sessionId);\n        }\n\n        // Add additional headers\n        if (additionalHeaders.Authorization) {\n            headers['Authorization'] = additionalHeaders.Authorization;\n        }\n        if (additionalHeaders['X-Session-Id']) {\n            headers['X-Session-Id'] = additionalHeaders['X-Session-Id'];\n        }\n        if (additionalHeaders['X-Ekyc-Token']) {\n            headers['X-Ekyc-Token'] = additionalHeaders['X-Ekyc-Token'];\n        }\n        if (additionalHeaders.correlationid) {\n            headers['correlationid'] = additionalHeaders.correlationid;\n        }\n\n        // Generate X-Tid if not provided (mandatory field)\n        // Generate UUID v4 for transaction ID using UuidGenerator\n        headers['X-Tid'] = UuidGenerator.getUniqueId();\n\n        return headers;\n    }\n\n    /**\n     * Process the API response according to business rules\n     * @param {Object} response - API response\n     * @returns {Object} - Processed response\n     */\n    processResponse(response) {\n        // Business logic for processing response\n        // This is where you can add any business rules for handling the response\n        \n        return {\n            success: response.wasProcessed === true && response.error === false,\n            scanResultBlob: response.scanResultBlob,\n            originalResponse: response.originalResponse,\n            errorMessage: response.errorMessage\n        };\n    }\n}\n\nmodule.exports = PostIDScanOnlyUseCase; ", "/**\n * SessionToken entity\n * Represents a session token from the eKYC authentication API\n */\nclass SessionToken {\n  /**\n   * @param {Object} data - The session token data\n   */\n  constructor(data) {\n    this.data = data;\n  }\n\n  /**\n   * Get the token value\n   * @returns {string|null} - The token value or null if not available\n   */\n  getToken() {\n    return this.data?.token || null;\n  }\n\n  /**\n   * Get the eKYC token value\n   * @returns {string|null} - The eKYC token value or null if not available\n   */\n  getEkycToken() {\n    // Handle the new response format with code, description, and data.ekycToken\n    if (this.data?.data?.ekycToken) {\n      return this.data.data.ekycToken;\n    }\n    // Fallback to the old format or return null\n    return this.data?.ekycToken || null;\n  }\n\n  /**\n   * Get the expiration time\n   * @returns {string|null} - The expiration time or null if not available\n   */\n  getExpiresAt() {\n    return this.data?.expiresAt || null;\n  }\n\n  /**\n   * Get the response code\n   * @returns {string|null} - The response code or null if not available\n   */\n  getCode() {\n    return this.data?.code || null;\n  }\n\n  /**\n   * Get the response description\n   * @returns {string|null} - The response description or null if not available\n   */\n  getDescription() {\n    return this.data?.description || null;\n  }\n\n  /**\n   * Check if the token is valid\n   * @returns {boolean} - True if the token is valid\n   */\n  isValid() {\n    return !!this.getEkycToken();\n  }\n\n  /**\n   * Get the raw data\n   * @returns {Object} - The raw data\n   */\n  toJSON() {\n    return this.data;\n  }\n}\n\nmodule.exports = SessionToken;\n", "const SessionToken = require('../../domain/entities/SessionToken');\n\n/**\n * AuthRepository\n * Repository for authentication-related operations\n */\nclass AuthRepository {\n  /**\n   * @param {Object} authApiDataSource - The authentication API data source\n   */\n  constructor(authApiDataSource) {\n    this.authApiDataSource = authApiDataSource;\n  }\n\n  /**\n   * Get a session token\n   * @param {Object} headers - Optional headers to include in the request\n   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity\n   */\n  async getSessionToken(headers = {}) {\n    const data = await this.authApiDataSource.getSessionToken(headers);\n    return new SessionToken(data);\n  }\n\n  /**\n   * Get a FaceTec session token using the stored eKYC token\n   * @param {Object} headers - Optional headers to include in the request\n   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity\n   */\n  async getFaceTecSessionTokenWithEkycToken(headers = {}) {\n    const data = await this.authApiDataSource.getFaceTecSessionTokenWithEkycToken(headers);\n    return new SessionToken(data);\n  }\n}\n\nmodule.exports = AuthRepository;\n", "/**\n * Currency entity\n * Represents a currency amount\n */\nclass Currency {\n  /**\n   * @param {number} amount - The amount\n   * @param {string} currencyCode - The currency code (e.g., 'USD')\n   */\n  constructor(amount, currencyCode = 'USD') {\n    this.amount = amount;\n    this.currencyCode = currencyCode;\n  }\n\n  /**\n   * Format the currency amount\n   * @returns {string} - The formatted currency amount\n   */\n  format() {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: this.currencyCode\n    }).format(this.amount);\n  }\n\n  /**\n   * Get the amount\n   * @returns {number} - The amount\n   */\n  getAmount() {\n    return this.amount;\n  }\n\n  /**\n   * Get the currency code\n   * @returns {string} - The currency code\n   */\n  getCurrencyCode() {\n    return this.currencyCode;\n  }\n}\n\nmodule.exports = Currency;\n", "const IDScanDataSource = require('../datasources/IDScanDataSource');\nconst LivenessCheckDataSource = require('../datasources/LivenessCheckDataSource');\n\n/**\n * Repository for FaceTec related operations\n * Implements the repository pattern to abstract data access\n */\nclass FaceTecRepository {\n    constructor() {\n        this.idScanDataSource = new IDScanDataSource();\n        this.livenessCheckDataSource = new LivenessCheckDataSource();\n    }\n\n    /**\n     * Submit ID scan data for processing\n     * @param {Object} scanData - The scan data including images and metadata\n     * @param {Object} headers - Additional headers for the request\n     * @param {Function} onProgress - Progress callback function\n     * @returns {Promise<Object>} - Processed response\n     */\n    async submitIDScan(scanData, headers = {}, onProgress = null) {\n        try {\n            // Call the data source to post ID scan data\n            const response = await this.idScanDataSource.postIDScanOnly(scanData, headers, onProgress);\n            \n            // Repository can add additional business logic here if needed\n            // For example: data transformation, caching, etc.\n            \n            return response;\n        } catch (error) {\n            console.error('FaceTecRepository - submitIDScan error:', error);\n            throw error;\n        }\n    }\n\n    /**\n     * Submit liveness check data for processing\n     * @param {Object} livenessData - The liveness data including face scan and audit trail\n     * @param {Object} headers - Additional headers for the request\n     * @param {Function} onProgress - Progress callback function\n     * @returns {Promise<Object>} - Processed response\n     */\n    async submitLivenessCheck(livenessData, headers = {}, onProgress = null) {\n        try {\n            // Call the data source to post liveness check data\n            const response = await this.livenessCheckDataSource.postLivenessCheck(livenessData, headers, onProgress);\n            \n            // Repository can add additional business logic here if needed\n            // For example: data transformation, caching, etc.\n            \n            return response;\n        } catch (error) {\n            console.error('FaceTecRepository - submitLivenessCheck error:', error);\n            throw error;\n        }\n    }\n\n\n    /**\n     * Validate scan data before submission\n     * @param {Object} scanData - The scan data to validate\n     * @returns {boolean} - True if valid, throws error if invalid\n     */\n    validateScanData(scanData) {\n        if (!scanData) {\n            throw new Error('Scan data is required');\n        }\n\n        if (!scanData.idScan) {\n            throw new Error('ID scan data is required');\n        }\n\n        // Add more validation rules as needed\n        return true;\n    }\n\n\n    /**\n     * Validate liveness data before submission\n     * @param {Object} livenessData - The liveness data to validate\n     * @returns {boolean} - True if valid, throws error if invalid\n     */\n    validateLivenessData(livenessData) {\n        if (!livenessData) {\n            throw new Error('Liveness data is required');\n        }\n\n        if (!livenessData.faceScan) {\n            throw new Error('Face scan data is required');\n        }\n\n        // Add more validation rules as needed\n        return true;\n    }\n\n}\n\nmodule.exports = FaceTecRepository;\n", "//\n// Welcome to the annotated FaceTec Device SDK core code for performing secure Liveness Checks.\n//\n//\n// This is an example self-contained class to perform Liveness Checks with the FaceTec SDK.\n// You may choose to further componentize parts of this in your own Apps based on your specific requirements.\n//\n\nconst PostLivenessCheckUseCase = require('../domain/usecases/PostLivenessCheckUseCase');\nconst DeveloperStatusMessages = require('../infrastructure/utils/DeveloperStatusMessages');\n\nvar LivenessCheckProcessor = /** @class */ (function () {\n    function LivenessCheckProcessor(sessionToken, sampleAppControllerReference, deviceKey, additionalHeaders) {\n        var _this = this;\n        this.latestNetworkRequest = new XMLHttpRequest();\n        this.deviceKey = deviceKey || null; // Store the device key\n        this.additionalHeaders = additionalHeaders || {}; // get additional headers\n        this.postLivenessCheckUseCase = new PostLivenessCheckUseCase(); // Initialize use case\n        \n        //\n        // Part 2:  Handling the Result of a FaceScan\n        //\n        this.processSessionResultWhileFaceTecSDKWaits = function (sessionResult, faceScanResultCallback) {\n            _this.latestSessionResult = sessionResult;\n            //\n            // Part 3:  Handles early exit scenarios where there is no FaceScan to handle -- i.e. User Cancellation, Timeouts, etc.\n            //\n            if (sessionResult.status !== FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully) {\n                _this.latestNetworkRequest.abort();\n                _this.latestNetworkRequest = new XMLHttpRequest();\n                faceScanResultCallback.cancel();\n                return;\n            }\n            \n            // IMPORTANT:  FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully DOES NOT mean the Liveness Check was Successful.\n            // It simply means the User completed the Session and a 3D FaceScan was created.  You still need to perform the Liveness Check on your Servers.\n            \n            //\n            // Part 4: Use Clean Architecture - Call UseCase instead of direct API\n            //\n            _this.executeLivenessCheckUseCase(sessionResult, faceScanResultCallback);\n        };\n\n        //\n        // New method: Execute Liveness Check using Clean Architecture UseCase\n        //\n        this.executeLivenessCheckUseCase = async function(sessionResult, faceScanResultCallback) {\n            try {\n                // Create progress callback for upload tracking\n                const onProgress = function(progress) {\n                    faceScanResultCallback.uploadProgress(progress);\n                };\n\n                // Execute the use case\n                const result = await _this.postLivenessCheckUseCase.execute({\n                    sessionResult: sessionResult,\n                    deviceKey: _this.deviceKey,\n                    additionalHeaders: _this.additionalHeaders,\n                    onProgress: onProgress\n                });\n\n                // Handle the result\n                if (result.success) {\n                    // Demonstrates dynamically setting the Success Screen Message.\n                    FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage(\"Face Scanned\\n3D Liveness Proven\");\n                    \n                    // Proceed to next step with scanResultBlob\n                    faceScanResultCallback.proceedToNextStep(result.scanResultBlob);\n                } else {\n                    // Handle error case\n                    _this.cancelDueToNetworkError(result.errorMessage || \"Unexpected API response, cancelling out.\", faceScanResultCallback);\n                }\n            } catch (error) {\n                console.error('LivenessCheckProcessor - executeLivenessCheckUseCase error:', error);\n                _this.cancelDueToNetworkError(error.message || \"Exception while handling API response, cancelling out.\", faceScanResultCallback);\n            }\n        };\n\n        //\n        // Part 9:  This function gets called after the FaceTec SDK is completely done.  There are no parameters because you have already been passed all data in the processSessionWhileFaceTecSDKWaits function and have already handled all of your own results.\n        //\n        this.onFaceTecSDKCompletelyDone = function () {\n            //\n            // DEVELOPER NOTE:  onFaceTecSDKCompletelyDone() is called after the Session has completed or you signal the FaceTec SDK with cancel().\n            // Calling a custom function on the Sample App Controller is done for demonstration purposes to show you that here is where you get control back from the FaceTec SDK.\n            //\n            // If the Liveness Check was processed get the success result from isCompletelyDone\n            if (_this.latestSessionResult !== null) {\n                _this.success = _this.latestSessionResult.isCompletelyDone;\n            }\n            // Log success message\n            if (_this.success) {\n                DeveloperStatusMessages.logMessage(\"Liveness Check Complete\");\n            }\n            \n            // Pass the complete session result to the controller\n            _this.sampleAppControllerReference.onComplete(_this.latestSessionResult, 200); // Use 200 as default status\n        };\n\n        // Helper function to ensure the session is only cancelled once\n        this.cancelDueToNetworkError = function (networkErrorMessage, faceScanResultCallback) {\n            if (_this.cancelledDueToNetworkError === false) {\n                console.error(networkErrorMessage);\n                _this.cancelledDueToNetworkError = true;\n                faceScanResultCallback.cancel();\n            }\n        };\n\n        //\n        // DEVELOPER NOTE:  This public convenience method is for demonstration purposes only so the Sample App can get information about what is happening in the processor.\n        // In your code, you may not even want or need to do this.\n        //\n        this.isSuccess = function () {\n            return _this.success;\n        };\n\n        //\n        // DEVELOPER NOTE:  These properties are for demonstration purposes only so the Sample App can get information about what is happening in the processor.\n        // In the code in your own App, you can pass around signals, flags, intermediates, and results however you would like.\n        //\n        this.success = false;\n        this.sampleAppControllerReference = sampleAppControllerReference;\n        this.latestSessionResult = null;\n        this.cancelledDueToNetworkError = false;\n\n        //\n        // Part 1:  Starting the FaceTec Session\n        //\n        // Required parameters:\n        // - FaceTecFaceScanProcessor:  A class that implements FaceTecFaceScanProcessor, which handles the FaceScan when the User completes a Session.  In this example, \"this\" implements the class.\n        // - sessionToken:  A valid Session Token you just created by calling your API to get a Session Token from the Server SDK.\n        //\n        new FaceTecSDK.FaceTecSession(this, sessionToken);\n    }\n    return LivenessCheckProcessor;\n}());\n\n// Export the LivenessCheckProcessor class for use in Node.js/webpack environments\nmodule.exports = LivenessCheckProcessor;\n", "const FaceTecRepository = require('../../data/repositories/FaceTecRepository');\n\n/**\n * UseCase for handling Liveness Check API calls\n */\nclass PostLivenessCheckUseCase {\n  constructor() {\n    this.faceTecRepository = new FaceTecRepository();\n  }\n\n  /**\n   * Execute the Liveness Check\n   * @param {Object} params - Parameters for the liveness check\n   * @param {Object} params.sessionResult - The FaceTec session result\n   * @param {string} params.deviceKey - Optional device key for API\n   * @param {Object} params.additionalHeaders - Additional headers to include in the request\n   * @param {Function} params.onProgress - Callback for upload progress\n   * @returns {Promise<Object>} - Result of the liveness check\n   */\n  async execute(params) {\n    try {\n      const { sessionResult, deviceKey, additionalHeaders, onProgress } = params;\n      \n      // Prepare headers\n      const headers = { ...additionalHeaders };\n      if (deviceKey) {\n        headers['X-Device-Key'] = deviceKey;\n      }\n      \n      // Add FaceTec user agent header\n      if (sessionResult.sessionId && typeof FaceTecSDK !== 'undefined') {\n        headers['X-User-Agent'] = FaceTecSDK.createFaceTecAPIUserAgentString(sessionResult.sessionId);\n      }\n      \n      // Prepare parameters for the API call\n      const livenessData = {\n        faceScan: sessionResult.faceScan,\n        auditTrailImage: sessionResult.auditTrail[0],\n        lowQualityAuditTrailImage: sessionResult.lowQualityAuditTrail[0],\n        sessionId: sessionResult.sessionId,\n        function: 'liveness'\n      };\n      \n      // Call the repository\n      const response = await this.faceTecRepository.submitLivenessCheck(livenessData, headers, onProgress);\n      \n      // Process the response\n      if (response.wasProcessed === true && response.error === false) {\n        return {\n          success: true,\n          scanResultBlob: response.scanResultBlob\n        };\n      } else {\n        return {\n          success: false,\n          errorMessage: response.errorMessage || \"Server returned an error.\"\n        };\n      }\n    } catch (error) {\n      console.error(\"PostLivenessCheckUseCase error:\", error);\n      throw error;\n    }\n  }\n}\n\nmodule.exports = PostLivenessCheckUseCase;", "// Import use cases\nconst FormatCurrencyUseCase = require('./domain/usecases/FormatCurrencyUseCase');\nconst GreetUseCase = require('./domain/usecases/GreetUseCase');\nconst GetSessionTokenUseCase = require('./domain/usecases/GetSessionTokenUseCase');\nconst GetFaceTecSessionTokenWithEkycTokenUseCase = require('./domain/usecases/GetFaceTecSessionTokenWithEkycTokenUseCase');\n\n// Import repositories and data sources\nconst AuthRepository = require('./data/repositories/AuthRepository');\nconst AuthApiDataSource = require('./data/datasources/AuthApiDataSource');\n\n// Import utilities\nconst { TokenStorage } = require('./infrastructure/utils');\nconst { UuidGenerator } = require('./infrastructure/utils');\n\n\n// Import FaceTec service\nconst facetecService = require('./facetec');\n\n// Create instances of data sources\nconst authApiDataSource = new AuthApiDataSource();\n\n// Create instances of repositories\nconst authRepository = new AuthRepository(authApiDataSource);\n\n// Create instances of use cases\nconst formatCurrencyUseCase = new FormatCurrencyUseCase();\nconst greetUseCase = new GreetUseCase();\nconst getSessionTokenUseCase = new GetSessionTokenUseCase(authRepository);\nconst getFaceTecSessionTokenWithEkycTokenUseCase = new GetFaceTecSessionTokenWithEkycTokenUseCase(authRepository);\n\n// Import PhotoIDScanProcessor\nconst PhotoIDScanProcessor = require('./processors/PhotoIDScanProcessor');\n/**\n * Format a currency amount\n * @param {number} amount - The amount to format\n * @param {string} currency - The currency code (e.g., 'USD')\n * @returns {string} - The formatted currency amount\n */\nconst formatCurrency = (amount, currency = 'USD') => {\n  return formatCurrencyUseCase.execute(amount, currency);\n};\n\n/**\n * Generate a greeting message\n * @param {string} name - The name to greet\n * @returns {string} - The greeting message\n */\nconst greet = (name) => {\n  return greetUseCase.execute(name);\n};\n\n/**\n * Get a session token from the eKYC authentication API\n * @param {Object} headers - Optional additional headers to include in the request\n * @param {boolean} storeToken - Whether to store the ekycToken in localStorage (default: true)\n * @returns {Promise<Object>} - The response data\n */\nconst getSessionToken = async (headers = {}, storeToken = true) => {\n  try {\n    // Store the session ID from headers if provided\n    if (headers['X-Session-Id']) {\n      TokenStorage.storeSessionId(headers['X-Session-Id']);\n    }\n\n    const sessionToken = await getSessionTokenUseCase.execute(headers);\n    const responseData = sessionToken.toJSON();\n\n    // Store the session ID from the response headers if available\n    // Note: This would require modifying the API response to include the headers\n    // For now, we'll use the session ID from the request headers\n\n    // Store the ekycToken if requested\n    if (storeToken) {\n      const ekycToken = sessionToken.getEkycToken();\n      if (ekycToken) {\n        TokenStorage.storeEkycToken(ekycToken);\n      }\n    }\n\n    return responseData;\n  } catch (error) {\n    console.error('Error getting session token:', error);\n    throw error;\n  }\n};\n\n/**\n * Get the stored eKYC token\n * @returns {string|null} - The stored eKYC token or null if not found\n */\nconst getStoredEkycToken = () => {\n  return TokenStorage.getEkycToken();\n};\n\n/**\n * Remove the stored eKYC token\n * @returns {boolean} - True if the token was removed successfully\n */\nconst clearEkycToken = () => {\n  return TokenStorage.removeEkycToken();\n};\n\n/**\n * Get a FaceTec session token using the stored eKYC token\n * @param {Object} headers - Optional additional headers to include in the request\n * @param {boolean} initializeFaceTecSdk - Whether to initialize FaceTec SDK with the response data (default: true)\n * @returns {Promise<Object>} - The response data with an additional property 'faceTecInitialized' indicating if FaceTec was initialized\n */\nconst getFaceTecSessionTokenWithEkycToken = async (headers = {}, initializeFaceTecSdk = true) => {\n  try {\n    // Get the stored session ID if available\n    const storedSessionId = TokenStorage.getSessionId();\n    if (storedSessionId && !headers['X-Session-Id']) {\n      // Add the stored session ID to the headers if not already provided\n      headers = {\n        ...headers,\n        'X-Session-Id': storedSessionId\n      };\n    }\n\n    const sessionToken = await getFaceTecSessionTokenWithEkycTokenUseCase.execute(headers);\n    const responseData = sessionToken.toJSON();\n\n    // Add a property to track if FaceTec was initialized\n    responseData.faceTecInitialized = false;\n\n\n    // Initialize FaceTec SDK if requested and if the response contains the required data\n    if (initializeFaceTecSdk &&\n        responseData &&\n        responseData.code === \"CUS-KYC-1000\" &&\n        responseData.data &&\n        responseData.data.deviceKey &&\n        responseData.data.encryptionKey) {\n\n      try {\n        // Initialize FaceTec with the values from the response\n        await facetecService.initializeFaceTec(\n          responseData.data.deviceKey,\n          responseData.data.encryptionKey\n        );\n        // console.log(\"Status: \" + facetecService.loadFaceTecSDK.getStatus());\n        console.log('FaceTec SDK initialized successfully');\n        responseData.faceTecInitialized = true;\n      } catch (initError) {\n        // console.log(\"Status: \" + facetecService.loadFaceTecSDK.getStatus());\n        console.error('Error initializing FaceTec SDK:', initError);\n        responseData.faceTecError = initError.message || 'Failed to initialize FaceTec SDK';\n      }\n    }\n\n    return responseData;\n  } catch (error) {\n    console.error('Error getting FaceTec session token with eKYC token:', error);\n    throw error;\n  }\n};\n\n/**\n * Perform Photo ID Scan using FaceTec SDK\n * @param {Object} headers - Optional additional headers to include in the request\n * @param {string} deviceKey - The device key for the Photo ID Scan\n * @returns {Promise<Object>} - The scan result data\n */\nconst performPhotoIDScan = async (headers = {}, deviceKey = null, sessionTokenResponse = null) => {\n\n  try {\n    if (!deviceKey) {\n      throw new Error('deviceKey parameter is required for Photo ID Scan');\n    }\n\n    if (!sessionTokenResponse.faceTecInitialized) {\n      throw new Error('FaceTec SDK not initialized properly');\n    }\n\n    const FaceTecSDK = await facetecService.loadFaceTecSDK();\n    FaceTecSDK.setResourceDirectory(\"/core-sdk/FaceTecSDK.js/resources\");\n    FaceTecSDK.setImagesDirectory(\"/core-sdk/FaceTec_images\");\n\n    const controller = {\n      onComplete: (sessionResult, idScanResult, networkResponseStatus) => {\n        return { sessionResult, idScanResult, networkResponseStatus };\n      }\n    };\n\n    // ✅ prepare headers follow spec for PhotoIDScanProcessor\n    const additionalHeaders = {\n      'X-Session-Id': headers['X-Session-Id'] || sessionTokenResponse.data?.sessionId,\n      'X-Ekyc-Token': sessionTokenResponse.data?.ekycToken || TokenStorage.getEkycToken(),\n      'correlationid': headers.correlationid || require('./infrastructure/utils').UuidGenerator.getUniqueId()\n    };\n\n    const processor = new PhotoIDScanProcessor(\n      sessionTokenResponse.data.sessionFaceTec,\n      controller,\n      deviceKey,\n      additionalHeaders  // ✅ send additional headers\n    );\n\n    return new Promise((resolve, reject) => {\n      controller.onComplete = (sessionResult, idScanResult, networkResponseStatus) => {\n        if (processor.isSuccess()) {\n          resolve({ sessionResult, idScanResult, networkResponseStatus });\n        } else {\n          reject(new Error('ID scan failed'));\n        }\n      };\n    });\n  } catch (error) {\n    console.error('Error performing photo ID scan:', error);\n    throw error;\n  }\n};\n\n/**\n * 1. Initialize eKYC SDK\n * @param {Object} options - Initialization options\n * @param {string} options.sessionId - Session ID for the eKYC session\n * @param {string} options.token - API token for authentication\n * @param {string} options.environment - Environment (development, staging, production)\n * @param {string} options.language - Language code (default: 'en')\n * @param {Function} options.initCallback - Optional initialization callback\n * @returns {Promise<Object>} Initialization result\n */\nconst initEkyc = async (options = {}) => {\n  const {\n    sessionId,\n    token,\n    environment = 'development',\n    language = 'en',\n    initCallback\n  } = options;\n\n  // Validate required parameters\n  if (!sessionId) {\n    throw new Error('sessionId is required for eKYC initialization');\n  }\n  if (!token) {\n    throw new Error('token is required for eKYC initialization');\n  }\n\n  try {\n    console.log('🚀 Initializing eKYC SDK with sessionId:', sessionId);\n\n    // Generate and store device ID if not exists\n    const { UuidGenerator } = require('./infrastructure/utils');\n    let deviceId = TokenStorage.getToken('ekyc_device_id');\n    if (!deviceId) {\n      deviceId = UuidGenerator.getUniqueId();\n      TokenStorage.storeToken('ekyc_device_id', deviceId);\n    }\n\n    // Store session information\n    TokenStorage.storeToken('ekyc_session_id', sessionId);\n    TokenStorage.storeToken('ekyc_api_token', token);\n    TokenStorage.storeToken('ekyc_environment', environment);\n    TokenStorage.storeToken('ekyc_language', language);\n\n    // Prepare headers for session token request\n    const sessionHeaders = {\n      'Authorization': `Bearer ${token}`,\n      'X-Session-Id': sessionId,\n      'X-Ekyc-Device-Info': `browser|${deviceId}|${typeof window !== 'undefined' ? window.location.origin : 'unknown'}|${language}|${language.toUpperCase()}`\n    };\n\n    // Get session token\n    console.log('📡 Getting session token...');\n    const sessionTokenResponse = await getSessionToken(sessionHeaders, true);\n\n    // Get FaceTec session token and initialize\n    console.log('🎭 Getting FaceTec session token and initializing...');\n    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(sessionHeaders, true);\n\n    const result = {\n      success: true,\n      sessionToken: sessionTokenResponse,\n      faceTecToken: faceTecResponse,\n      environment,\n      language,\n      sessionId,\n      initialized: true,\n      faceTecInitialized: faceTecResponse.faceTecInitialized || false\n    };\n\n    console.log('✅ eKYC SDK initialized successfully');\n\n    // Call initialization callback if provided\n    if (initCallback && typeof initCallback === 'function') {\n      initCallback(result);\n    }\n\n    return result;\n  } catch (error) {\n    console.error('❌ Error initializing eKYC SDK:', error);\n    const errorResult = {\n      success: false,\n      error: error.message || 'Failed to initialize eKYC SDK',\n      environment,\n      language,\n      sessionId,\n      initialized: false\n    };\n\n    if (initCallback && typeof initCallback === 'function') {\n      initCallback(errorResult);\n    }\n\n    throw error;\n  }\n};\n\n/**\n * 2. OCR ID Card scanning\n * @param {Object} options - OCR options\n * @param {boolean} options.checkExpiredIdCard - Check if ID card is expired (default: true)\n * @param {boolean} options.checkDopa - Check against DOPA database (default: false)\n * @param {boolean} options.enableConfirmInfo - Enable confirmation screen (default: true)\n * @param {Function} options.callback - Result callback function\n * @returns {Promise<Object>} OCR results\n */\nconst ocrIdCard = async (options = {}) => {\n  const {\n    checkExpiredIdCard = true,\n    checkDopa = false,\n    enableConfirmInfo = true,\n    callback\n  } = options;\n\n  try {\n    console.log('📄 Starting OCR ID Card scan...');\n\n    // Get stored session information\n    const sessionId = TokenStorage.getToken('ekyc_session_id');\n    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';\n\n    if (!sessionId) {\n      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');\n    }\n\n    // Prepare headers\n    const headers = {\n      'X-Session-Id': sessionId,\n      'X-Ekyc-Device-Info': `browser|${deviceId}`,\n      'X-Ekyc-Token': TokenStorage.getToken('ekyc_token')\n    };\n\n    // Ensure FaceTec is initialized by getting session token\n    console.log('🎭 Ensuring FaceTec SDK is initialized...');\n    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(headers, true);\n\n    if (!faceTecResponse.faceTecInitialized) {\n      throw new Error('FaceTec SDK not properly initialized');\n    }\n\n    // Perform ID scan using existing logic\n    console.log('🔍 Performing ID scan...');\n    const scanResult = await performPhotoIDScan(headers, deviceId, faceTecResponse);\n\n    const result = {\n      success: true,\n      ocrData: scanResult,\n      checkExpiredIdCard,\n      checkDopa,\n      enableConfirmInfo,\n      sessionId,\n      scanType: 'id_card_ocr'\n    };\n\n    console.log('✅ OCR ID Card scan completed successfully');\n\n    // Call callback if provided\n    if (callback && typeof callback === 'function') {\n      callback(result);\n    }\n\n    return result;\n  } catch (error) {\n    console.error('❌ Error performing OCR ID card scan:', error);\n    const errorResult = {\n      success: false,\n      error: error.message || 'Failed to perform OCR ID card scan',\n      checkExpiredIdCard,\n      checkDopa,\n      enableConfirmInfo\n    };\n\n    if (callback && typeof callback === 'function') {\n      callback(errorResult);\n    }\n\n    throw error;\n  }\n};\n\n/**\n * 3. OCR ID Card with facial verification\n * @param {Object} options - OCR and face verification options\n * @param {boolean} options.checkExpiredIdCard - Check if ID card is expired (default: true)\n * @param {boolean} options.checkDopa - Check against DOPA database (default: false)\n * @param {boolean} options.enableConfirmInfo - Enable confirmation screen (default: true)\n * @param {Function} options.ocrResultsCallback - Callback for OCR and face verification results\n * @returns {Promise<Object>} Combined OCR and face verification results\n */\nconst ocrIdCardVerifyByFace = async (options = {}) => {\n  const {\n    checkExpiredIdCard = true,\n    checkDopa = false,\n    enableConfirmInfo = true,\n    ocrResultsCallback\n  } = options;\n\n  try {\n    console.log('👤 Starting OCR ID Card with face verification...');\n\n    // First perform OCR ID card scan\n    const ocrResult = await ocrIdCard({\n      checkExpiredIdCard,\n      checkDopa,\n      enableConfirmInfo\n    });\n\n    // Then perform liveness check for face verification\n    const livenessResult = await livenessCheck();\n\n    const result = {\n      success: true,\n      ocrData: ocrResult.ocrData,\n      faceVerification: livenessResult,\n      combined: true,\n      checkExpiredIdCard,\n      checkDopa,\n      enableConfirmInfo,\n      sessionId: ocrResult.sessionId,\n      scanType: 'id_card_ocr_with_face'\n    };\n\n    console.log('✅ OCR ID Card with face verification completed successfully');\n\n    // Call callback if provided\n    if (ocrResultsCallback && typeof ocrResultsCallback === 'function') {\n      ocrResultsCallback(result);\n    }\n\n    return result;\n  } catch (error) {\n    console.error('❌ Error performing OCR ID card with face verification:', error);\n    const errorResult = {\n      success: false,\n      error: error.message || 'Failed to perform OCR ID card with face verification',\n      checkExpiredIdCard,\n      checkDopa,\n      enableConfirmInfo\n    };\n\n    if (ocrResultsCallback && typeof ocrResultsCallback === 'function') {\n      ocrResultsCallback(errorResult);\n    }\n\n    throw error;\n  }\n};\n\n/**\n * 4. NDID digital identity verification\n * @param {Object} options - NDID verification options\n * @param {string} options.identifierType - Type of identifier (citizenId, passport, etc.)\n * @param {string} options.identifierValue - The identifier value\n * @param {string} options.serviceId - Service ID for NDID verification\n * @param {Function} options.ndidVerificationCallback - Callback for NDID verification results\n * @returns {Promise<Object>} NDID verification results\n */\nconst ndidVerification = async (options = {}) => {\n  const {\n    identifierType,\n    identifierValue,\n    serviceId,\n    ndidVerificationCallback\n  } = options;\n\n  // Validate required parameters\n  if (!identifierType) {\n    throw new Error('identifierType is required for NDID verification');\n  }\n  if (!identifierValue) {\n    throw new Error('identifierValue is required for NDID verification');\n  }\n  if (!serviceId) {\n    throw new Error('serviceId is required for NDID verification');\n  }\n\n  try {\n    console.log('🆔 Starting NDID verification...');\n\n    // Get stored session information\n    const sessionId = TokenStorage.getToken('ekyc_session_id');\n    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';\n\n    if (!sessionId) {\n      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');\n    }\n\n    // Simulate NDID verification process\n    // In a real implementation, this would call the actual NDID verification API\n    const verificationId = `ndid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n    // Simulate processing time\n    await new Promise(resolve => setTimeout(resolve, 2000));\n\n    const result = {\n      success: true,\n      ndidVerified: true,\n      identifierType,\n      identifierValue,\n      serviceId,\n      sessionId,\n      verificationId,\n      timestamp: new Date().toISOString(),\n      // Simulated NDID response\n      ndidResponse: {\n        status: 'verified',\n        confidence: 0.95,\n        details: {\n          identityConfirmed: true,\n          documentValid: true,\n          biometricMatch: true\n        }\n      }\n    };\n\n    console.log('✅ NDID verification completed successfully');\n\n    // Call callback if provided\n    if (ndidVerificationCallback && typeof ndidVerificationCallback === 'function') {\n      ndidVerificationCallback(result);\n    }\n\n    return result;\n  } catch (error) {\n    console.error('❌ Error performing NDID verification:', error);\n    const errorResult = {\n      success: false,\n      error: error.message || 'Failed to perform NDID verification',\n      identifierType,\n      identifierValue,\n      serviceId\n    };\n\n    if (ndidVerificationCallback && typeof ndidVerificationCallback === 'function') {\n      ndidVerificationCallback(errorResult);\n    }\n\n    throw error;\n  }\n};\n\n/**\n * 5. Facial liveness detection\n * @param {Object} options - Liveness check options\n * @param {Function} options.livenessCheckCallback - Callback for liveness check results\n * @returns {Promise<Object>} Liveness check results\n */\nconst livenessCheck = async (options = {}) => {\n  const {\n    livenessCheckCallback\n  } = options;\n\n  try {\n    console.log('👁️ Starting liveness check...');\n\n    // Get stored session information\n    const sessionId = TokenStorage.getToken('ekyc_session_id');\n    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';\n\n    if (!sessionId) {\n      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');\n    }\n\n    // Prepare headers\n    const headers = {\n      'X-Session-Id': sessionId,\n      'X-Ekyc-Device-Info': `browser|${deviceId}`\n    };\n\n    // Get FaceTec session token if not available\n    console.log('🎭 Ensuring FaceTec SDK is initialized...');\n    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(headers, true);\n\n    if (!faceTecResponse.faceTecInitialized) {\n      throw new Error('FaceTec SDK not properly initialized');\n    }\n\n    // Prepare additional headers for the API request\n    const additionalHeaders = {\n      'X-Device-Key': deviceId || faceTecResponse.data?.deviceKey,\n      'X-Session-Id': sessionId,\n      'X-Ekyc-Token': faceTecResponse.data?.ekycToken || TokenStorage.getEkycToken(),\n      'X-Tid': UuidGenerator.getUniqueId(),\n      'correlationid': UuidGenerator.getUniqueId()\n    };\n\n    // Create a controller for the LivenessCheckProcessor\n    const controller = {\n      onComplete: (sessionResult, networkResponseStatus) => {\n        return { sessionResult, networkResponseStatus };\n      }\n    };\n\n    // Create a promise to handle the liveness check result\n    const livenessPromise = new Promise((resolve, reject) => {\n      // Override the onComplete method to handle the result\n      controller.onComplete = (sessionResult, networkResponseStatus) => {\n        // Check if the processor was successful instead of relying only on sessionResult status\n        if (processor.isSuccess()) {\n          resolve({ sessionResult, networkResponseStatus });\n        } else if (sessionResult && sessionResult.status === FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully) {\n          resolve({ sessionResult, networkResponseStatus });\n        } else {\n          reject(new Error('Liveness check failed or was cancelled'));\n        }\n      };\n    });\n\n    // Create and start the LivenessCheckProcessor\n    console.log('🔍 Creating LivenessCheckProcessor...');\n    const LivenessCheckProcessor = require('./processors/LivenessCheckProcessor');\n    const processor = new LivenessCheckProcessor(\n      faceTecResponse.data.sessionFaceTec,\n      controller,\n      additionalHeaders['X-Device-Key'],\n      additionalHeaders\n    );\n\n    // Wait for the liveness check to complete\n    console.log('⏳ Waiting for liveness check to complete...');\n    const livenessResult = await livenessPromise;\n\n    // Process the result\n    const result = {\n      success: processor.isSuccess(),\n      liveness: {\n        sessionId: livenessResult.sessionResult?.sessionId || `liveness_${Date.now()}`,\n        livenessScore: 1.0, // FaceTec doesn't provide a score, but it's either pass or fail\n        isLive: processor.isSuccess(),\n        confidence: processor.isSuccess() ? 'high' : 'low',\n        timestamp: new Date().toISOString()\n      },\n      sessionId,\n      deviceId,\n      faceTecInitialized: true,\n      rawResult: livenessResult\n    };\n\n    console.log('✅ Liveness check completed successfully:', result.success);\n\n    // Call callback if provided\n    if (livenessCheckCallback && typeof livenessCheckCallback === 'function') {\n      livenessCheckCallback({\n        responseCode: result.success ? 'CUS-KYC-1000' : 'ERROR',\n        responseDescription: result.success ? 'Liveness check successful' : 'Liveness check failed',\n        success: result.success,\n        data: result\n      });\n    }\n\n    return result;\n  } catch (error) {\n    console.error('❌ Error performing liveness check:', error);\n    const errorResult = {\n      success: false,\n      error: error.message || 'Failed to perform liveness check'\n    };\n\n    if (livenessCheckCallback && typeof livenessCheckCallback === 'function') {\n      livenessCheckCallback({\n        responseCode: 'ERROR',\n        responseDescription: error.message || 'Failed to perform liveness check',\n        success: false,\n        error: error.message\n      });\n    }\n\n    throw error;\n  }\n};\n\n// Export the functions\nmodule.exports = {\n  formatCurrency,\n  greet,\n  getSessionToken,\n  getStoredEkycToken,\n  clearEkycToken,\n  getFaceTecSessionTokenWithEkycToken,\n  performPhotoIDScan,\n  // New 5 main SDK functions\n  initEkyc,\n  ocrIdCard,\n  ocrIdCardVerifyByFace,\n  ndidVerification,\n  livenessCheck\n};\n", "// Import services\nconst FaceTecService = require('./infrastructure/services/FaceTecService');\n\n// Create instance of the service\nconst faceTecService = new FaceTecService();\n\n/**\n * Load the FaceTecSDK\n * @returns {Promise<Object>} - A promise that resolves to the FaceTecSDK\n */\nconst loadFaceTecSDK = () => {\n  return faceTecService.loadFaceTecSDK();\n};\n\n/**\n * Initialize the FaceTecSDK\n * @param {string} deviceKeyIdentifier - The device key identifier\n * @param {string} publicEncryptionKey - The public encryption key\n * @returns {Promise<boolean>} - A promise that resolves to true if initialization was successful\n */\nconst initializeFaceTec = (deviceKeyIdentifier, publicEncryptionKey) => {\n  return faceTecService.initializeFaceTec(deviceKeyIdentifier, publicEncryptionKey);\n};\n\n/**\n * Get the FaceTecSDK version\n * @returns {Promise<string>} - A promise that resolves to the FaceTecSDK version\n */\nconst getFaceTecVersion = () => {\n  return faceTecService.getFaceTecVersion();\n};\n\n// Export the functions\nmodule.exports = {\n  loadFaceTecSDK,\n  initializeFaceTec,\n  getFaceTecVersion\n};\n", "const UuidGenerator = require('./UuidGenerator');\nconst TokenStorage = require('./TokenStorage');\n\nmodule.exports = {\n  UuidGenerator,\n  TokenStorage\n};\n", "const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');\n\n/**\n * Data source for ID Scan API operations\n * Handles direct API communication for ID scanning functionality\n */\nclass IDScanDataSource {\n    constructor() {\n        this.baseUrl = '/api'; // Use Next.js API routes as proxy\n    }\n\n    /**\n     * Post ID scan data to the backend API\n     * @param {Object} scanData - The scan data including images and metadata\n     * @param {Object} headers - Additional headers for the request\n     * @param {Function} onProgress - Progress callback function\n     * @returns {Promise<Object>} - API response\n     */\n    async postIDScanOnly(scanData, headers = {}, onProgress = null) {\n        return new Promise((resolve, reject) => {\n            const startTime = performance.now();\n            \n            try {\n                const url = `${this.baseUrl}/idscan-only`;\n                DeveloperStatusMessages.logApiCall(url, 'POST', 'Starting request');\n                \n                // Create XMLHttpRequest for progress tracking\n                const xhr = new XMLHttpRequest();\n                \n                // Set up progress tracking\n                if (onProgress && typeof onProgress === 'function') {\n                    xhr.upload.onprogress = function(event) {\n                        if (event.lengthComputable) {\n                            const progress = event.loaded / event.total;\n                            DeveloperStatusMessages.logIDScanProgress('Uploading', progress);\n                            onProgress(progress);\n                        }\n                    };\n                }\n\n                // Set up response handlers\n                xhr.onreadystatechange = function() {\n                    if (xhr.readyState === XMLHttpRequest.DONE) {\n                        const endTime = performance.now();\n                        \n                        try {\n                            if (xhr.status >= 200 && xhr.status < 300) {\n                                const responseData = JSON.parse(xhr.responseText);\n                                DeveloperStatusMessages.logPerformance('IDScanDataSource.postIDScanOnly', startTime, endTime);\n                                DeveloperStatusMessages.logApiCall(url, 'POST', `Success (${xhr.status})`);\n                                DeveloperStatusMessages.logData('API Response', {\n                                    status: xhr.status,\n                                    wasProcessed: responseData.wasProcessed,\n                                    error: responseData.error,\n                                    hasScanResultBlob: !!responseData.scanResultBlob\n                                });\n                                resolve(responseData);\n                            } else {\n                                DeveloperStatusMessages.logPerformance('IDScanDataSource.postIDScanOnly (failed)', startTime, endTime);\n                                DeveloperStatusMessages.logError(`API call failed with status ${xhr.status}`);\n                                reject(new Error(`HTTP error! status: ${xhr.status}`));\n                            }\n                        } catch (parseError) {\n                            DeveloperStatusMessages.logError('Failed to parse API response', parseError);\n                            reject(new Error('Failed to parse response JSON'));\n                        }\n                    }\n                };\n\n                xhr.onerror = function() {\n                    const endTime = performance.now();\n                    DeveloperStatusMessages.logPerformance('IDScanDataSource.postIDScanOnly (network error)', startTime, endTime);\n                    DeveloperStatusMessages.logError('Network request failed');\n                    reject(new Error('Network request failed'));\n                };\n\n                // Open the request\n                xhr.open('POST', url);\n\n                // Set headers\n                xhr.setRequestHeader('Content-Type', 'application/json');\n                Object.keys(headers).forEach(key => {\n                    if (headers[key] !== undefined) {\n                        xhr.setRequestHeader(key, headers[key]);\n                    }\n                });\n\n                // Send the request\n                const jsonData = JSON.stringify(scanData);\n                DeveloperStatusMessages.logMessage(`Sending request to ${url} with ${Object.keys(scanData).length} data fields`);\n                xhr.send(jsonData);\n\n            } catch (error) {\n                DeveloperStatusMessages.logError('IDScanDataSource - postIDScanOnly error', error);\n                reject(error);\n            }\n        });\n    }\n}\n\nmodule.exports = IDScanDataSource; ", "const UuidGenerator = require('../../infrastructure/utils/UuidGenerator');\nconst { TokenStorage } = require('../../infrastructure/utils');\n\n/**\n * AuthApiDataSource\n * Data source for authentication API\n */\nclass AuthApiDataSource {\n  /**\n   * Get a session token from the API\n   * @param {Object} headers - Optional headers to include in the request\n   * @returns {Promise<Object>} - A promise that resolves to the API response\n   */\n  async getSessionToken(headers = {}) {\n    try {\n      // Use the Next.js API route to proxy the request\n      const url = '/api/session-token';\n\n      // Generate UUIDs for headers if not provided\n      const deviceId = headers['X-Ekyc-Device-Info'] ? null : UuidGenerator.getDeviceId();\n      // Use getUniqueId() to generate a new UUID every time for session-related IDs\n      const tId = UuidGenerator.getUniqueId();\n      // Use provided session ID or generate a new one and store it\n      const sessionId = headers['X-Session-Id'] || UuidGenerator.getUniqueId();\n      // Store the session ID for future use\n      if (!headers['X-Session-Id']) {\n        TokenStorage.storeSessionId(sessionId);\n      }\n\n      const correlationid = UuidGenerator.getUniqueId();\n\n      // Merge default headers with any additional headers\n      const requestHeaders = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'X-Ekyc-Sdk-Version': '1.0.0',\n        'X-Ekyc-Device-Info': `browser|${deviceId}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,\n        'X-Session-Id': `${sessionId}`,\n        'X-Tid': `${tId}`,\n        'correlationid': `${correlationid}`,\n        ...headers\n      };\n\n      // Only add Authorization header if it exists and is not undefined\n      if (headers['Authorization']) {\n        requestHeaders['Authorization'] = headers['Authorization'];\n      }\n\n      // Make the GET request with headers only (no body)\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: requestHeaders,\n      });\n\n      // Check if the response is ok (status in the range 200-299)\n      if (!response.ok) {\n        throw new Error(`API request failed with status ${response.status}`);\n      }\n\n      // Parse and return the JSON response\n      return await response.json();\n    } catch (error) {\n      console.error('Error getting session token:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a FaceTec session token from the API\n   * @param {Object} headers - Optional headers to include in the request\n   * @returns {Promise<Object>} - A promise that resolves to the API response\n   */\n  async getFaceTecSessionToken(headers = {}) {\n    try {\n      // Use the Next.js API route to proxy the request\n      const url = '/api/facetec-session-token';\n\n      // Generate UUIDs for headers if not provided\n      const deviceId = headers['X-Ekyc-Device-Info'] ? null : UuidGenerator.getDeviceId();\n      // Use getUniqueId() to generate a new UUID every time for session-related IDs\n      const tId = UuidGenerator.getUniqueId();\n      // Use the stored session ID if available, or the one provided in headers, or generate a new one\n      const storedSessionId = TokenStorage.getSessionId();\n      const sessionId = headers['X-Session-Id'] || storedSessionId || UuidGenerator.getUniqueId();\n      const correlationid = UuidGenerator.getUniqueId();\n\n      // Merge default headers with any additional headers\n      const requestHeaders = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'X-Ekyc-Sdk-Version': '1.0.0',\n        'X-Ekyc-Device-Info': `browser|${deviceId}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,\n        'X-Session-Id': `${sessionId}`,\n        'X-Tid': `${tId}`,\n        'correlationid': `${correlationid}`,\n        ...headers\n      };\n\n      // Only add Authorization header if it exists and is not undefined\n      if (headers['Authorization']) {\n        requestHeaders['Authorization'] = headers['Authorization'];\n      }\n\n      // Make the GET request with headers only (no body)\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: requestHeaders,\n      });\n\n      // Check if the response is ok (status in the range 200-299)\n      if (!response.ok) {\n        throw new Error(`API request failed with status ${response.status}`);\n      }\n\n      // Parse and return the JSON response\n      return await response.json();\n    } catch (error) {\n      console.error('Error getting FaceTec session token:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a FaceTec session token from the API using the stored eKYC token\n   * @param {Object} headers - Optional headers to include in the request\n   * @returns {Promise<Object>} - A promise that resolves to the API response\n   */\n  async getFaceTecSessionTokenWithEkycToken(headers = {}) {\n    try {\n      // Use the Next.js API route to proxy the request\n      const url = '/api/facetec-session-token';\n\n      // Get the stored eKYC token\n      const ekycToken = TokenStorage.getEkycToken();\n      if (!ekycToken) {\n        throw new Error('No eKYC token found. Please get a session token first.');\n      }\n\n      // Generate UUIDs for headers if not provided\n      const deviceId = headers['X-Ekyc-Device-Info'] ? null : UuidGenerator.getDeviceId();\n      // Use getUniqueId() to generate a new UUID every time for session-related IDs\n      const tId = headers['X-Tid'] || UuidGenerator.getUniqueId();\n      // Use the stored session ID if available, or the one provided in headers, or generate a new one\n      const storedSessionId = TokenStorage.getSessionId();\n      const sessionId = headers['X-Session-Id'] || storedSessionId || UuidGenerator.getUniqueId();\n      const correlationid = headers['correlationid'] || UuidGenerator.getUniqueId();\n\n      // Merge default headers with any additional headers\n      const requestHeaders = {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n        'X-Ekyc-Sdk-Version': headers['X-Ekyc-Sdk-Version'] || '1.0.0',\n        'X-Ekyc-Device-Info': headers['X-Ekyc-Device-Info'] || `browser|${deviceId}`,\n        'X-Session-Id': `${sessionId}`,\n        'X-Tid': `${tId}`,\n        'correlationid': `${correlationid}`,\n        'X-Ekyc-Token': ekycToken,\n        ...headers\n      };\n\n      // Only add Authorization header if it exists and is not undefined\n      if (headers['Authorization']) {\n        requestHeaders['Authorization'] = headers['Authorization'];\n      }\n      // Make the GET request with headers only (no body)\n      const response = await fetch(url, {\n        method: 'GET',\n        headers: requestHeaders,\n      });\n\n      // Check if the response is ok (status in the range 200-299)\n      if (!response.ok) {\n        throw new Error(`API request failed with status ${response.status}`);\n      }\n\n      // Parse and return the JSON response\n      return await response.json();\n    } catch (error) {\n      console.error('Error getting FaceTec session token with eKYC token:', error);\n      throw error;\n    }\n  }\n}\n\nmodule.exports = AuthApiDataSource;\n", "const Currency = require('../entities/Currency');\n\n/**\n * FormatCurrencyUseCase\n * Use case for formatting a currency amount\n */\nclass FormatCurrencyUseCase {\n  /**\n   * Execute the use case\n   * @param {number} amount - The amount to format\n   * @param {string} currencyCode - The currency code (e.g., 'USD')\n   * @returns {string} - The formatted currency amount\n   */\n  execute(amount, currencyCode = 'USD') {\n    const currency = new Currency(amount, currencyCode);\n    return currency.format();\n  }\n}\n\nmodule.exports = FormatCurrencyUseCase;\n", "/**\n * Utility class for generating UUIDs\n */\nclass UuidGenerator {\n  /**\n   * Generate a UUID v4\n   * @returns {string} - A UUID v4 string\n   */\n  static generateUuid() {\n    // Implementation of UUID v4 generation\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      const r = Math.random() * 16 | 0;\n      const v = c === 'x' ? r : (r & 0x3 | 0x8);\n      return v.toString(16);\n    });\n  }\n\n  /**\n   * Get a device identifier\n   * This will generate a UUID and store it in localStorage if running in a browser\n   * If the UUID already exists in localStorage, it will be reused\n   * @returns {string} - A device identifier\n   */\n  static getDeviceId() {\n    if (typeof window !== 'undefined' && window.localStorage) {\n      // Running in browser, check localStorage\n      let deviceId = localStorage.getItem('ekyc_device_id');\n      if (!deviceId) {\n        // Generate a new UUID if not found in localStorage\n        deviceId = this.generateUuid();\n        localStorage.setItem('ekyc_device_id', deviceId);\n      }\n      return deviceId;\n    } else {\n      // Not running in browser, generate a new UUID\n      return this.generateUuid();\n    }\n  }\n\n  /**\n   * Generate a new UUID every time\n   * Unlike getDeviceId, this does not store or reuse UUIDs\n   * @returns {string} - A new UUID v4 string\n   */\n  static getUniqueId() {\n    return this.generateUuid();\n  }\n}\n\nmodule.exports = UuidGenerator;\n", "/**\n * Utility class for storing and retrieving tokens\n */\nclass TokenStorage {\n  /**\n   * Store a token in localStorage\n   * @param {string} key - The key to store the token under\n   * @param {string} token - The token to store\n   * @returns {boolean} - True if the token was stored successfully\n   */\n  static storeToken(key, token) {\n    if (typeof window !== 'undefined' && window.localStorage && token) {\n      try {\n        localStorage.setItem(key, token);\n        return true;\n      } catch (error) {\n        console.error('Error storing token:', error);\n        return false;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Retrieve a token from localStorage\n   * @param {string} key - The key the token is stored under\n   * @returns {string|null} - The token or null if not found\n   */\n  static getToken(key) {\n    if (typeof window !== 'undefined' && window.localStorage) {\n      return localStorage.getItem(key);\n    }\n    return null;\n  }\n\n  /**\n   * Remove a token from localStorage\n   * @param {string} key - The key the token is stored under\n   * @returns {boolean} - True if the token was removed successfully\n   */\n  static removeToken(key) {\n    if (typeof window !== 'undefined' && window.localStorage) {\n      try {\n        localStorage.removeItem(key);\n        return true;\n      } catch (error) {\n        console.error('Error removing token:', error);\n        return false;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Store the eKYC token\n   * @param {string} token - The eKYC token to store\n   * @returns {boolean} - True if the token was stored successfully\n   */\n  static storeEkycToken(token) {\n    return this.storeToken('ekyc_token', token);\n  }\n\n  /**\n   * Get the stored eKYC token\n   * @returns {string|null} - The eKYC token or null if not found\n   */\n  static getEkycToken() {\n    return this.getToken('ekyc_token');\n  }\n\n  /**\n   * Remove the stored eKYC token\n   * @returns {boolean} - True if the token was removed successfully\n   */\n  static removeEkycToken() {\n    return this.removeToken('ekyc_token');\n  }\n\n  /**\n   * Store the session ID\n   * @param {string} sessionId - The session ID to store\n   * @returns {boolean} - True if the session ID was stored successfully\n   */\n  static storeSessionId(sessionId) {\n    return this.storeToken('ekyc_session_id', sessionId);\n  }\n\n  /**\n   * Get the stored session ID\n   * @returns {string|null} - The session ID or null if not found\n   */\n  static getSessionId() {\n    return this.getToken('ekyc_session_id');\n  }\n\n  /**\n   * Remove the stored session ID\n   * @returns {boolean} - True if the session ID was removed successfully\n   */\n  static removeSessionId() {\n    return this.removeToken('ekyc_session_id');\n  }\n}\n\nmodule.exports = TokenStorage;\n", "/**\n * FaceTecService\n * Service for interacting with the FaceTec SDK\n */\nclass FaceTecService {\n  /**\n   * Load the FaceTecSDK\n   * @returns {Promise<Object>} - A promise that resolves to the FaceTecSDK\n   */\n  loadFaceTecSDK() {\n    return new Promise((resolve, reject) => {\n      // Check if FaceTecSDK is already loaded\n      if (typeof window !== 'undefined' && window.FaceTecSDK) {\n        resolve(window.FaceTecSDK);\n        return;\n      }\n\n      // Create a script element to load the FaceTecSDK\n      const script = document.createElement('script');\n      script.src = '/core-sdk/FaceTecSDK.js/FaceTecSDK.js'; // Path to the FaceTecSDK script\n      script.async = true;\n      script.onload = () => {\n        if (window.FaceTecSDK) {\n          resolve(window.FaceTecSDK);\n        } else {\n          reject(new Error('FaceTecSDK not found after loading script'));\n        }\n      };\n      script.onerror = () => {\n        reject(new Error('Failed to load FaceTecSDK script'));\n      };\n\n      // Add the script to the document\n      document.head.appendChild(script);\n    });\n  }\n\n  /**\n   * Initialize the FaceTecSDK\n   * @param {string} deviceKeyIdentifier - The device key identifier\n   * @param {string} publicEncryptionKey - The public encryption key\n   * @returns {Promise<boolean>} - A promise that resolves to true if initialization was successful\n   */\n  async initializeFaceTec(deviceKeyIdentifier, publicEncryptionKey) {\n    try {\n      const FaceTecSDK = await this.loadFaceTecSDK();\n      FaceTecSDK.setResourceDirectory(\"/core-sdk/FaceTecSDK.js/resources\");\n      FaceTecSDK.setImagesDirectory(\"/core-sdk/FaceTec_images\");\n      return new Promise((resolve, reject) => {\n        FaceTecSDK.initializeInDevelopmentMode(\n          deviceKeyIdentifier,\n          publicEncryptionKey,\n          (initializedSuccessfully) => {\n            if (initializedSuccessfully) {\n              console.log('FaceTecSDK initialized successfully');\n              resolve(true);\n            } else {\n              console.error('FaceTecSDK failed to initialize');\n              reject(new Error('FaceTecSDK failed to initialize'));\n            }\n          }\n        );\n      });\n    } catch (error) {\n      console.error('Error loading FaceTecSDK:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get the FaceTecSDK version\n   * @returns {Promise<string>} - A promise that resolves to the FaceTecSDK version\n   */\n  async getFaceTecVersion() {\n    try {\n      const FaceTecSDK = await this.loadFaceTecSDK();\n      return FaceTecSDK.version();\n    } catch (error) {\n      console.error('Error getting FaceTecSDK version:', error);\n      throw error;\n    }\n  }\n}\n\nmodule.exports = FaceTecService;\n", "/**\n * Developer Status Messages Utility\n * Provides logging and status tracking functionality for development purposes\n */\nclass DeveloperStatusMessages {\n    \n    /**\n     * Log a message with timestamp\n     * @param {string} message - The message to log\n     * @param {string} level - Log level (info, warn, error, success)\n     */\n    static logMessage(message, level = 'info') {\n        const timestamp = new Date().toISOString();\n        const prefix = `[${timestamp}] [${level.toUpperCase()}]`;\n        \n        switch (level) {\n            case 'error':\n                console.error(`${prefix} ${message}`);\n                break;\n            case 'warn':\n                console.warn(`${prefix} ${message}`);\n                break;\n            case 'success':\n                console.log(`%c${prefix} ${message}`, 'color: green; font-weight: bold;');\n                break;\n            case 'info':\n            default:\n                console.log(`${prefix} ${message}`);\n                break;\n        }\n    }\n\n    /**\n     * Log FaceTec SDK status\n     * @param {string} status - The status message\n     */\n    static logFaceTecStatus(status) {\n        this.logMessage(`FaceTec SDK: ${status}`, 'info');\n    }\n\n    /**\n     * Log API call status\n     * @param {string} endpoint - API endpoint\n     * @param {string} method - HTTP method\n     * @param {string} status - Status message\n     */\n    static logApiCall(endpoint, method, status) {\n        this.logMessage(`API ${method} ${endpoint}: ${status}`, 'info');\n    }\n\n    /**\n     * Log success message\n     * @param {string} message - Success message\n     */\n    static logSuccess(message) {\n        this.logMessage(message, 'success');\n    }\n\n    /**\n     * Log error message\n     * @param {string} message - Error message\n     * @param {Error} error - Error object (optional)\n     */\n    static logError(message, error = null) {\n        let fullMessage = message;\n        if (error) {\n            fullMessage += ` - ${error.message}`;\n        }\n        this.logMessage(fullMessage, 'error');\n        \n        if (error && error.stack) {\n            console.error('Stack trace:', error.stack);\n        }\n    }\n\n    /**\n     * Log warning message\n     * @param {string} message - Warning message\n     */\n    static logWarning(message) {\n        this.logMessage(message, 'warn');\n    }\n\n    /**\n     * Log ID scan progress\n     * @param {string} step - Current step\n     * @param {number} progress - Progress percentage (0-100)\n     */\n    static logIDScanProgress(step, progress = null) {\n        let message = `ID Scan: ${step}`;\n        if (progress !== null) {\n            message += ` (${Math.round(progress * 100)}%)`;\n        }\n        this.logMessage(message, 'info');\n    }\n\n    /**\n     * Log liveness check progress\n     * @param {string} step - Current step\n     * @param {number} progress - Progress percentage (0-1)\n     */\n    static logLivenessProgress(step, progress = null) {\n        let message = `Liveness Check: ${step}`;\n        if (progress !== null) {\n            message += ` (${Math.round(progress * 100)}%)`;\n        }\n        this.logMessage(message, 'info');\n    }\n\n    /**\n     * Log session information\n     * @param {string} sessionId - Session ID\n     * @param {string} action - Action being performed\n     */\n    static logSession(sessionId, action) {\n        this.logMessage(`Session ${sessionId}: ${action}`, 'info');\n    }\n\n    /**\n     * Clear console (for development)\n     */\n    static clearConsole() {\n        if (typeof console.clear === 'function') {\n            console.clear();\n        }\n    }\n\n    /**\n     * Log object/data for debugging\n     * @param {string} label - Label for the data\n     * @param {any} data - Data to log\n     */\n    static logData(label, data) {\n        console.group(`📊 ${label}`);\n        console.log(data);\n        console.groupEnd();\n    }\n\n    /**\n     * Log performance timing\n     * @param {string} operation - Operation name\n     * @param {number} startTime - Start time (performance.now())\n     * @param {number} endTime - End time (performance.now())\n     */\n    static logPerformance(operation, startTime, endTime) {\n        const duration = endTime - startTime;\n        this.logMessage(`Performance: ${operation} took ${duration.toFixed(2)}ms`, 'info');\n    }\n}\n\nmodule.exports = DeveloperStatusMessages; ", "const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');\n\n/**\n * Data source for Liveness Check API operations\n * Handles direct API communication for liveness check functionality\n */\nclass LivenessCheckDataSource {\n    constructor() {\n        this.baseUrl = '/api'; // Use Next.js API routes as proxy\n    }\n\n    /**\n     * Post liveness check data to the backend API\n     * @param {Object} livenessData - The liveness data including face scan and audit trail\n     * @param {Object} headers - Additional headers for the request\n     * @param {Function} onProgress - Progress callback function\n     * @returns {Promise<Object>} - API response\n     */\n    async postLivenessCheck(livenessData, headers = {}, onProgress = null) {\n        return new Promise((resolve, reject) => {\n            const startTime = performance.now();\n            \n            try {\n                const url = `${this.baseUrl}/enrollment-3d`;\n\n                DeveloperStatusMessages.logApiCall(url, 'POST', 'Starting request');\n                \n                // Create XMLHttpRequest for progress tracking\n                const xhr = new XMLHttpRequest();\n                \n                // Set up progress tracking\n                if (onProgress && typeof onProgress === 'function') {\n                    xhr.upload.onprogress = function(event) {\n                        if (event.lengthComputable) {\n                            const progress = event.loaded / event.total;\n                            DeveloperStatusMessages.logLivenessProgress('Uploading', progress);\n                            onProgress(progress);\n                        }\n                    };\n                }\n\n                // Set up response handlers\n                xhr.onreadystatechange = function() {\n                    if (xhr.readyState === XMLHttpRequest.DONE) {\n                        const endTime = performance.now();\n                        \n                        try {\n                            if (xhr.status >= 200 && xhr.status < 300) {\n                                const responseData = JSON.parse(xhr.responseText);\n                                DeveloperStatusMessages.logPerformance('LivenessCheckDataSource.postLivenessCheck', startTime, endTime);\n                                DeveloperStatusMessages.logApiCall(url, 'POST', `Success (${xhr.status})`);\n                                DeveloperStatusMessages.logData('API Response', {\n                                    status: xhr.status,\n                                    wasProcessed: responseData.wasProcessed,\n                                    error: responseData.error,\n                                    hasScanResultBlob: !!responseData.scanResultBlob\n                                });\n                                resolve(responseData);\n                            } else {\n                                DeveloperStatusMessages.logPerformance('LivenessCheckDataSource.postLivenessCheck (failed)', startTime, endTime);\n                                DeveloperStatusMessages.logError(`API call failed with status ${xhr.status}`);\n                                reject(new Error(`HTTP error! status: ${xhr.status}`));\n                            }\n                        } catch (error) {\n                            DeveloperStatusMessages.logError('Error parsing response', error);\n                            reject(error);\n                        }\n                    }\n                };\n                \n                // Set up error handler\n                xhr.onerror = function() {\n                    DeveloperStatusMessages.logError('Network error occurred');\n                    reject(new Error('Network error occurred'));\n                };\n                \n                // Open and send the request\n                xhr.open('POST', url, true);\n                \n                // Set headers\n                xhr.setRequestHeader('Content-Type', 'application/json');\n                for (const [key, value] of Object.entries(headers)) {\n                    if (value !== undefined && value !== null) {\n                        xhr.setRequestHeader(key, value);\n                    }\n                }\n                \n                // Send the request\n                const requestBody = JSON.stringify(livenessData);\n                DeveloperStatusMessages.logData('Request Body Keys', Object.keys(livenessData));\n                xhr.send(requestBody);\n            } catch (error) {\n                DeveloperStatusMessages.logError('Error in postLivenessCheck', error);\n                reject(error);\n            }\n        });\n    }\n}\n\nmodule.exports = LivenessCheckDataSource;\n", "/**\n * GreetUseCase\n * Use case for generating a greeting message\n */\nclass GreetUseCase {\n  /**\n   * Execute the use case\n   * @param {string} name - The name to greet\n   * @returns {string} - The greeting message\n   */\n  execute(name) {\n    return `Hello 12, ${name}!`;\n  }\n}\n\nmodule.exports = GreetUseCase;\n", "//\n// Welcome to the annotated FaceTec Device SDK core code for performing a secure Photo ID Scan.\n//\n//\n// This is an example self-contained class to perform Photo ID Scans with the FaceTec SDK.\n// You may choose to further componentize parts of this in your own Apps based on your specific requirements.\n//\n\nconst PostIDScanOnlyUseCase = require('../domain/usecases/PostIDScanOnlyUseCase');\nconst DeveloperStatusMessages = require('../infrastructure/utils/DeveloperStatusMessages');\n\nvar PhotoIDScanProcessor = /** @class */ (function () {\n    function PhotoIDScanProcessor(sessionToken, sampleAppControllerReference, deviceKey, additionalHeaders) {\n        var _this = this;\n        this.latestNetworkRequest = new XMLHttpRequest();\n        this.deviceKey = deviceKey || null; // Store the device key\n        this.additionalHeaders = additionalHeaders || {}; // get additional headers\n        this.postIDScanOnlyUseCase = new PostIDScanOnlyUseCase(); // Initialize use case\n        \n        //\n        // Part 2:  Handling the Result of a Photo ID Scan\n        //\n        this.processIDScanResultWhileFaceTecSDKWaits = function (idScanResult, idScanResultCallback) {\n            _this.latestIDScanResult = idScanResult;\n            //\n            // Part 3:  Handles early exit scenarios where there is no Photo ID Scan result to handle -- i.e. User Cancellation, Timeouts, etc.\n            //\n            if (idScanResult.status !== FaceTecSDK.FaceTecIDScanStatus.Success) {\n                _this.latestNetworkRequest.abort();\n                _this.latestNetworkRequest = new XMLHttpRequest();\n                idScanResultCallback.cancel();\n                return;\n            }\n            // IMPORTANT:  FaceTecSDK.FaceTecIDScanStatus.Success DOES NOT mean the Photo ID Scan was Successful.\n            // It simply means the User completed the Session.  You still need to perform the Photo ID Scan result checks on your Servers.\n            \n            //\n            // Part 4: Use Clean Architecture - Call UseCase instead of direct API\n            //\n            _this.executeIDScanUseCase(idScanResult, idScanResultCallback);\n        };\n\n        //\n        // New method: Execute ID Scan using Clean Architecture UseCase\n        //\n        this.executeIDScanUseCase = async function(idScanResult, idScanResultCallback) {\n            try {\n                // Create progress callback for upload tracking\n                const onProgress = function(progress) {\n                    idScanResultCallback.uploadProgress(progress);\n                };\n\n                // Execute the use case\n                const result = await _this.postIDScanOnlyUseCase.execute({\n                    idScanResult: idScanResult,\n                    deviceKey: _this.deviceKey,\n                    additionalHeaders: _this.additionalHeaders,\n                    onProgress: onProgress\n                });\n\n                // Handle the result\n                if (result.success) {\n                    // Configure success messages\n                    FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(\n                        \"Front Scan Complete\", // Successful scan of ID front-side (ID Types with no back-side).\n                        \"Front of ID<br/>Scanned\", // Successful scan of ID front-side (ID Types that do have a back-side).\n                        \"ID Scan Complete\", // Successful scan of the ID back-side.\n                        \"Passport Scan Complete\", // Successful scan of a Passport\n                        \"Photo ID Scan<br/>Complete\", // Successful upload of final Photo ID Scan result containing User-Confirmed ID Text.\n                        \"ID Photo Capture<br/>Complete\", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.\n                        \"Face Didn't Match<br/>Highly Enough\", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.\n                        \"ID Document<br/>Not Fully Visible\", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.\n                        \"ID Text Not Legible\", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.\n                        \"ID Type Mismatch<br/>Please Try Again\" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.\n                    );\n                    \n                    // Proceed to next step with scanResultBlob\n                    idScanResultCallback.proceedToNextStep(result.scanResultBlob);\n                } else {\n                    // Handle error case\n                    _this.cancelDueToNetworkError(result.errorMessage || \"Unexpected API response, cancelling out.\", idScanResultCallback);\n                }\n            } catch (error) {\n                console.error('PhotoIDScanProcessor - executeIDScanUseCase error:', error);\n                _this.cancelDueToNetworkError(error.message || \"Exception while handling API response, cancelling out.\", idScanResultCallback);\n            }\n        };\n\n        //\n        // Part 9:  This function gets called after the FaceTec SDK is completely done.  There are no parameters because you have already been passed all data in the processSessionWhileFaceTecSDKWaits function and have already handled all of your own results.\n        //\n        this.onFaceTecSDKCompletelyDone = function () {\n            //\n            // DEVELOPER NOTE:  onFaceTecSDKCompletelyDone() is called after the Session has completed or you signal the FaceTec SDK with cancel().\n            // Calling a custom function on the Sample App Controller is done for demonstration purposes to show you that here is where you get control back from the FaceTec SDK.\n            //\n            // If the Photo ID Scan was processed get the success result from isCompletelyDone\n            if (_this.latestIDScanResult !== null) {\n                _this.success = _this.latestIDScanResult.isCompletelyDone;\n            }\n            // Log success message\n            if (_this.success) {\n                DeveloperStatusMessages.logMessage(\"Id Scan Complete\");\n            }\n            _this.sampleAppControllerReference.onComplete(null, _this.latestIDScanResult, 200); // Use 200 as default status\n        };\n\n        // Helper function to ensure the session is only cancelled once\n        this.cancelDueToNetworkError = function (networkErrorMessage, faceTecIdScanResultCallback) {\n            if (_this.cancelledDueToNetworkError === false) {\n                console.error(networkErrorMessage);\n                _this.cancelledDueToNetworkError = true;\n                faceTecIdScanResultCallback.cancel();\n            }\n        };\n\n        //\n        // DEVELOPER NOTE:  This public convenience method is for demonstration purposes only so the Sample App can get information about what is happening in the processor.\n        // In your code, you may not even want or need to do this.\n        //\n        this.isSuccess = function () {\n            return _this.success;\n        };\n\n        //\n        // DEVELOPER NOTE:  These properties are for demonstration purposes only so the Sample App can get information about what is happening in the processor.\n        // In the code in your own App, you can pass around signals, flags, intermediates, and results however you would like.\n        //\n        this.success = false;\n        this.sampleAppControllerReference = sampleAppControllerReference;\n        this.latestIDScanResult = null;\n        this.cancelledDueToNetworkError = false;\n\n        // In v9.2.2+, configure the messages that will be displayed to the User in each of the possible cases.\n        // Based on the internal processing and decision logic about how the flow gets advanced, the FaceTec SDK will use the appropriate, configured message.\n        FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides(\n            \"Uploading<br/>Encrypted<br/>ID Scan\", // Upload of ID front-side has started.\n            \"Still Uploading...<br/>Slow Connection\", // Upload of ID front-side is still uploading to Server after an extended period of time.\n            \"Upload Complete\", // Upload of ID front-side to the Server is complete.\n            \"Processing<br/>ID Scan\", // Upload of ID front-side is complete and we are waiting for the Server to finish processing and respond.\n            \"Uploading<br/>Encrypted<br/>Back of ID\", // Upload of ID back-side has started.\n            \"Still Uploading...<br/>Slow Connection\", // Upload of ID back-side is still uploading to Server after an extended period of time.\n            \"Upload Complete\", // Upload of ID back-side to Server is complete.\n            \"Processing<br/>Back of ID\", // Upload of ID back-side is complete and we are waiting for the Server to finish processing and respond.\n            \"Uploading<br/>Your Confirmed Info\", // Upload of User Confirmed Info has started.\n            \"Still Uploading...<br/>Slow Connection\", // Upload of User Confirmed Info is still uploading to Server after an extended period of time.\n            \"Info Saved\", // Upload of User Confirmed Info to the Server is complete.\n            \"Processing\" // Upload of User Confirmed Info is complete and we are waiting for the Server to finish processing and respond.\n        );\n\n        //\n        // Part 1:  Starting the FaceTec Photo ID Scan Session\n        //\n        // Required parameters:\n        // - FaceTecIDScanProcessor:  A class that implements FaceTecIDScanProcessor, which handles the Photo ID Scan when the User completes a Session.  In this example, \"this\" implements the class.\n        // - sessionToken:  A valid Session Token you just created by calling your API to get a Session Token from the Server SDK.\n        //\n        new FaceTecSDK.FaceTecSession(this, sessionToken);\n    }\n    return PhotoIDScanProcessor;\n}());\n\n// Export the PhotoIDScanProcessor class for use in Node.js/webpack environments\nmodule.exports = PhotoIDScanProcessor;\n", "/**\n * GetFaceTecSessionTokenWithEkycTokenUseCase\n * Use case for getting a FaceTec session token using the stored eKYC token\n */\nclass GetFaceTecSessionTokenWithEkycTokenUseCase {\n  /**\n   * @param {Object} authRepository - The authentication repository\n   */\n  constructor(authRepository) {\n    this.authRepository = authRepository;\n  }\n\n  /**\n   * Execute the use case\n   * @param {Object} headers - Optional headers to include in the request\n   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity\n   */\n  async execute(headers = {}) {\n    return await this.authRepository.getFaceTecSessionTokenWithEkycToken(headers);\n  }\n}\n\nmodule.exports = GetFaceTecSessionTokenWithEkycTokenUseCase;\n", "/**\n * GetSessionTokenUseCase\n * Use case for getting a session token from the authentication API\n */\nclass GetSessionTokenUseCase {\n  /**\n   * @param {Object} authRepository - The authentication repository\n   */\n  constructor(authRepository) {\n    this.authRepository = authRepository;\n  }\n\n  /**\n   * Execute the use case\n   * @param {Object} headers - Optional headers to include in the request\n   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity\n   */\n  async execute(headers = {}) {\n    return await this.authRepository.getSessionToken(headers);\n  }\n}\n\nmodule.exports = GetSessionTokenUseCase;\n"], "names": [], "sourceRoot": ""}