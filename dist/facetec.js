!function(e,c){"object"==typeof exports&&"object"==typeof module?module.exports=c():"function"==typeof define&&define.amd?define("facetec",[],c):"object"==typeof exports?exports.facetec=c():e.facetec=c()}(this,(()=>{return e={382:(e,c,o)=>{const r=new(o(706));e.exports={loadFaceTecSDK:()=>r.loadFaceTecSDK(),initializeFaceTec:(e,c)=>r.initializeFaceTec(e,c),getFaceTecVersion:()=>r.getFaceTecVersion()}},706:e=>{e.exports=class{loadFaceTecSDK(){return new Promise(((e,c)=>{if("undefined"!=typeof window&&window.FaceTecSDK)return void e(window.FaceTecSDK);const o=document.createElement("script");o.src="/core-sdk/FaceTecSDK.js/FaceTecSDK.js",o.async=!0,o.onload=()=>{window.FaceTecSDK?e(window.FaceTecSDK):c(new Error("FaceTecSDK not found after loading script"))},o.onerror=()=>{c(new Error("Failed to load FaceTecSDK script"))},document.head.appendChild(o)}))}async initializeFaceTec(e,c){try{const o=await this.loadFaceTecSDK();return o.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources"),o.setImagesDirectory("/core-sdk/FaceTec_images"),new Promise(((r,t)=>{o.initializeInDevelopmentMode(e,c,(e=>{e?(console.log("FaceTecSDK initialized successfully"),r(!0)):(console.error("FaceTecSDK failed to initialize"),t(new Error("FaceTecSDK failed to initialize")))}))}))}catch(e){throw console.error("Error loading FaceTecSDK:",e),e}}async getFaceTecVersion(){try{return(await this.loadFaceTecSDK()).version()}catch(e){throw console.error("Error getting FaceTecSDK version:",e),e}}}}},c={},function o(r){var t=c[r];if(void 0!==t)return t.exports;var i=c[r]={exports:{}};return e[r](i,i.exports,o),i.exports}(382);var e,c}));