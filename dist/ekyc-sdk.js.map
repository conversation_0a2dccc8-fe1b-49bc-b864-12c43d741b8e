{"version": 3, "file": "ekyc-sdk.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD,CAAC;AACD,O;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,EAAE,mBAAO,CAAC,GAAU;;AAEtB,QAAQ,8BAA8B,EAAE,mBAAO,CAAC,GAAwB;AACxE,uBAAuB,mBAAO,CAAC,GAAW;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,SAAS;AACtB,eAAe,iBAAiB;AAChC;AACA,oCAAoC;AACpC,YAAY,YAAY,8BAA8B;AACtD;AACA;AACA,2CAA2C,OAAO;AAClD;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,SAAS;AACtB,eAAe,iBAAiB;AAChC;AACA,2CAA2C;AAC3C,YAAY,YAAY,6BAA6B;AACrD;AACA,GAAG;;AAEH;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,kBAAkB;AACjC;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,iBAAiB;AAChC;AACA,kCAAkC;AAClC,YAAY,gDAAgD;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB,eAAe,QAAQ;AACvB,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,aAAa;AAC9B;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA,eAAe,QAAQ;AACvB,iBAAiB,SAAS;AAC1B;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,4CAA4C;AAC5C,kCAAkC,oBAAoB;AACtD,GAAG;;AAEH;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,GAAG;;AAEH,mEAAmE;AACnE;AACA;AACA;AACA;AACA,KAAK;AACL,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;AACA,6BAA6B;AAC7B;AACA,GAAG;;AAEH;AACA;AACA;AACA,8BAA8B;AAC9B;AACA,GAAG;;AAEH;AACA;AACA;AACA,0CAA0C;AAC1C;AACA,GAAG;;AAEH;AACA;AACA;AACA,qCAAqC;AACrC;AACA,GAAG;;AAEH;AACA;AACA;AACA,kCAAkC;AAClC;AACA,GAAG;;;;AAIH;AACA;AACA;AACA;;AAEA;AACA;AACA,yBAAsB;;AAEtB;AACA,IAAI,IAA8B;AAClC,EAAE,eAAe;AACjB,EAAE,YAAY;AACd,EAAE,eAAe;AACjB,EAAE,aAAa;AACf;;;;;;;UCzRA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA,kBAAkB,qBAAqB;WACvC;WACA;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC3BA;;;;;WCAA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;UEhDA;UACA;UACA;UACA;UACA", "sources": ["webpack://ScbTechXEkycSDK/webpack/universalModuleDefinition", "webpack://ScbTechXEkycSDK/./lib/sdk-entry.js", "webpack://ScbTechXEkycSDK/webpack/bootstrap", "webpack://ScbTechXEkycSDK/webpack/runtime/chunk loaded", "webpack://ScbTechXEkycSDK/webpack/runtime/hasOwnProperty shorthand", "webpack://ScbTechXEkycSDK/webpack/runtime/jsonp chunk loading", "webpack://ScbTechXEkycSDK/webpack/before-startup", "webpack://ScbTechXEkycSDK/webpack/startup", "webpack://ScbTechXEkycSDK/webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ScbTechXEkycSDK\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ScbTechXEkycSDK\"] = factory();\n\telse\n\t\troot[\"ScbTechXEkycSDK\"] = root[\"ScbTechXEkycSDK\"] || {}, root[\"ScbTechXEkycSDK\"][\"ekyc-sdk\"] = factory();\n})(this, () => {\nreturn ", "/**\n * eKYC SDK - Main Entry Point\n *\n * This is the primary entry point for external developers using the eKYC SDK by SCB TechX.\n * It provides a clean, framework-agnostic API for authentication, FaceTec integration,\n * and ID scanning functionality.\n *\n * Usage: import EkycSDK from './ekyc-sdk/ekyc-sdk.js';\n *\n * @version 1.0.0\n * <AUTHOR> TechX\n */\n\n// Import core functionality\nconst {\n  getSessionToken: getSessionTokenCore,\n  getFaceTecSessionTokenWithEkycToken: getFaceTecSessionTokenCore,\n  performPhotoIDScan: performPhotoIDScanCore,\n  getStoredEkycToken,\n  clearEkycToken,\n  // Import the 5 main SDK functions\n  initEkyc: initEkycCore,\n  ocrIdCard: ocrIdCardCore,\n  ocrIdCardVerifyByFace: ocrIdCardVerifyByFaceCore,\n  ndidVerification: ndidVerificationCore,\n  livenessCheck: livenessCheckCore\n} = require('./simple');\n\nconst { TokenStorage, UuidGenerator } = require('./infrastructure/utils');\nconst facetecService = require('./facetec');\n\n/**\n * Authentication namespace\n * Handles session tokens and authentication flow\n */\nconst Auth = {\n  /**\n   * Get a session token from the eKYC API\n   * @param {Object} options - Configuration options\n   * @param {Object} options.headers - Additional headers to include\n   * @param {string} options.apiKey - API key for authentication\n   * @param {boolean} options.storeToken - Whether to store the token (default: true)\n   * @returns {Promise<Object>} Session token response\n   */\n  async getSessionToken(options = {}) {\n    const { headers = {}, apiKey, storeToken = true } = options;\n    \n    if (apiKey) {\n      headers['Authorization'] = `Bearer ${apiKey}`;\n    }\n    \n    return await getSessionTokenCore(headers, storeToken);\n  },\n\n  /**\n   * Get a FaceTec session token using stored eKYC token\n   * @param {Object} options - Configuration options\n   * @param {Object} options.headers - Additional headers to include\n   * @param {boolean} options.initializeFaceTec - Whether to initialize FaceTec SDK (default: true)\n   * @returns {Promise<Object>} FaceTec session token response\n   */\n  async getFaceTecSessionToken(options = {}) {\n    const { headers = {}, initializeFaceTec = true } = options;\n    return await getFaceTecSessionTokenCore(headers, initializeFaceTec);\n  },\n\n  /**\n   * Get the stored eKYC token\n   * @returns {string|null} The stored token or null\n   */\n  getStoredToken() {\n    return getStoredEkycToken();\n  },\n\n  /**\n   * Clear the stored eKYC token\n   * @returns {boolean} True if cleared successfully\n   */\n  clearToken() {\n    return clearEkycToken();\n  }\n};\n\n/**\n * FaceTec namespace\n * Handles FaceTec SDK integration and ID scanning\n */\nconst FaceTec = {\n  /**\n   * Initialize FaceTec SDK\n   * @param {string} deviceKey - Device key from session token response\n   * @param {string} encryptionKey - Encryption key from session token response\n   * @returns {Promise<boolean>} True if initialized successfully\n   */\n  async initialize(deviceKey, encryptionKey) {\n    return await facetecService.initializeFaceTec(deviceKey, encryptionKey);\n  },\n\n  /**\n   * Load FaceTec SDK\n   * @returns {Promise<Object>} FaceTec SDK instance\n   */\n  async loadSDK() {\n    return await facetecService.loadFaceTecSDK();\n  },\n\n  /**\n   * Get FaceTec SDK version\n   * @returns {Promise<string>} SDK version\n   */\n  async getVersion() {\n    return await facetecService.getFaceTecVersion();\n  },\n\n  /**\n   * Perform Photo ID Scan\n   * @param {Object} options - Scan configuration\n   * @param {string} options.deviceKey - Device key for scanning\n   * @param {Object} options.sessionTokenResponse - Session token response from Auth.getFaceTecSessionToken()\n   * @param {Object} options.headers - Additional headers\n   * @returns {Promise<Object>} Scan result\n   */\n  async performIDScan(options = {}) {\n    const { deviceKey, sessionTokenResponse, headers = {} } = options;\n    \n    if (!deviceKey) {\n      throw new Error('deviceKey is required for ID scanning');\n    }\n    \n    if (!sessionTokenResponse) {\n      throw new Error('sessionTokenResponse is required for ID scanning');\n    }\n    \n    return await performPhotoIDScanCore(headers, deviceKey, sessionTokenResponse);\n  }\n};\n\n/**\n * Utilities namespace\n * Provides utility functions for UUID generation and token storage\n */\nconst Utils = {\n  /**\n   * Generate a new UUID\n   * @returns {string} UUID v4 string\n   */\n  generateUUID() {\n    return UuidGenerator.getUniqueId();\n  },\n\n  /**\n   * Get or generate a device ID (persisted in localStorage if available)\n   * @returns {string} Device ID\n   */\n  getDeviceId() {\n    return UuidGenerator.getDeviceId();\n  },\n\n  /**\n   * Token storage utilities\n   */\n  TokenStorage: {\n    /**\n     * Store a token\n     * @param {string} key - Storage key\n     * @param {string} value - Token value\n     * @returns {boolean} True if stored successfully\n     */\n    store(key, value) {\n      return TokenStorage.storeToken(key, value);\n    },\n\n    /**\n     * Get a stored token\n     * @param {string} key - Storage key\n     * @returns {string|null} Token value or null\n     */\n    get(key) {\n      return TokenStorage.getToken(key);\n    },\n\n    /**\n     * Remove a stored token\n     * @param {string} key - Storage key\n     * @returns {boolean} True if removed successfully\n     */\n    remove(key) {\n      return TokenStorage.removeToken(key);\n    }\n  }\n};\n\n/**\n * Main SDK object with clean public API\n */\nconst EkycSDK = {\n  // Namespaced APIs\n  Auth,\n  FaceTec,\n  Utils,\n\n  // Quick access methods for common operations\n  async getSessionToken(apiKey, options = {}) {\n    return Auth.getSessionToken({ apiKey, ...options });\n  },\n\n  async initializeFaceTec(sessionTokenResponse) {\n    if (!sessionTokenResponse?.data?.deviceKey || !sessionTokenResponse?.data?.encryptionKey) {\n      throw new Error('Invalid session token response: missing deviceKey or encryptionKey');\n    }\n\n    return FaceTec.initialize(\n      sessionTokenResponse.data.deviceKey,\n      sessionTokenResponse.data.encryptionKey\n    );\n  },\n\n  async performIDScan(deviceKey, sessionTokenResponse, options = {}) {\n    return FaceTec.performIDScan({\n      deviceKey,\n      sessionTokenResponse,\n      ...options\n    });\n  },\n\n  // ========================================\n  // 5 MAIN SDK ENTRY POINT FUNCTIONS\n  // ========================================\n\n  /**\n   * 1. Initialize the eKYC SDK\n   */\n  async initEkyc(options = {}) {\n    return await initEkycCore(options);\n  },\n\n  /**\n   * 2. Perform OCR ID Card scanning\n   */\n  async ocrIdCard(options = {}) {\n    return await ocrIdCardCore(options);\n  },\n\n  /**\n   * 3. OCR ID Card with facial verification\n   */\n  async ocrIdCardVerifyByFace(options = {}) {\n    return await ocrIdCardVerifyByFaceCore(options);\n  },\n\n  /**\n   * 4. NDID digital identity verification\n   */\n  async ndidVerification(options = {}) {\n    return await ndidVerificationCore(options);\n  },\n\n  /**\n   * 5. Facial liveness detection\n   */\n  async livenessCheck(options = {}) {\n    return await livenessCheckCore(options);\n  },\n\n\n\n  // SDK metadata\n  version: '1.0.0',\n  name: 'SCB TechX eKYC SDK'\n};\n\n// Export for different module systems\nmodule.exports = EkycSDK;\nmodule.exports.default = EkycSDK;\n\n// For ES6 modules\nif (typeof exports !== 'undefined') {\n  exports.EkycSDK = EkycSDK;\n  exports.Auth = Auth;\n  exports.FaceTec = FaceTec;\n  exports.Utils = Utils;\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t656: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = this[\"webpackChunkScbTechXEkycSDK\"] = this[\"webpackChunkScbTechXEkycSDK\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [347], () => (__webpack_require__(236)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": [], "sourceRoot": ""}