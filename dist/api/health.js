!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("api/health",[],e):"object"==typeof exports?exports["api/health"]=e():t["api/health"]=e()}(this,(()=>{return t={22:t=>{function e(t,e){e.status(200).json({status:"UP"})}t.exports=e,t.exports.default=e}},e={},function o(r){var p=e[r];if(void 0!==p)return p.exports;var s=e[r]={exports:{}};return t[r](s,s.exports,o),s.exports}(22);var t,e}));