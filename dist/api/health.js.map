{"version": 3, "file": "api/health.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD,CAAC;AACD,O;;;;;;ACVA;AACA;AACA;AACA,WAAW,+BAA+B;AAC1C,WAAW,gCAAgC;AAC3C;AACA;AACA;AACA,yBAAyB,cAAc;AACvC;;AAEA;AACA;AACA,yBAAsB;;;;;;;UCbtB;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;UEtBA;UACA;UACA;UACA", "sources": ["webpack://ScbTechXEkycSDK/webpack/universalModuleDefinition", "webpack://ScbTechXEkycSDK/./lib/infrastructure/api/health.js", "webpack://ScbTechXEkycSDK/webpack/bootstrap", "webpack://ScbTechXEkycSDK/webpack/before-startup", "webpack://ScbTechXEkycSDK/webpack/startup", "webpack://ScbTechXEkycSDK/webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ScbTechXEkycSDK\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ScbTechXEkycSDK\"] = factory();\n\telse\n\t\troot[\"ScbTechXEkycSDK\"] = root[\"ScbTechXEkycSDK\"] || {}, root[\"ScbTechXEkycSDK\"][\"api/health\"] = factory();\n})(this, () => {\nreturn ", "/**\n * Health check API endpoint\n *\n * @param {import('next').NextApiRequest} req\n * @param {import('next').NextApiResponse} res\n */\nfunction handler(req, res) {\n  // Return a simple health status\n  res.status(200).json({ status: 'UP' });\n}\n\n// Export for both CommonJS and ES modules\nmodule.exports = handler;\nmodule.exports.default = handler;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(22);\n", ""], "names": [], "sourceRoot": ""}