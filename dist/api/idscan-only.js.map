{"version": 3, "file": "api/idscan-only.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD,CAAC;AACD,O;;;;;;ACVA;AACA;AACA;AACA;AACA,WAAW,+BAA+B;AAC1C,WAAW,gCAAgC;AAC3C;AACA;AACA;AACA;AACA,kCAAkC,6BAA6B;AAC/D;;AAEA;AACA;AACA;AACA,mBAAmB,QAAQ;;AAE3B;AACA;AACA;AACA;AACA,0FAA0F,sBAAsB;AAChH;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA,yBAAsB;;;;;;UCpFtB;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;UEtBA;UACA;UACA;UACA", "sources": ["webpack://ScbTechXEkycSDK/webpack/universalModuleDefinition", "webpack://ScbTechXEkycSDK/./lib/infrastructure/api/idscan-only.js", "webpack://ScbTechXEkycSDK/webpack/bootstrap", "webpack://ScbTechXEkycSDK/webpack/before-startup", "webpack://ScbTechXEkycSDK/webpack/startup", "webpack://ScbTechXEkycSDK/webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ScbTechXEkycSDK\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ScbTechXEkycSDK\"] = factory();\n\telse\n\t\troot[\"ScbTechXEkycSDK\"] = root[\"ScbTechXEkycSDK\"] || {}, root[\"ScbTechXEkycSDK\"][\"api/idscan-only\"] = factory();\n})(this, () => {\nreturn ", "/**\n * API route to process ID scan data to the eKYC backend API\n * This acts as a proxy to avoid CORS issues when calling from the browser\n *\n * @param {import('next').NextApiRequest} req\n * @param {import('next').NextApiResponse} res\n */\nasync function handler(req, res) {\n  // Only allow POST requests\n  if (req.method !== 'POST') {\n    return res.status(405).json({ error: 'Method not allowed' });\n  }\n\n  try {\n    // Set the base URL - this should be configured based on environment\n    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';\n    const url = `${baseUrl}/v1/ekyc/idscan-only`;\n\n    // ✅ Forward headers follow spec\n    const headers = {\n      'Content-Type': 'application/json',\n      'Accept': 'application/json',\n      'Authorization': req.headers['authorization'] || (process.env.JWT_TOKEN ? `Bearer ${process.env.JWT_TOKEN}` : undefined),\n      'X-Device-Key': req.headers['x-device-key'],\n      'X-User-Agent': req.headers['x-user-agent'],\n      'X-Session-Id': req.headers['x-session-id'],\n      'X-Tid': req.headers['x-tid'],\n      'X-Ekyc-Token': req.headers['x-ekyc-token'],\n      'correlationid': req.headers.correlationid\n    };\n\n    // Remove undefined headers\n    Object.keys(headers).forEach(key => {\n      if (headers[key] === undefined) {\n        delete headers[key];\n      }\n    });\n\n    // Make the POST request with the body from the client\n    const response = await fetch(url, {\n      method: 'POST',\n      headers: headers,\n      body: JSON.stringify(req.body),\n    });\n\n    // Get the response data\n    const data = await response.json();\n\n    // Transform the response to match PhotoIDScanProcessor expectations\n    if (response.ok && data.code === 'CUS-KYC-1000') {\n      // Success case - return format expected by PhotoIDScanProcessor\n      const successResponse = {\n        wasProcessed: true,\n        error: false,\n        scanResultBlob: data.data?.scanResultBlob || \"\", // Use scanResultBlob from backend if available\n        originalResponse: data // Keep original response for debugging\n      };\n      \n      console.log('Returning success response with scanResultBlob length:', successResponse.scanResultBlob.length);\n      return res.status(200).json(successResponse);\n    } else {\n      // Error case - return format expected by PhotoIDScanProcessor\n      const errorResponse = {\n        wasProcessed: false,\n        error: true,\n        errorMessage: data.description || data.message || 'Unknown error',\n        originalResponse: data // Keep original response for debugging\n      };\n      \n      console.log('Returning error response:', errorResponse.errorMessage);\n      return res.status(response.status).json(errorResponse);\n    }\n  } catch (error) {\n    console.error('Error processing ID scan:', error);\n    return res.status(500).json({\n      wasProcessed: false,\n      error: true,\n      errorMessage: 'Failed to process ID scan'\n    });\n  }\n}\n\n// Export for both CommonJS and ES modules\nmodule.exports = handler;\nmodule.exports.default = handler;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(235);\n", ""], "names": [], "sourceRoot": ""}