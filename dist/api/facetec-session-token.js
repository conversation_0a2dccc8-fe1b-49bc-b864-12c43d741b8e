!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("api/facetec-session-token",[],t):"object"==typeof exports?exports["api/facetec-session-token"]=t():e["api/facetec-session-token"]=t()}(this,(()=>{return e={264:(e,t,o)=>{const n=o(599);async function s(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken/facetec`,s={...e.headers};if(delete s.host,delete s.connection,s["Content-Type"]="application/json",s.Accept="application/json",!s.Authorization&&process.env.JWT_TOKEN&&(s.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!s["X-Ekyc-Device-Info"]){const e=n.getUniqueId();s["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(o,{method:"GET",headers:s}),c=await r.json();return t.status(r.status).json(c)}catch(e){return console.error("Error getting FaceTec session token:",e),t.status(500).json({error:"Failed to get FaceTec session token"})}}e.exports=s,e.exports.default=s},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}}},t={},function o(n){var s=t[n];if(void 0!==s)return s.exports;var r=t[n]={exports:{}};return e[n](r,r.exports,o),r.exports}(264);var e,t}));