!function(e,o){"object"==typeof exports&&"object"==typeof module?module.exports=o():"function"==typeof define&&define.amd?define("api/idscan-only",[],o):"object"==typeof exports?exports["api/idscan-only"]=o():e["api/idscan-only"]=o()}(this,(()=>{return e={235:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/idscan-only`,t={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],correlationid:e.headers.correlationid};let r;Object.keys(t).forEach((e=>{void 0===t[e]&&delete t[e]})),console.log("=== IDSCAN-ONLY API HANDLER - REQUEST BODY ANALYSIS ==="),console.log("📋 Request body type:",typeof e.body),console.log("📋 Request body constructor:",e.body?.constructor?.name),"string"==typeof e.body?(console.log("⚠️ Request body is already a string - using directly"),r=e.body):(console.log("📦 Request body is object - stringifying"),r=JSON.stringify(e.body)),console.log("📏 Request body size:",r.length,"characters"),console.log("📋 Request body starts with:",r.substring(0,100)+"..."),r.startsWith('"{')||r.startsWith('"{')?(console.error("🚨 IDSCAN-ONLY: DETECTED DOUBLE STRINGIFICATION!"),console.error("🚨 Request body appears to be double-stringified JSON")):console.log("✅ IDSCAN-ONLY: Request body appears to be properly formatted");const n=await fetch(s,{method:"POST",headers:t,body:r}),a=await n.json();if(n.ok&&"CUS-KYC-1000"===a.code){const e={wasProcessed:!0,error:!1,scanResultBlob:a.data?.scanResultBlob||"",originalResponse:a};return console.log("Returning success response with scanResultBlob length:",e.scanResultBlob.length),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:a.description||a.message||"Unknown error",originalResponse:a};return console.log("Returning error response:",e.errorMessage),o.status(n.status).json(e)}}catch(e){return console.error("Error processing ID scan:",e),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process ID scan"})}}e.exports=o,e.exports.default=o}},o={},function s(t){var r=o[t];if(void 0!==r)return r.exports;var n=o[t]={exports:{}};return e[t](n,n.exports,s),n.exports}(235);var e,o}));