!function(e,o){"object"==typeof exports&&"object"==typeof module?module.exports=o():"function"==typeof define&&define.amd?define("api/match-3d-2d-idscan-back",[],o):"object"==typeof exports?exports["api/match-3d-2d-idscan-back"]=o():e["api/match-3d-2d-idscan-back"]=o()}(this,(()=>{return e={991:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/match-3d-2d-idscan/back`;if(console.log("=== BACK ID SCAN API HANDLER - REQUEST ANALYSIS ==="),console.log("📤 Target URL:",s),console.log("📋 Request body type:",typeof e.body),console.log("📋 Request body constructor:",e.body?.constructor?.name),console.log("📋 Request body keys:",e.body?Object.keys(e.body):"No body"),!e.body)return console.error("❌ Request body is missing"),o.status(400).json({wasProcessed:!1,error:!0,errorMessage:"Request body is required"});const t={hasIdScan:!!e.body.idScan,hasIdScanFrontImage:!!e.body.idScanFrontImage,hasIdScanBackImage:!!e.body.idScanBackImage,hasEnableConfirmInfo:void 0!==e.body.enableConfirmInfo,enableConfirmInfoValue:e.body.enableConfirmInfo};console.log("📊 Back scan body analysis:",t),e.body.idScan||console.error("❌ Missing required parameter: idScan"),e.body.idScanBackImage||console.error("❌ Missing required parameter: idScanBackImage"),e.body.idScanFrontImage&&console.log("ℹ️ Front image also present in back scan request");const n={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":e.headers["x-ekyc-sdk-version"]||"1.0.0",correlationid:e.headers.correlationid};let r;if(Object.keys(n).forEach((e=>{void 0===n[e]&&delete n[e]})),console.log("📋 Request headers:",Object.keys(n)),"string"==typeof e.body?(console.log("⚠️ Request body is already a string - using directly"),r=e.body):(console.log("📦 Request body is object - stringifying"),r=JSON.stringify(e.body)),console.log("📏 Request body size:",r.length,"characters"),console.log("📋 Request body starts with:",r.substring(0,100)+"..."),r.startsWith('"{')||r.startsWith('"{')){console.error("🚨 DETECTED DOUBLE STRINGIFICATION!"),console.error("🚨 Request body appears to be double-stringified JSON"),console.error("🚨 First 200 chars:",r.substring(0,200));try{const e=JSON.parse(r);"object"==typeof e&&(console.log("🔧 Attempting to fix by using parsed object"),r=JSON.stringify(e),console.log("✅ Fixed request body starts with:",r.substring(0,100)+"..."))}catch(e){console.error("❌ Failed to fix double stringification:",e.message)}}if(r.length>1e3){const e=r.substring(0,500)+"...[truncated]..."+r.substring(r.length-500);console.log("📄 Request body (truncated):",e)}else console.log("📄 Request body:",r);console.log("📤 Sending request to backend...");const a=await fetch(s,{method:"POST",headers:n,body:r}),c=await a.json();if(console.log("=== BACK ID SCAN API HANDLER - RESPONSE ANALYSIS ==="),console.log("📥 Response status:",a.status),console.log("📥 Response ok:",a.ok),console.log("📥 Response code:",c.code),console.log("📥 Response keys:",Object.keys(c)),c.data&&(console.log("📊 Response data keys:",Object.keys(c.data)),console.log("📊 Has scanResultBlob:",!!c.data.scanResultBlob),console.log("📊 Has documentData:",!!c.data.documentData),c.data.scanResultBlob&&console.log("📦 ScanResultBlob length:",c.data.scanResultBlob.length),c.data.documentData)){console.log("📄 DocumentData length:",c.data.documentData.length);try{const e=JSON.parse(c.data.documentData);console.log("📊 DocumentData structure:",{hasScannedValues:!!e.scannedValues,groupCount:e.scannedValues?.groups?.length||0})}catch(e){console.error("❌ Failed to parse documentData:",e.message)}}if(a.ok&&"CUS-KYC-1000"===c.code){const e={wasProcessed:!0,error:!1,scanResultBlob:c.data?.scanResultBlob||"",originalResponse:c};return console.log("✅ SUCCESS - Returning back scan success response"),console.log("📦 ScanResultBlob length:",e.scanResultBlob.length),console.log("📄 Has OCR data:",!!c.data?.documentData),console.log("=== BACK ID SCAN API HANDLER - SUCCESS ==="),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:c.description||c.message||"Unknown error occurred during back ID scan",originalResponse:c};return console.error("❌ ERROR - Back scan failed"),console.error("❌ Error message:",e.errorMessage),console.error("❌ Full response data:",JSON.stringify(c,null,2)),console.error("=== BACK ID SCAN API HANDLER - ERROR ==="),o.status(a.status).json(e)}}catch(e){return console.error("=== BACK ID SCAN API HANDLER - EXCEPTION ==="),console.error("❌ Exception type:",e.constructor.name),console.error("❌ Exception message:",e.message),console.error("❌ Exception stack:",e.stack),e.cause&&console.error("❌ Error cause:",e.cause),console.error("=== END BACK ID SCAN EXCEPTION ==="),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process back ID scan: "+e.message})}}e.exports=o,e.exports.default=o}},o={},function s(t){var n=o[t];if(void 0!==n)return n.exports;var r=o[t]={exports:{}};return e[t](r,r.exports,s),r.exports}(991);var e,o}));