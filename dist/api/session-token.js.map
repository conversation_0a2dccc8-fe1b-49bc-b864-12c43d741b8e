{"version": 3, "file": "api/session-token.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD,CAAC;AACD,O;;;;;;ACVA,sBAAsB,mBAAO,CAAC,GAAwB;;AAEtD;AACA;AACA;AACA;AACA,WAAW,+BAA+B;AAC1C,WAAW,gCAAgC;AAC3C;AACA;AACA;AACA;AACA,kCAAkC,6BAA6B;AAC/D;;AAEA;AACA;AACA;AACA,mBAAmB,QAAQ;;AAE3B;AACA,sBAAsB;AACtB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,2CAA2C,sBAAsB;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA,iDAAiD,KAAK;AACtD;;AAEA;AACA;AACA;AACA;AACA,KAAK;;AAEL;AACA;;AAEA;AACA;AACA,IAAI;AACJ;AACA,kCAAkC,sCAAsC;AACxE;AACA;;AAEA;AACA;AACA,yBAAsB;;;;;;;;AC7DtB;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;;AAEA;;;;;;;UCjDA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;UEtBA;UACA;UACA;UACA", "sources": ["webpack://ScbTechXEkycSDK/webpack/universalModuleDefinition", "webpack://ScbTechXEkycSDK/./lib/infrastructure/api/session-token.js", "webpack://ScbTechXEkycSDK/./lib/infrastructure/utils/UuidGenerator.js", "webpack://ScbTechXEkycSDK/webpack/bootstrap", "webpack://ScbTechXEkycSDK/webpack/before-startup", "webpack://ScbTechXEkycSDK/webpack/startup", "webpack://ScbTechXEkycSDK/webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ScbTechXEkycSDK\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ScbTechXEkycSDK\"] = factory();\n\telse\n\t\troot[\"ScbTechXEkycSDK\"] = root[\"ScbTechXEkycSDK\"] || {}, root[\"ScbTechXEkycSDK\"][\"api/session-token\"] = factory();\n})(this, () => {\nreturn ", "const UuidGenerator = require('../utils/UuidGenerator');\n\n/**\n * API route to get a session token from the eKYC authentication API\n * This acts as a proxy to avoid CORS issues when calling from the browser\n *\n * @param {import('next').NextApiRequest} req\n * @param {import('next').NextApiResponse} res\n */\nasync function handler(req, res) {\n  // Only allow GET requests\n  if (req.method !== 'GET') {\n    return res.status(405).json({ error: 'Method not allowed' });\n  }\n\n  try {\n    // Set the base URL - this should be configured based on environment\n    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';\n    const url = `${baseUrl}/v1/ekyc/authen/sessiontoken`;\n\n    // Forward any headers from the client request, except host-specific ones\n    const headers = { ...req.headers };\n    delete headers.host;\n    delete headers.connection;\n\n    // Add or override specific headers\n    headers['Content-Type'] = 'application/json';\n    headers['Accept'] = 'application/json';\n    \n    // Add default JWT token if not provided in request\n    if (!headers['Authorization'] && process.env.JWT_TOKEN) {\n      headers['Authorization'] = `Bearer ${process.env.JWT_TOKEN}`;\n    }\n\n    // Ensure X-Ekyc-Device-Info is preserved if it exists\n    // This is important because it contains the device ID generated on the client side\n    if (!headers['X-Ekyc-Device-Info']) {\n      // If not provided, generate a fallback UUID for server-side requests\n      const uuid = UuidGenerator.getUniqueId();\n      headers['X-Ekyc-Device-Info'] = `browser|${uuid}`;\n    }\n\n    // Make the GET request with headers only (no body)\n    const response = await fetch(url, {\n      method: 'GET',\n      headers: headers,\n    });\n\n    // Get the response data\n    const data = await response.json();\n\n    // Return the response with the same status code\n    return res.status(response.status).json(data);\n  } catch (error) {\n    console.error('Error getting session token:', error);\n    return res.status(500).json({ error: 'Failed to get session token' });\n  }\n}\n\n// Export for both CommonJS and ES modules\nmodule.exports = handler;\nmodule.exports.default = handler;\n", "/**\n * Utility class for generating UUIDs\n */\nclass UuidGenerator {\n  /**\n   * Generate a UUID v4\n   * @returns {string} - A UUID v4 string\n   */\n  static generateUuid() {\n    // Implementation of UUID v4 generation\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      const r = Math.random() * 16 | 0;\n      const v = c === 'x' ? r : (r & 0x3 | 0x8);\n      return v.toString(16);\n    });\n  }\n\n  /**\n   * Get a device identifier\n   * This will generate a UUID and store it in localStorage if running in a browser\n   * If the UUID already exists in localStorage, it will be reused\n   * @returns {string} - A device identifier\n   */\n  static getDeviceId() {\n    if (typeof window !== 'undefined' && window.localStorage) {\n      // Running in browser, check localStorage\n      let deviceId = localStorage.getItem('ekyc_device_id');\n      if (!deviceId) {\n        // Generate a new UUID if not found in localStorage\n        deviceId = this.generateUuid();\n        localStorage.setItem('ekyc_device_id', deviceId);\n      }\n      return deviceId;\n    } else {\n      // Not running in browser, generate a new UUID\n      return this.generateUuid();\n    }\n  }\n\n  /**\n   * Generate a new UUID every time\n   * Unlike getDeviceId, this does not store or reuse UUIDs\n   * @returns {string} - A new UUID v4 string\n   */\n  static getUniqueId() {\n    return this.generateUuid();\n  }\n}\n\nmodule.exports = UuidGenerator;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(198);\n", ""], "names": [], "sourceRoot": ""}