{"version": 3, "file": "utils.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD,CAAC;AACD,O;;;;;;ACVA,sBAAsB,mBAAO,CAAC,GAAiB;AAC/C,qBAAqB,mBAAO,CAAC,GAAgB;;AAE7C;AACA;AACA;AACA;;;;;;;;ACNA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,eAAe,QAAQ;AACvB;AACA;AACA;AACA;AACA;;AAEA;;;;;;;;ACjDA;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,aAAa;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ;AACR;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,SAAS;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA,aAAa,QAAQ;AACrB,eAAe,SAAS;AACxB;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,aAAa;AAC5B;AACA;AACA;AACA;;AAEA;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;;AAEA;;;;;;;UCxGA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;UEtBA;UACA;UACA;UACA", "sources": ["webpack://ScbTechXEkycSDK/webpack/universalModuleDefinition", "webpack://ScbTechXEkycSDK/./lib/infrastructure/utils/index.js", "webpack://ScbTechXEkycSDK/./lib/infrastructure/utils/UuidGenerator.js", "webpack://ScbTechXEkycSDK/./lib/infrastructure/utils/TokenStorage.js", "webpack://ScbTechXEkycSDK/webpack/bootstrap", "webpack://ScbTechXEkycSDK/webpack/before-startup", "webpack://ScbTechXEkycSDK/webpack/startup", "webpack://ScbTechXEkycSDK/webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ScbTechXEkycSDK\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ScbTechXEkycSDK\"] = factory();\n\telse\n\t\troot[\"ScbTechXEkycSDK\"] = root[\"ScbTechXEkycSDK\"] || {}, root[\"ScbTechXEkycSDK\"][\"utils\"] = factory();\n})(this, () => {\nreturn ", "const UuidGenerator = require('./UuidGenerator');\nconst TokenStorage = require('./TokenStorage');\n\nmodule.exports = {\n  UuidGenerator,\n  TokenStorage\n};\n", "/**\n * Utility class for generating UUIDs\n */\nclass UuidGenerator {\n  /**\n   * Generate a UUID v4\n   * @returns {string} - A UUID v4 string\n   */\n  static generateUuid() {\n    // Implementation of UUID v4 generation\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      const r = Math.random() * 16 | 0;\n      const v = c === 'x' ? r : (r & 0x3 | 0x8);\n      return v.toString(16);\n    });\n  }\n\n  /**\n   * Get a device identifier\n   * This will generate a UUID and store it in localStorage if running in a browser\n   * If the UUID already exists in localStorage, it will be reused\n   * @returns {string} - A device identifier\n   */\n  static getDeviceId() {\n    if (typeof window !== 'undefined' && window.localStorage) {\n      // Running in browser, check localStorage\n      let deviceId = localStorage.getItem('ekyc_device_id');\n      if (!deviceId) {\n        // Generate a new UUID if not found in localStorage\n        deviceId = this.generateUuid();\n        localStorage.setItem('ekyc_device_id', deviceId);\n      }\n      return deviceId;\n    } else {\n      // Not running in browser, generate a new UUID\n      return this.generateUuid();\n    }\n  }\n\n  /**\n   * Generate a new UUID every time\n   * Unlike getDeviceId, this does not store or reuse UUIDs\n   * @returns {string} - A new UUID v4 string\n   */\n  static getUniqueId() {\n    return this.generateUuid();\n  }\n}\n\nmodule.exports = UuidGenerator;\n", "/**\n * Utility class for storing and retrieving tokens\n */\nclass TokenStorage {\n  /**\n   * Store a token in localStorage\n   * @param {string} key - The key to store the token under\n   * @param {string} token - The token to store\n   * @returns {boolean} - True if the token was stored successfully\n   */\n  static storeToken(key, token) {\n    if (typeof window !== 'undefined' && window.localStorage && token) {\n      try {\n        localStorage.setItem(key, token);\n        return true;\n      } catch (error) {\n        console.error('Error storing token:', error);\n        return false;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Retrieve a token from localStorage\n   * @param {string} key - The key the token is stored under\n   * @returns {string|null} - The token or null if not found\n   */\n  static getToken(key) {\n    if (typeof window !== 'undefined' && window.localStorage) {\n      return localStorage.getItem(key);\n    }\n    return null;\n  }\n\n  /**\n   * Remove a token from localStorage\n   * @param {string} key - The key the token is stored under\n   * @returns {boolean} - True if the token was removed successfully\n   */\n  static removeToken(key) {\n    if (typeof window !== 'undefined' && window.localStorage) {\n      try {\n        localStorage.removeItem(key);\n        return true;\n      } catch (error) {\n        console.error('Error removing token:', error);\n        return false;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * Store the eKYC token\n   * @param {string} token - The eKYC token to store\n   * @returns {boolean} - True if the token was stored successfully\n   */\n  static storeEkycToken(token) {\n    return this.storeToken('ekyc_token', token);\n  }\n\n  /**\n   * Get the stored eKYC token\n   * @returns {string|null} - The eKYC token or null if not found\n   */\n  static getEkycToken() {\n    return this.getToken('ekyc_token');\n  }\n\n  /**\n   * Remove the stored eKYC token\n   * @returns {boolean} - True if the token was removed successfully\n   */\n  static removeEkycToken() {\n    return this.removeToken('ekyc_token');\n  }\n\n  /**\n   * Store the session ID\n   * @param {string} sessionId - The session ID to store\n   * @returns {boolean} - True if the session ID was stored successfully\n   */\n  static storeSessionId(sessionId) {\n    return this.storeToken('ekyc_session_id', sessionId);\n  }\n\n  /**\n   * Get the stored session ID\n   * @returns {string|null} - The session ID or null if not found\n   */\n  static getSessionId() {\n    return this.getToken('ekyc_session_id');\n  }\n\n  /**\n   * Remove the stored session ID\n   * @returns {boolean} - True if the session ID was removed successfully\n   */\n  static removeSessionId() {\n    return this.removeToken('ekyc_session_id');\n  }\n}\n\nmodule.exports = TokenStorage;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(411);\n", ""], "names": [], "sourceRoot": ""}