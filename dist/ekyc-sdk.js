(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define("ScbTechXEkycSDK", [], factory);
	else if(typeof exports === 'object')
		exports["ScbTechXEkycSDK"] = factory();
	else
		root["ScbTechXEkycSDK"] = root["ScbTechXEkycSDK"] || {}, root["ScbTechXEkycSDK"]["ekyc-sdk"] = factory();
})(this, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ 236:
/***/ ((module, exports, __webpack_require__) => {

/**
 * eKYC SDK - Main Entry Point
 *
 * This is the primary entry point for external developers using the eKYC SDK by SCB TechX.
 * It provides a clean, framework-agnostic API for authentication, FaceTec integration,
 * and ID scanning functionality.
 *
 * Usage: import EkycSDK from './ekyc-sdk/ekyc-sdk.js';
 *
 * @version 1.0.0
 * <AUTHOR> TechX
 */

// Import core functionality
const {
  getSessionToken: getSessionTokenCore,
  getFaceTecSessionTokenWithEkycToken: getFaceTecSessionTokenCore,
  performPhotoIDScan: performPhotoIDScanCore,
  getStoredEkycToken,
  clearEkycToken,
  // Import the 5 main SDK functions
  initEkyc: initEkycCore,
  ocrIdCard: ocrIdCardCore,
  ocrIdCardVerifyByFace: ocrIdCardVerifyByFaceCore,
  ndidVerification: ndidVerificationCore,
  livenessCheck: livenessCheckCore
} = __webpack_require__(347);

const { TokenStorage, UuidGenerator } = __webpack_require__(411);
const facetecService = __webpack_require__(382);

/**
 * Authentication namespace
 * Handles session tokens and authentication flow
 */
const Auth = {
  /**
   * Get a session token from the eKYC API
   * @param {Object} options - Configuration options
   * @param {Object} options.headers - Additional headers to include
   * @param {string} options.apiKey - API key for authentication
   * @param {boolean} options.storeToken - Whether to store the token (default: true)
   * @returns {Promise<Object>} Session token response
   */
  async getSessionToken(options = {}) {
    const { headers = {}, apiKey, storeToken = true } = options;
    
    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }
    
    return await getSessionTokenCore(headers, storeToken);
  },

  /**
   * Get a FaceTec session token using stored eKYC token
   * @param {Object} options - Configuration options
   * @param {Object} options.headers - Additional headers to include
   * @param {boolean} options.initializeFaceTec - Whether to initialize FaceTec SDK (default: true)
   * @returns {Promise<Object>} FaceTec session token response
   */
  async getFaceTecSessionToken(options = {}) {
    const { headers = {}, initializeFaceTec = true } = options;
    return await getFaceTecSessionTokenCore(headers, initializeFaceTec);
  },

  /**
   * Get the stored eKYC token
   * @returns {string|null} The stored token or null
   */
  getStoredToken() {
    return getStoredEkycToken();
  },

  /**
   * Clear the stored eKYC token
   * @returns {boolean} True if cleared successfully
   */
  clearToken() {
    return clearEkycToken();
  }
};

/**
 * FaceTec namespace
 * Handles FaceTec SDK integration and ID scanning
 */
const FaceTec = {
  /**
   * Initialize FaceTec SDK
   * @param {string} deviceKey - Device key from session token response
   * @param {string} encryptionKey - Encryption key from session token response
   * @returns {Promise<boolean>} True if initialized successfully
   */
  async initialize(deviceKey, encryptionKey) {
    return await facetecService.initializeFaceTec(deviceKey, encryptionKey);
  },

  /**
   * Load FaceTec SDK
   * @returns {Promise<Object>} FaceTec SDK instance
   */
  async loadSDK() {
    return await facetecService.loadFaceTecSDK();
  },

  /**
   * Get FaceTec SDK version
   * @returns {Promise<string>} SDK version
   */
  async getVersion() {
    return await facetecService.getFaceTecVersion();
  },

  /**
   * Perform Photo ID Scan
   * @param {Object} options - Scan configuration
   * @param {string} options.deviceKey - Device key for scanning
   * @param {Object} options.sessionTokenResponse - Session token response from Auth.getFaceTecSessionToken()
   * @param {Object} options.headers - Additional headers
   * @returns {Promise<Object>} Scan result
   */
  async performIDScan(options = {}) {
    const { deviceKey, sessionTokenResponse, headers = {} } = options;
    
    if (!deviceKey) {
      throw new Error('deviceKey is required for ID scanning');
    }
    
    if (!sessionTokenResponse) {
      throw new Error('sessionTokenResponse is required for ID scanning');
    }
    
    return await performPhotoIDScanCore(headers, deviceKey, sessionTokenResponse);
  }
};

/**
 * Utilities namespace
 * Provides utility functions for UUID generation and token storage
 */
const Utils = {
  /**
   * Generate a new UUID
   * @returns {string} UUID v4 string
   */
  generateUUID() {
    return UuidGenerator.getUniqueId();
  },

  /**
   * Get or generate a device ID (persisted in localStorage if available)
   * @returns {string} Device ID
   */
  getDeviceId() {
    return UuidGenerator.getDeviceId();
  },

  /**
   * Token storage utilities
   */
  TokenStorage: {
    /**
     * Store a token
     * @param {string} key - Storage key
     * @param {string} value - Token value
     * @returns {boolean} True if stored successfully
     */
    store(key, value) {
      return TokenStorage.storeToken(key, value);
    },

    /**
     * Get a stored token
     * @param {string} key - Storage key
     * @returns {string|null} Token value or null
     */
    get(key) {
      return TokenStorage.getToken(key);
    },

    /**
     * Remove a stored token
     * @param {string} key - Storage key
     * @returns {boolean} True if removed successfully
     */
    remove(key) {
      return TokenStorage.removeToken(key);
    }
  }
};

/**
 * Main SDK object with clean public API
 */
const EkycSDK = {
  // Namespaced APIs
  Auth,
  FaceTec,
  Utils,

  // Quick access methods for common operations
  async getSessionToken(apiKey, options = {}) {
    return Auth.getSessionToken({ apiKey, ...options });
  },

  async initializeFaceTec(sessionTokenResponse) {
    if (!sessionTokenResponse?.data?.deviceKey || !sessionTokenResponse?.data?.encryptionKey) {
      throw new Error('Invalid session token response: missing deviceKey or encryptionKey');
    }

    return FaceTec.initialize(
      sessionTokenResponse.data.deviceKey,
      sessionTokenResponse.data.encryptionKey
    );
  },

  async performIDScan(deviceKey, sessionTokenResponse, options = {}) {
    return FaceTec.performIDScan({
      deviceKey,
      sessionTokenResponse,
      ...options
    });
  },

  // ========================================
  // 5 MAIN SDK ENTRY POINT FUNCTIONS
  // ========================================

  /**
   * 1. Initialize the eKYC SDK
   */
  async initEkyc(options = {}) {
    return await initEkycCore(options);
  },

  /**
   * 2. Perform OCR ID Card scanning
   */
  async ocrIdCard(options = {}) {
    return await ocrIdCardCore(options);
  },

  /**
   * 3. OCR ID Card with facial verification
   */
  async ocrIdCardVerifyByFace(options = {}) {
    return await ocrIdCardVerifyByFaceCore(options);
  },

  /**
   * 4. NDID digital identity verification
   */
  async ndidVerification(options = {}) {
    return await ndidVerificationCore(options);
  },

  /**
   * 5. Facial liveness detection
   */
  async livenessCheck(options = {}) {
    return await livenessCheckCore(options);
  },



  // SDK metadata
  version: '1.0.0',
  name: 'SCB TechX eKYC SDK'
};

// Export for different module systems
module.exports = EkycSDK;
module.exports["default"] = EkycSDK;

// For ES6 modules
if (true) {
  exports.EkycSDK = EkycSDK;
  exports.Auth = Auth;
  exports.FaceTec = FaceTec;
  exports.Utils = Utils;
}


/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	(() => {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = (result, chunkIds, fn, priority) => {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var [chunkIds, fn, priority] = deferred[i];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	(() => {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			656: 0
/******/ 		};
/******/ 		
/******/ 		// no chunk on demand loading
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = (parentChunkLoadingFunction, data) => {
/******/ 			var [chunkIds, moreModules, runtime] = data;
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some((id) => (installedChunks[id] !== 0))) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 			return __webpack_require__.O(result);
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = this["webpackChunkScbTechXEkycSDK"] = this["webpackChunkScbTechXEkycSDK"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 	var __webpack_exports__ = __webpack_require__.O(undefined, [347], () => (__webpack_require__(236)))
/******/ 	__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=ekyc-sdk.js.map