(this["webpackChunkScbTechXEkycSDK"] = this["webpackChunkScbTechXEkycSDK"] || []).push([[347,738],{

/***/ 0:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const FaceTecRepository = __webpack_require__(216);
const UuidGenerator = __webpack_require__(599);
const DeveloperStatusMessages = __webpack_require__(719);

/**
 * Use case for posting ID scan data
 * Contains business logic for ID scanning operations
 */
class PostIDScanOnlyUseCase {
    constructor() {
        this.faceTecRepository = new FaceTecRepository();
    }

    /**
     * Execute the ID scan submission process
     * @param {Object} params - Parameters for the use case
     * @param {Object} params.idScanResult - FaceTec ID scan result
     * @param {string} params.deviceKey - Device key for authentication
     * @param {Object} params.additionalHeaders - Additional headers for the request
     * @param {Function} params.onProgress - Progress callback function
     * @returns {Promise<Object>} - Use case result
     */
    async execute({ idScanResult, deviceKey, additionalHeaders = {}, onProgress = null }) {
        const startTime = performance.now();
        
        try {
            DeveloperStatusMessages.logMessage('Starting PostIDScanOnlyUseCase execution');
            
            // Prepare scan data from FaceTec result
            DeveloperStatusMessages.logMessage('Preparing scan data...');
            const scanData = this.prepareScanData(idScanResult);
            DeveloperStatusMessages.logData('Scan Data Keys', Object.keys(scanData));
            
            // Prepare headers
            DeveloperStatusMessages.logMessage('Preparing headers...');
            const headers = this.prepareHeaders(idScanResult, deviceKey, additionalHeaders);
            DeveloperStatusMessages.logData('Request Headers', Object.keys(headers));
            
            // Validate scan data
            DeveloperStatusMessages.logMessage('Validating scan data...');
            this.faceTecRepository.validateScanData(scanData);
            DeveloperStatusMessages.logSuccess('Scan data validation passed');
            
            // Submit to repository with progress tracking
            DeveloperStatusMessages.logMessage('Submitting to repository...');
            const response = await this.faceTecRepository.submitIDScan(scanData, headers, onProgress);
            
            // Process the response according to business rules
            DeveloperStatusMessages.logMessage('Processing response...');
            const result = this.processResponse(response);
            
            const endTime = performance.now();
            DeveloperStatusMessages.logPerformance('PostIDScanOnlyUseCase.execute', startTime, endTime);
            DeveloperStatusMessages.logSuccess(`UseCase completed successfully: ${result.success}`);
            
            return result;
            
        } catch (error) {
            const endTime = performance.now();
            DeveloperStatusMessages.logPerformance('PostIDScanOnlyUseCase.execute (failed)', startTime, endTime);
            DeveloperStatusMessages.logError('PostIDScanOnlyUseCase - execute error', error);
            throw error;
        }
    }

    /**
     * Prepare scan data from FaceTec ID scan result
     * @param {Object} idScanResult - FaceTec ID scan result
     * @returns {Object} - Prepared scan data
     */
    prepareScanData(idScanResult) {
        const parameters = {
            idScan: idScanResult.idScan,
            enableConfirmInfo: true // default value follow spec
        };

        // Add front image if available
        if (idScanResult.frontImages && idScanResult.frontImages[0]) {
            parameters.idScanFrontImage = idScanResult.frontImages[0];
        }

        // Add back image if available
        if (idScanResult.backImages && idScanResult.backImages[0]) {
            parameters.idScanBackImage = idScanResult.backImages[0];
        }

        return parameters;
    }

    /**
     * Prepare headers for the API request
     * @param {Object} idScanResult - FaceTec ID scan result
     * @param {string} deviceKey - Device key
     * @param {Object} additionalHeaders - Additional headers
     * @returns {Object} - Prepared headers
     */
    prepareHeaders(idScanResult, deviceKey, additionalHeaders) {
        const headers = {};

        // Add device key if available
        if (deviceKey) {
            headers['X-Device-Key'] = deviceKey;
        }

        // Add FaceTec user agent
        if (idScanResult.sessionId) {
            headers['X-User-Agent'] = FaceTecSDK.createFaceTecAPIUserAgentString(idScanResult.sessionId);
        }

        // Add additional headers
        if (additionalHeaders.Authorization) {
            headers['Authorization'] = additionalHeaders.Authorization;
        }
        if (additionalHeaders['X-Session-Id']) {
            headers['X-Session-Id'] = additionalHeaders['X-Session-Id'];
        }
        if (additionalHeaders['X-Ekyc-Token']) {
            headers['X-Ekyc-Token'] = additionalHeaders['X-Ekyc-Token'];
        }
        if (additionalHeaders.correlationid) {
            headers['correlationid'] = additionalHeaders.correlationid;
        }

        // Generate X-Tid if not provided (mandatory field)
        // Generate UUID v4 for transaction ID using UuidGenerator
        headers['X-Tid'] = UuidGenerator.getUniqueId();

        return headers;
    }

    /**
     * Process the API response according to business rules
     * @param {Object} response - API response
     * @returns {Object} - Processed response
     */
    processResponse(response) {
        // Business logic for processing response
        // This is where you can add any business rules for handling the response
        
        return {
            success: response.wasProcessed === true && response.error === false,
            scanResultBlob: response.scanResultBlob,
            originalResponse: response.originalResponse,
            errorMessage: response.errorMessage
        };
    }
}

module.exports = PostIDScanOnlyUseCase; 

/***/ }),

/***/ 103:
/***/ ((module) => {

/**
 * SessionToken entity
 * Represents a session token from the eKYC authentication API
 */
class SessionToken {
  /**
   * @param {Object} data - The session token data
   */
  constructor(data) {
    this.data = data;
  }

  /**
   * Get the token value
   * @returns {string|null} - The token value or null if not available
   */
  getToken() {
    return this.data?.token || null;
  }

  /**
   * Get the eKYC token value
   * @returns {string|null} - The eKYC token value or null if not available
   */
  getEkycToken() {
    // Handle the new response format with code, description, and data.ekycToken
    if (this.data?.data?.ekycToken) {
      return this.data.data.ekycToken;
    }
    // Fallback to the old format or return null
    return this.data?.ekycToken || null;
  }

  /**
   * Get the expiration time
   * @returns {string|null} - The expiration time or null if not available
   */
  getExpiresAt() {
    return this.data?.expiresAt || null;
  }

  /**
   * Get the response code
   * @returns {string|null} - The response code or null if not available
   */
  getCode() {
    return this.data?.code || null;
  }

  /**
   * Get the response description
   * @returns {string|null} - The response description or null if not available
   */
  getDescription() {
    return this.data?.description || null;
  }

  /**
   * Check if the token is valid
   * @returns {boolean} - True if the token is valid
   */
  isValid() {
    return !!this.getEkycToken();
  }

  /**
   * Get the raw data
   * @returns {Object} - The raw data
   */
  toJSON() {
    return this.data;
  }
}

module.exports = SessionToken;


/***/ }),

/***/ 161:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const SessionToken = __webpack_require__(103);

/**
 * AuthRepository
 * Repository for authentication-related operations
 */
class AuthRepository {
  /**
   * @param {Object} authApiDataSource - The authentication API data source
   */
  constructor(authApiDataSource) {
    this.authApiDataSource = authApiDataSource;
  }

  /**
   * Get a session token
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity
   */
  async getSessionToken(headers = {}) {
    const data = await this.authApiDataSource.getSessionToken(headers);
    return new SessionToken(data);
  }

  /**
   * Get a FaceTec session token using the stored eKYC token
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity
   */
  async getFaceTecSessionTokenWithEkycToken(headers = {}) {
    const data = await this.authApiDataSource.getFaceTecSessionTokenWithEkycToken(headers);
    return new SessionToken(data);
  }
}

module.exports = AuthRepository;


/***/ }),

/***/ 189:
/***/ ((module) => {

/**
 * Currency entity
 * Represents a currency amount
 */
class Currency {
  /**
   * @param {number} amount - The amount
   * @param {string} currencyCode - The currency code (e.g., 'USD')
   */
  constructor(amount, currencyCode = 'USD') {
    this.amount = amount;
    this.currencyCode = currencyCode;
  }

  /**
   * Format the currency amount
   * @returns {string} - The formatted currency amount
   */
  format() {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: this.currencyCode
    }).format(this.amount);
  }

  /**
   * Get the amount
   * @returns {number} - The amount
   */
  getAmount() {
    return this.amount;
  }

  /**
   * Get the currency code
   * @returns {string} - The currency code
   */
  getCurrencyCode() {
    return this.currencyCode;
  }
}

module.exports = Currency;


/***/ }),

/***/ 216:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const IDScanDataSource = __webpack_require__(518);
const LivenessCheckDataSource = __webpack_require__(777);

/**
 * Repository for FaceTec related operations
 * Implements the repository pattern to abstract data access
 */
class FaceTecRepository {
    constructor() {
        this.idScanDataSource = new IDScanDataSource();
        this.livenessCheckDataSource = new LivenessCheckDataSource();
    }

    /**
     * Submit ID scan data for processing
     * @param {Object} scanData - The scan data including images and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitIDScan(scanData, headers = {}, onProgress = null) {
        try {
            // Call the data source to post ID scan data
            const response = await this.idScanDataSource.postIDScanOnly(scanData, headers, onProgress);
            
            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.
            
            return response;
        } catch (error) {
            console.error('FaceTecRepository - submitIDScan error:', error);
            throw error;
        }
    }

    /**
     * Submit liveness check data for processing
     * @param {Object} livenessData - The liveness data including face scan and audit trail
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitLivenessCheck(livenessData, headers = {}, onProgress = null) {
        try {
            // Call the data source to post liveness check data
            const response = await this.livenessCheckDataSource.postLivenessCheck(livenessData, headers, onProgress);
            
            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.
            
            return response;
        } catch (error) {
            console.error('FaceTecRepository - submitLivenessCheck error:', error);
            throw error;
        }
    }


    /**
     * Validate scan data before submission
     * @param {Object} scanData - The scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateScanData(scanData) {
        if (!scanData) {
            throw new Error('Scan data is required');
        }

        if (!scanData.idScan) {
            throw new Error('ID scan data is required');
        }

        // Add more validation rules as needed
        return true;
    }


    /**
     * Validate liveness data before submission
     * @param {Object} livenessData - The liveness data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateLivenessData(livenessData) {
        if (!livenessData) {
            throw new Error('Liveness data is required');
        }

        if (!livenessData.faceScan) {
            throw new Error('Face scan data is required');
        }

        // Add more validation rules as needed
        return true;
    }

}

module.exports = FaceTecRepository;


/***/ }),

/***/ 266:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

//
// Welcome to the annotated FaceTec Device SDK core code for performing secure Liveness Checks.
//
//
// This is an example self-contained class to perform Liveness Checks with the FaceTec SDK.
// You may choose to further componentize parts of this in your own Apps based on your specific requirements.
//

const PostLivenessCheckUseCase = __webpack_require__(307);
const DeveloperStatusMessages = __webpack_require__(719);

var LivenessCheckProcessor = /** @class */ (function () {
    function LivenessCheckProcessor(sessionToken, sampleAppControllerReference, deviceKey, additionalHeaders) {
        var _this = this;
        this.latestNetworkRequest = new XMLHttpRequest();
        this.deviceKey = deviceKey || null; // Store the device key
        this.additionalHeaders = additionalHeaders || {}; // get additional headers
        this.postLivenessCheckUseCase = new PostLivenessCheckUseCase(); // Initialize use case
        
        //
        // Part 2:  Handling the Result of a FaceScan
        //
        this.processSessionResultWhileFaceTecSDKWaits = function (sessionResult, faceScanResultCallback) {
            _this.latestSessionResult = sessionResult;
            //
            // Part 3:  Handles early exit scenarios where there is no FaceScan to handle -- i.e. User Cancellation, Timeouts, etc.
            //
            if (sessionResult.status !== FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully) {
                _this.latestNetworkRequest.abort();
                _this.latestNetworkRequest = new XMLHttpRequest();
                faceScanResultCallback.cancel();
                return;
            }
            
            // IMPORTANT:  FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully DOES NOT mean the Liveness Check was Successful.
            // It simply means the User completed the Session and a 3D FaceScan was created.  You still need to perform the Liveness Check on your Servers.
            
            //
            // Part 4: Use Clean Architecture - Call UseCase instead of direct API
            //
            _this.executeLivenessCheckUseCase(sessionResult, faceScanResultCallback);
        };

        //
        // New method: Execute Liveness Check using Clean Architecture UseCase
        //
        this.executeLivenessCheckUseCase = async function(sessionResult, faceScanResultCallback) {
            try {
                // Create progress callback for upload tracking
                const onProgress = function(progress) {
                    faceScanResultCallback.uploadProgress(progress);
                };

                // Execute the use case
                const result = await _this.postLivenessCheckUseCase.execute({
                    sessionResult: sessionResult,
                    deviceKey: _this.deviceKey,
                    additionalHeaders: _this.additionalHeaders,
                    onProgress: onProgress
                });

                // Handle the result
                if (result.success) {
                    // Demonstrates dynamically setting the Success Screen Message.
                    FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven");
                    
                    // Proceed to next step with scanResultBlob
                    faceScanResultCallback.proceedToNextStep(result.scanResultBlob);
                } else {
                    // Handle error case
                    _this.cancelDueToNetworkError(result.errorMessage || "Unexpected API response, cancelling out.", faceScanResultCallback);
                }
            } catch (error) {
                console.error('LivenessCheckProcessor - executeLivenessCheckUseCase error:', error);
                _this.cancelDueToNetworkError(error.message || "Exception while handling API response, cancelling out.", faceScanResultCallback);
            }
        };

        //
        // Part 9:  This function gets called after the FaceTec SDK is completely done.  There are no parameters because you have already been passed all data in the processSessionWhileFaceTecSDKWaits function and have already handled all of your own results.
        //
        this.onFaceTecSDKCompletelyDone = function () {
            //
            // DEVELOPER NOTE:  onFaceTecSDKCompletelyDone() is called after the Session has completed or you signal the FaceTec SDK with cancel().
            // Calling a custom function on the Sample App Controller is done for demonstration purposes to show you that here is where you get control back from the FaceTec SDK.
            //
            // If the Liveness Check was processed get the success result from isCompletelyDone
            if (_this.latestSessionResult !== null) {
                _this.success = _this.latestSessionResult.isCompletelyDone;
            }
            // Log success message
            if (_this.success) {
                DeveloperStatusMessages.logMessage("Liveness Check Complete");
            }
            
            // Pass the complete session result to the controller
            _this.sampleAppControllerReference.onComplete(_this.latestSessionResult, 200); // Use 200 as default status
        };

        // Helper function to ensure the session is only cancelled once
        this.cancelDueToNetworkError = function (networkErrorMessage, faceScanResultCallback) {
            if (_this.cancelledDueToNetworkError === false) {
                console.error(networkErrorMessage);
                _this.cancelledDueToNetworkError = true;
                faceScanResultCallback.cancel();
            }
        };

        //
        // DEVELOPER NOTE:  This public convenience method is for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In your code, you may not even want or need to do this.
        //
        this.isSuccess = function () {
            return _this.success;
        };

        //
        // DEVELOPER NOTE:  These properties are for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In the code in your own App, you can pass around signals, flags, intermediates, and results however you would like.
        //
        this.success = false;
        this.sampleAppControllerReference = sampleAppControllerReference;
        this.latestSessionResult = null;
        this.cancelledDueToNetworkError = false;

        //
        // Part 1:  Starting the FaceTec Session
        //
        // Required parameters:
        // - FaceTecFaceScanProcessor:  A class that implements FaceTecFaceScanProcessor, which handles the FaceScan when the User completes a Session.  In this example, "this" implements the class.
        // - sessionToken:  A valid Session Token you just created by calling your API to get a Session Token from the Server SDK.
        //
        new FaceTecSDK.FaceTecSession(this, sessionToken);
    }
    return LivenessCheckProcessor;
}());

// Export the LivenessCheckProcessor class for use in Node.js/webpack environments
module.exports = LivenessCheckProcessor;


/***/ }),

/***/ 307:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const FaceTecRepository = __webpack_require__(216);

/**
 * UseCase for handling Liveness Check API calls
 */
class PostLivenessCheckUseCase {
  constructor() {
    this.faceTecRepository = new FaceTecRepository();
  }

  /**
   * Execute the Liveness Check
   * @param {Object} params - Parameters for the liveness check
   * @param {Object} params.sessionResult - The FaceTec session result
   * @param {string} params.deviceKey - Optional device key for API
   * @param {Object} params.additionalHeaders - Additional headers to include in the request
   * @param {Function} params.onProgress - Callback for upload progress
   * @returns {Promise<Object>} - Result of the liveness check
   */
  async execute(params) {
    try {
      const { sessionResult, deviceKey, additionalHeaders, onProgress } = params;
      
      // Prepare headers
      const headers = { ...additionalHeaders };
      if (deviceKey) {
        headers['X-Device-Key'] = deviceKey;
      }
      
      // Add FaceTec user agent header
      if (sessionResult.sessionId && typeof FaceTecSDK !== 'undefined') {
        headers['X-User-Agent'] = FaceTecSDK.createFaceTecAPIUserAgentString(sessionResult.sessionId);
      }
      
      // Prepare parameters for the API call
      const livenessData = {
        faceScan: sessionResult.faceScan,
        auditTrailImage: sessionResult.auditTrail[0],
        lowQualityAuditTrailImage: sessionResult.lowQualityAuditTrail[0],
        sessionId: sessionResult.sessionId,
        function: 'liveness'
      };
      
      // Call the repository
      const response = await this.faceTecRepository.submitLivenessCheck(livenessData, headers, onProgress);
      
      // Process the response
      if (response.wasProcessed === true && response.error === false) {
        return {
          success: true,
          scanResultBlob: response.scanResultBlob
        };
      } else {
        return {
          success: false,
          errorMessage: response.errorMessage || "Server returned an error."
        };
      }
    } catch (error) {
      console.error("PostLivenessCheckUseCase error:", error);
      throw error;
    }
  }
}

module.exports = PostLivenessCheckUseCase;

/***/ }),

/***/ 347:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

// Import use cases
const FormatCurrencyUseCase = __webpack_require__(592);
const GreetUseCase = __webpack_require__(863);
const GetSessionTokenUseCase = __webpack_require__(995);
const GetFaceTecSessionTokenWithEkycTokenUseCase = __webpack_require__(985);

// Import repositories and data sources
const AuthRepository = __webpack_require__(161);
const AuthApiDataSource = __webpack_require__(548);

// Import utilities
const { TokenStorage } = __webpack_require__(411);
const { UuidGenerator } = __webpack_require__(411);


// Import FaceTec service
const facetecService = __webpack_require__(382);

// Create instances of data sources
const authApiDataSource = new AuthApiDataSource();

// Create instances of repositories
const authRepository = new AuthRepository(authApiDataSource);

// Create instances of use cases
const formatCurrencyUseCase = new FormatCurrencyUseCase();
const greetUseCase = new GreetUseCase();
const getSessionTokenUseCase = new GetSessionTokenUseCase(authRepository);
const getFaceTecSessionTokenWithEkycTokenUseCase = new GetFaceTecSessionTokenWithEkycTokenUseCase(authRepository);

// Import PhotoIDScanProcessor
const PhotoIDScanProcessor = __webpack_require__(955);
/**
 * Format a currency amount
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (e.g., 'USD')
 * @returns {string} - The formatted currency amount
 */
const formatCurrency = (amount, currency = 'USD') => {
  return formatCurrencyUseCase.execute(amount, currency);
};

/**
 * Generate a greeting message
 * @param {string} name - The name to greet
 * @returns {string} - The greeting message
 */
const greet = (name) => {
  return greetUseCase.execute(name);
};

/**
 * Get a session token from the eKYC authentication API
 * @param {Object} headers - Optional additional headers to include in the request
 * @param {boolean} storeToken - Whether to store the ekycToken in localStorage (default: true)
 * @returns {Promise<Object>} - The response data
 */
const getSessionToken = async (headers = {}, storeToken = true) => {
  try {
    // Store the session ID from headers if provided
    if (headers['X-Session-Id']) {
      TokenStorage.storeSessionId(headers['X-Session-Id']);
    }

    const sessionToken = await getSessionTokenUseCase.execute(headers);
    const responseData = sessionToken.toJSON();

    // Store the session ID from the response headers if available
    // Note: This would require modifying the API response to include the headers
    // For now, we'll use the session ID from the request headers

    // Store the ekycToken if requested
    if (storeToken) {
      const ekycToken = sessionToken.getEkycToken();
      if (ekycToken) {
        TokenStorage.storeEkycToken(ekycToken);
      }
    }

    return responseData;
  } catch (error) {
    console.error('Error getting session token:', error);
    throw error;
  }
};

/**
 * Get the stored eKYC token
 * @returns {string|null} - The stored eKYC token or null if not found
 */
const getStoredEkycToken = () => {
  return TokenStorage.getEkycToken();
};

/**
 * Remove the stored eKYC token
 * @returns {boolean} - True if the token was removed successfully
 */
const clearEkycToken = () => {
  return TokenStorage.removeEkycToken();
};

/**
 * Get a FaceTec session token using the stored eKYC token
 * @param {Object} headers - Optional additional headers to include in the request
 * @param {boolean} initializeFaceTecSdk - Whether to initialize FaceTec SDK with the response data (default: true)
 * @returns {Promise<Object>} - The response data with an additional property 'faceTecInitialized' indicating if FaceTec was initialized
 */
const getFaceTecSessionTokenWithEkycToken = async (headers = {}, initializeFaceTecSdk = true) => {
  try {
    // Get the stored session ID if available
    const storedSessionId = TokenStorage.getSessionId();
    if (storedSessionId && !headers['X-Session-Id']) {
      // Add the stored session ID to the headers if not already provided
      headers = {
        ...headers,
        'X-Session-Id': storedSessionId
      };
    }

    const sessionToken = await getFaceTecSessionTokenWithEkycTokenUseCase.execute(headers);
    const responseData = sessionToken.toJSON();

    // Add a property to track if FaceTec was initialized
    responseData.faceTecInitialized = false;


    // Initialize FaceTec SDK if requested and if the response contains the required data
    if (initializeFaceTecSdk &&
        responseData &&
        responseData.code === "CUS-KYC-1000" &&
        responseData.data &&
        responseData.data.deviceKey &&
        responseData.data.encryptionKey) {

      try {
        // Initialize FaceTec with the values from the response
        await facetecService.initializeFaceTec(
          responseData.data.deviceKey,
          responseData.data.encryptionKey
        );
        // console.log("Status: " + facetecService.loadFaceTecSDK.getStatus());
        console.log('FaceTec SDK initialized successfully');
        responseData.faceTecInitialized = true;
      } catch (initError) {
        // console.log("Status: " + facetecService.loadFaceTecSDK.getStatus());
        console.error('Error initializing FaceTec SDK:', initError);
        responseData.faceTecError = initError.message || 'Failed to initialize FaceTec SDK';
      }
    }

    return responseData;
  } catch (error) {
    console.error('Error getting FaceTec session token with eKYC token:', error);
    throw error;
  }
};

/**
 * Perform Photo ID Scan using FaceTec SDK
 * @param {Object} headers - Optional additional headers to include in the request
 * @param {string} deviceKey - The device key for the Photo ID Scan
 * @returns {Promise<Object>} - The scan result data
 */
const performPhotoIDScan = async (headers = {}, deviceKey = null, sessionTokenResponse = null) => {

  try {
    if (!deviceKey) {
      throw new Error('deviceKey parameter is required for Photo ID Scan');
    }

    if (!sessionTokenResponse.faceTecInitialized) {
      throw new Error('FaceTec SDK not initialized properly');
    }

    const FaceTecSDK = await facetecService.loadFaceTecSDK();
    FaceTecSDK.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources");
    FaceTecSDK.setImagesDirectory("/core-sdk/FaceTec_images");

    const controller = {
      onComplete: (sessionResult, idScanResult, networkResponseStatus) => {
        return { sessionResult, idScanResult, networkResponseStatus };
      }
    };

    // ✅ prepare headers follow spec for PhotoIDScanProcessor
    const additionalHeaders = {
      'X-Session-Id': headers['X-Session-Id'] || sessionTokenResponse.data?.sessionId,
      'X-Ekyc-Token': sessionTokenResponse.data?.ekycToken || TokenStorage.getEkycToken(),
      'correlationid': headers.correlationid || (__webpack_require__(411).UuidGenerator).getUniqueId()
    };

    const processor = new PhotoIDScanProcessor(
      sessionTokenResponse.data.sessionFaceTec,
      controller,
      deviceKey,
      additionalHeaders  // ✅ send additional headers
    );

    return new Promise((resolve, reject) => {
      controller.onComplete = (sessionResult, idScanResult, networkResponseStatus) => {
        if (processor.isSuccess()) {
          resolve({ sessionResult, idScanResult, networkResponseStatus });
        } else {
          reject(new Error('ID scan failed'));
        }
      };
    });
  } catch (error) {
    console.error('Error performing photo ID scan:', error);
    throw error;
  }
};

/**
 * 1. Initialize eKYC SDK
 * @param {Object} options - Initialization options
 * @param {string} options.sessionId - Session ID for the eKYC session
 * @param {string} options.token - API token for authentication
 * @param {string} options.environment - Environment (development, staging, production)
 * @param {string} options.language - Language code (default: 'en')
 * @param {Function} options.initCallback - Optional initialization callback
 * @returns {Promise<Object>} Initialization result
 */
const initEkyc = async (options = {}) => {
  const {
    sessionId,
    token,
    environment = 'development',
    language = 'en',
    initCallback
  } = options;

  // Validate required parameters
  if (!sessionId) {
    throw new Error('sessionId is required for eKYC initialization');
  }
  if (!token) {
    throw new Error('token is required for eKYC initialization');
  }

  try {
    console.log('🚀 Initializing eKYC SDK with sessionId:', sessionId);

    // Generate and store device ID if not exists
    const { UuidGenerator } = __webpack_require__(411);
    let deviceId = TokenStorage.getToken('ekyc_device_id');
    if (!deviceId) {
      deviceId = UuidGenerator.getUniqueId();
      TokenStorage.storeToken('ekyc_device_id', deviceId);
    }

    // Store session information
    TokenStorage.storeToken('ekyc_session_id', sessionId);
    TokenStorage.storeToken('ekyc_api_token', token);
    TokenStorage.storeToken('ekyc_environment', environment);
    TokenStorage.storeToken('ekyc_language', language);

    // Prepare headers for session token request
    const sessionHeaders = {
      'Authorization': `Bearer ${token}`,
      'X-Session-Id': sessionId,
      'X-Ekyc-Device-Info': `browser|${deviceId}|${typeof window !== 'undefined' ? window.location.origin : 'unknown'}|${language}|${language.toUpperCase()}`
    };

    // Get session token
    console.log('📡 Getting session token...');
    const sessionTokenResponse = await getSessionToken(sessionHeaders, true);

    // Get FaceTec session token and initialize
    console.log('🎭 Getting FaceTec session token and initializing...');
    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(sessionHeaders, true);

    const result = {
      success: true,
      sessionToken: sessionTokenResponse,
      faceTecToken: faceTecResponse,
      environment,
      language,
      sessionId,
      initialized: true,
      faceTecInitialized: faceTecResponse.faceTecInitialized || false
    };

    console.log('✅ eKYC SDK initialized successfully');

    // Call initialization callback if provided
    if (initCallback && typeof initCallback === 'function') {
      initCallback(result);
    }

    return result;
  } catch (error) {
    console.error('❌ Error initializing eKYC SDK:', error);
    const errorResult = {
      success: false,
      error: error.message || 'Failed to initialize eKYC SDK',
      environment,
      language,
      sessionId,
      initialized: false
    };

    if (initCallback && typeof initCallback === 'function') {
      initCallback(errorResult);
    }

    throw error;
  }
};

/**
 * 2. OCR ID Card scanning
 * @param {Object} options - OCR options
 * @param {boolean} options.checkExpiredIdCard - Check if ID card is expired (default: true)
 * @param {boolean} options.checkDopa - Check against DOPA database (default: false)
 * @param {boolean} options.enableConfirmInfo - Enable confirmation screen (default: true)
 * @param {Function} options.callback - Result callback function
 * @returns {Promise<Object>} OCR results
 */
const ocrIdCard = async (options = {}) => {
  const {
    checkExpiredIdCard = true,
    checkDopa = false,
    enableConfirmInfo = true,
    callback
  } = options;

  try {
    console.log('📄 Starting OCR ID Card scan...');

    // Get stored session information
    const sessionId = TokenStorage.getToken('ekyc_session_id');
    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';

    if (!sessionId) {
      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
    }

    // Prepare headers
    const headers = {
      'X-Session-Id': sessionId,
      'X-Ekyc-Device-Info': `browser|${deviceId}`,
      'X-Ekyc-Token': TokenStorage.getToken('ekyc_token')
    };

    // Ensure FaceTec is initialized by getting session token
    console.log('🎭 Ensuring FaceTec SDK is initialized...');
    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(headers, true);

    if (!faceTecResponse.faceTecInitialized) {
      throw new Error('FaceTec SDK not properly initialized');
    }

    // Perform ID scan using existing logic
    console.log('🔍 Performing ID scan...');
    const scanResult = await performPhotoIDScan(headers, deviceId, faceTecResponse);

    const result = {
      success: true,
      ocrData: scanResult,
      checkExpiredIdCard,
      checkDopa,
      enableConfirmInfo,
      sessionId,
      scanType: 'id_card_ocr'
    };

    console.log('✅ OCR ID Card scan completed successfully');

    // Call callback if provided
    if (callback && typeof callback === 'function') {
      callback(result);
    }

    return result;
  } catch (error) {
    console.error('❌ Error performing OCR ID card scan:', error);
    const errorResult = {
      success: false,
      error: error.message || 'Failed to perform OCR ID card scan',
      checkExpiredIdCard,
      checkDopa,
      enableConfirmInfo
    };

    if (callback && typeof callback === 'function') {
      callback(errorResult);
    }

    throw error;
  }
};

/**
 * 3. OCR ID Card with facial verification
 * @param {Object} options - OCR and face verification options
 * @param {boolean} options.checkExpiredIdCard - Check if ID card is expired (default: true)
 * @param {boolean} options.checkDopa - Check against DOPA database (default: false)
 * @param {boolean} options.enableConfirmInfo - Enable confirmation screen (default: true)
 * @param {Function} options.ocrResultsCallback - Callback for OCR and face verification results
 * @returns {Promise<Object>} Combined OCR and face verification results
 */
const ocrIdCardVerifyByFace = async (options = {}) => {
  const {
    checkExpiredIdCard = true,
    checkDopa = false,
    enableConfirmInfo = true,
    ocrResultsCallback
  } = options;

  try {
    console.log('👤 Starting OCR ID Card with face verification...');

    // First perform OCR ID card scan
    const ocrResult = await ocrIdCard({
      checkExpiredIdCard,
      checkDopa,
      enableConfirmInfo
    });

    // Then perform liveness check for face verification
    const livenessResult = await livenessCheck();

    const result = {
      success: true,
      ocrData: ocrResult.ocrData,
      faceVerification: livenessResult,
      combined: true,
      checkExpiredIdCard,
      checkDopa,
      enableConfirmInfo,
      sessionId: ocrResult.sessionId,
      scanType: 'id_card_ocr_with_face'
    };

    console.log('✅ OCR ID Card with face verification completed successfully');

    // Call callback if provided
    if (ocrResultsCallback && typeof ocrResultsCallback === 'function') {
      ocrResultsCallback(result);
    }

    return result;
  } catch (error) {
    console.error('❌ Error performing OCR ID card with face verification:', error);
    const errorResult = {
      success: false,
      error: error.message || 'Failed to perform OCR ID card with face verification',
      checkExpiredIdCard,
      checkDopa,
      enableConfirmInfo
    };

    if (ocrResultsCallback && typeof ocrResultsCallback === 'function') {
      ocrResultsCallback(errorResult);
    }

    throw error;
  }
};

/**
 * 4. NDID digital identity verification
 * @param {Object} options - NDID verification options
 * @param {string} options.identifierType - Type of identifier (citizenId, passport, etc.)
 * @param {string} options.identifierValue - The identifier value
 * @param {string} options.serviceId - Service ID for NDID verification
 * @param {Function} options.ndidVerificationCallback - Callback for NDID verification results
 * @returns {Promise<Object>} NDID verification results
 */
const ndidVerification = async (options = {}) => {
  const {
    identifierType,
    identifierValue,
    serviceId,
    ndidVerificationCallback
  } = options;

  // Validate required parameters
  if (!identifierType) {
    throw new Error('identifierType is required for NDID verification');
  }
  if (!identifierValue) {
    throw new Error('identifierValue is required for NDID verification');
  }
  if (!serviceId) {
    throw new Error('serviceId is required for NDID verification');
  }

  try {
    console.log('🆔 Starting NDID verification...');

    // Get stored session information
    const sessionId = TokenStorage.getToken('ekyc_session_id');
    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';

    if (!sessionId) {
      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
    }

    // Simulate NDID verification process
    // In a real implementation, this would call the actual NDID verification API
    const verificationId = `ndid_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    const result = {
      success: true,
      ndidVerified: true,
      identifierType,
      identifierValue,
      serviceId,
      sessionId,
      verificationId,
      timestamp: new Date().toISOString(),
      // Simulated NDID response
      ndidResponse: {
        status: 'verified',
        confidence: 0.95,
        details: {
          identityConfirmed: true,
          documentValid: true,
          biometricMatch: true
        }
      }
    };

    console.log('✅ NDID verification completed successfully');

    // Call callback if provided
    if (ndidVerificationCallback && typeof ndidVerificationCallback === 'function') {
      ndidVerificationCallback(result);
    }

    return result;
  } catch (error) {
    console.error('❌ Error performing NDID verification:', error);
    const errorResult = {
      success: false,
      error: error.message || 'Failed to perform NDID verification',
      identifierType,
      identifierValue,
      serviceId
    };

    if (ndidVerificationCallback && typeof ndidVerificationCallback === 'function') {
      ndidVerificationCallback(errorResult);
    }

    throw error;
  }
};

/**
 * 5. Facial liveness detection
 * @param {Object} options - Liveness check options
 * @param {Function} options.livenessCheckCallback - Callback for liveness check results
 * @returns {Promise<Object>} Liveness check results
 */
const livenessCheck = async (options = {}) => {
  const {
    livenessCheckCallback
  } = options;

  try {
    console.log('👁️ Starting liveness check...');

    // Get stored session information
    const sessionId = TokenStorage.getToken('ekyc_session_id');
    const deviceId = TokenStorage.getToken('ekyc_device_id') || 'unknown';

    if (!sessionId) {
      throw new Error('eKYC SDK not initialized. Call initEkyc() first.');
    }

    // Prepare headers
    const headers = {
      'X-Session-Id': sessionId,
      'X-Ekyc-Device-Info': `browser|${deviceId}`
    };

    // Get FaceTec session token if not available
    console.log('🎭 Ensuring FaceTec SDK is initialized...');
    const faceTecResponse = await getFaceTecSessionTokenWithEkycToken(headers, true);

    if (!faceTecResponse.faceTecInitialized) {
      throw new Error('FaceTec SDK not properly initialized');
    }

    // Prepare additional headers for the API request
    const additionalHeaders = {
      'X-Device-Key': deviceId || faceTecResponse.data?.deviceKey,
      'X-Session-Id': sessionId,
      'X-Ekyc-Token': faceTecResponse.data?.ekycToken || TokenStorage.getEkycToken(),
      'X-Tid': UuidGenerator.getUniqueId(),
      'correlationid': UuidGenerator.getUniqueId()
    };

    // Create a controller for the LivenessCheckProcessor
    const controller = {
      onComplete: (sessionResult, networkResponseStatus) => {
        return { sessionResult, networkResponseStatus };
      }
    };

    // Create a promise to handle the liveness check result
    const livenessPromise = new Promise((resolve, reject) => {
      // Override the onComplete method to handle the result
      controller.onComplete = (sessionResult, networkResponseStatus) => {
        // Check if the processor was successful instead of relying only on sessionResult status
        if (processor.isSuccess()) {
          resolve({ sessionResult, networkResponseStatus });
        } else if (sessionResult && sessionResult.status === FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully) {
          resolve({ sessionResult, networkResponseStatus });
        } else {
          reject(new Error('Liveness check failed or was cancelled'));
        }
      };
    });

    // Create and start the LivenessCheckProcessor
    console.log('🔍 Creating LivenessCheckProcessor...');
    const LivenessCheckProcessor = __webpack_require__(266);
    const processor = new LivenessCheckProcessor(
      faceTecResponse.data.sessionFaceTec,
      controller,
      additionalHeaders['X-Device-Key'],
      additionalHeaders
    );

    // Wait for the liveness check to complete
    console.log('⏳ Waiting for liveness check to complete...');
    const livenessResult = await livenessPromise;

    // Process the result
    const result = {
      success: processor.isSuccess(),
      liveness: {
        sessionId: livenessResult.sessionResult?.sessionId || `liveness_${Date.now()}`,
        livenessScore: 1.0, // FaceTec doesn't provide a score, but it's either pass or fail
        isLive: processor.isSuccess(),
        confidence: processor.isSuccess() ? 'high' : 'low',
        timestamp: new Date().toISOString()
      },
      sessionId,
      deviceId,
      faceTecInitialized: true,
      rawResult: livenessResult
    };

    console.log('✅ Liveness check completed successfully:', result.success);

    // Call callback if provided
    if (livenessCheckCallback && typeof livenessCheckCallback === 'function') {
      livenessCheckCallback({
        responseCode: result.success ? 'CUS-KYC-1000' : 'ERROR',
        responseDescription: result.success ? 'Liveness check successful' : 'Liveness check failed',
        success: result.success,
        data: result
      });
    }

    return result;
  } catch (error) {
    console.error('❌ Error performing liveness check:', error);
    const errorResult = {
      success: false,
      error: error.message || 'Failed to perform liveness check'
    };

    if (livenessCheckCallback && typeof livenessCheckCallback === 'function') {
      livenessCheckCallback({
        responseCode: 'ERROR',
        responseDescription: error.message || 'Failed to perform liveness check',
        success: false,
        error: error.message
      });
    }

    throw error;
  }
};

// Export the functions
module.exports = {
  formatCurrency,
  greet,
  getSessionToken,
  getStoredEkycToken,
  clearEkycToken,
  getFaceTecSessionTokenWithEkycToken,
  performPhotoIDScan,
  // New 5 main SDK functions
  initEkyc,
  ocrIdCard,
  ocrIdCardVerifyByFace,
  ndidVerification,
  livenessCheck
};


/***/ }),

/***/ 382:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

// Import services
const FaceTecService = __webpack_require__(706);

// Create instance of the service
const faceTecService = new FaceTecService();

/**
 * Load the FaceTecSDK
 * @returns {Promise<Object>} - A promise that resolves to the FaceTecSDK
 */
const loadFaceTecSDK = () => {
  return faceTecService.loadFaceTecSDK();
};

/**
 * Initialize the FaceTecSDK
 * @param {string} deviceKeyIdentifier - The device key identifier
 * @param {string} publicEncryptionKey - The public encryption key
 * @returns {Promise<boolean>} - A promise that resolves to true if initialization was successful
 */
const initializeFaceTec = (deviceKeyIdentifier, publicEncryptionKey) => {
  return faceTecService.initializeFaceTec(deviceKeyIdentifier, publicEncryptionKey);
};

/**
 * Get the FaceTecSDK version
 * @returns {Promise<string>} - A promise that resolves to the FaceTecSDK version
 */
const getFaceTecVersion = () => {
  return faceTecService.getFaceTecVersion();
};

// Export the functions
module.exports = {
  loadFaceTecSDK,
  initializeFaceTec,
  getFaceTecVersion
};


/***/ }),

/***/ 411:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const UuidGenerator = __webpack_require__(599);
const TokenStorage = __webpack_require__(641);

module.exports = {
  UuidGenerator,
  TokenStorage
};


/***/ }),

/***/ 518:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const DeveloperStatusMessages = __webpack_require__(719);

/**
 * Data source for ID Scan API operations
 * Handles direct API communication for ID scanning functionality
 */
class IDScanDataSource {
    constructor() {
        this.baseUrl = '/api'; // Use Next.js API routes as proxy
    }

    /**
     * Post ID scan data to the backend API
     * @param {Object} scanData - The scan data including images and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - API response
     */
    async postIDScanOnly(scanData, headers = {}, onProgress = null) {
        return new Promise((resolve, reject) => {
            const startTime = performance.now();
            
            try {
                const url = `${this.baseUrl}/idscan-only`;
                DeveloperStatusMessages.logApiCall(url, 'POST', 'Starting request');
                
                // Create XMLHttpRequest for progress tracking
                const xhr = new XMLHttpRequest();
                
                // Set up progress tracking
                if (onProgress && typeof onProgress === 'function') {
                    xhr.upload.onprogress = function(event) {
                        if (event.lengthComputable) {
                            const progress = event.loaded / event.total;
                            DeveloperStatusMessages.logIDScanProgress('Uploading', progress);
                            onProgress(progress);
                        }
                    };
                }

                // Set up response handlers
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === XMLHttpRequest.DONE) {
                        const endTime = performance.now();
                        
                        try {
                            if (xhr.status >= 200 && xhr.status < 300) {
                                const responseData = JSON.parse(xhr.responseText);
                                DeveloperStatusMessages.logPerformance('IDScanDataSource.postIDScanOnly', startTime, endTime);
                                DeveloperStatusMessages.logApiCall(url, 'POST', `Success (${xhr.status})`);
                                DeveloperStatusMessages.logData('API Response', {
                                    status: xhr.status,
                                    wasProcessed: responseData.wasProcessed,
                                    error: responseData.error,
                                    hasScanResultBlob: !!responseData.scanResultBlob
                                });
                                resolve(responseData);
                            } else {
                                DeveloperStatusMessages.logPerformance('IDScanDataSource.postIDScanOnly (failed)', startTime, endTime);
                                DeveloperStatusMessages.logError(`API call failed with status ${xhr.status}`);
                                reject(new Error(`HTTP error! status: ${xhr.status}`));
                            }
                        } catch (parseError) {
                            DeveloperStatusMessages.logError('Failed to parse API response', parseError);
                            reject(new Error('Failed to parse response JSON'));
                        }
                    }
                };

                xhr.onerror = function() {
                    const endTime = performance.now();
                    DeveloperStatusMessages.logPerformance('IDScanDataSource.postIDScanOnly (network error)', startTime, endTime);
                    DeveloperStatusMessages.logError('Network request failed');
                    reject(new Error('Network request failed'));
                };

                // Open the request
                xhr.open('POST', url);

                // Set headers
                xhr.setRequestHeader('Content-Type', 'application/json');
                Object.keys(headers).forEach(key => {
                    if (headers[key] !== undefined) {
                        xhr.setRequestHeader(key, headers[key]);
                    }
                });

                // Send the request
                const jsonData = JSON.stringify(scanData);
                DeveloperStatusMessages.logMessage(`Sending request to ${url} with ${Object.keys(scanData).length} data fields`);
                xhr.send(jsonData);

            } catch (error) {
                DeveloperStatusMessages.logError('IDScanDataSource - postIDScanOnly error', error);
                reject(error);
            }
        });
    }
}

module.exports = IDScanDataSource; 

/***/ }),

/***/ 548:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const UuidGenerator = __webpack_require__(599);
const { TokenStorage } = __webpack_require__(411);

/**
 * AuthApiDataSource
 * Data source for authentication API
 */
class AuthApiDataSource {
  /**
   * Get a session token from the API
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<Object>} - A promise that resolves to the API response
   */
  async getSessionToken(headers = {}) {
    try {
      // Use the Next.js API route to proxy the request
      const url = '/api/session-token';

      // Generate UUIDs for headers if not provided
      const deviceId = headers['X-Ekyc-Device-Info'] ? null : UuidGenerator.getDeviceId();
      // Use getUniqueId() to generate a new UUID every time for session-related IDs
      const tId = UuidGenerator.getUniqueId();
      // Use provided session ID or generate a new one and store it
      const sessionId = headers['X-Session-Id'] || UuidGenerator.getUniqueId();
      // Store the session ID for future use
      if (!headers['X-Session-Id']) {
        TokenStorage.storeSessionId(sessionId);
      }

      const correlationid = UuidGenerator.getUniqueId();

      // Merge default headers with any additional headers
      const requestHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Ekyc-Sdk-Version': '1.0.0',
        'X-Ekyc-Device-Info': `browser|${deviceId}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,
        'X-Session-Id': `${sessionId}`,
        'X-Tid': `${tId}`,
        'correlationid': `${correlationid}`,
        ...headers
      };

      // Only add Authorization header if it exists and is not undefined
      if (headers['Authorization']) {
        requestHeaders['Authorization'] = headers['Authorization'];
      }

      // Make the GET request with headers only (no body)
      const response = await fetch(url, {
        method: 'GET',
        headers: requestHeaders,
      });

      // Check if the response is ok (status in the range 200-299)
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse and return the JSON response
      return await response.json();
    } catch (error) {
      console.error('Error getting session token:', error);
      throw error;
    }
  }

  /**
   * Get a FaceTec session token from the API
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<Object>} - A promise that resolves to the API response
   */
  async getFaceTecSessionToken(headers = {}) {
    try {
      // Use the Next.js API route to proxy the request
      const url = '/api/facetec-session-token';

      // Generate UUIDs for headers if not provided
      const deviceId = headers['X-Ekyc-Device-Info'] ? null : UuidGenerator.getDeviceId();
      // Use getUniqueId() to generate a new UUID every time for session-related IDs
      const tId = UuidGenerator.getUniqueId();
      // Use the stored session ID if available, or the one provided in headers, or generate a new one
      const storedSessionId = TokenStorage.getSessionId();
      const sessionId = headers['X-Session-Id'] || storedSessionId || UuidGenerator.getUniqueId();
      const correlationid = UuidGenerator.getUniqueId();

      // Merge default headers with any additional headers
      const requestHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Ekyc-Sdk-Version': '1.0.0',
        'X-Ekyc-Device-Info': `browser|${deviceId}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,
        'X-Session-Id': `${sessionId}`,
        'X-Tid': `${tId}`,
        'correlationid': `${correlationid}`,
        ...headers
      };

      // Only add Authorization header if it exists and is not undefined
      if (headers['Authorization']) {
        requestHeaders['Authorization'] = headers['Authorization'];
      }

      // Make the GET request with headers only (no body)
      const response = await fetch(url, {
        method: 'GET',
        headers: requestHeaders,
      });

      // Check if the response is ok (status in the range 200-299)
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse and return the JSON response
      return await response.json();
    } catch (error) {
      console.error('Error getting FaceTec session token:', error);
      throw error;
    }
  }

  /**
   * Get a FaceTec session token from the API using the stored eKYC token
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<Object>} - A promise that resolves to the API response
   */
  async getFaceTecSessionTokenWithEkycToken(headers = {}) {
    try {
      // Use the Next.js API route to proxy the request
      const url = '/api/facetec-session-token';

      // Get the stored eKYC token
      const ekycToken = TokenStorage.getEkycToken();
      if (!ekycToken) {
        throw new Error('No eKYC token found. Please get a session token first.');
      }

      // Generate UUIDs for headers if not provided
      const deviceId = headers['X-Ekyc-Device-Info'] ? null : UuidGenerator.getDeviceId();
      // Use getUniqueId() to generate a new UUID every time for session-related IDs
      const tId = headers['X-Tid'] || UuidGenerator.getUniqueId();
      // Use the stored session ID if available, or the one provided in headers, or generate a new one
      const storedSessionId = TokenStorage.getSessionId();
      const sessionId = headers['X-Session-Id'] || storedSessionId || UuidGenerator.getUniqueId();
      const correlationid = headers['correlationid'] || UuidGenerator.getUniqueId();

      // Merge default headers with any additional headers
      const requestHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Ekyc-Sdk-Version': headers['X-Ekyc-Sdk-Version'] || '1.0.0',
        'X-Ekyc-Device-Info': headers['X-Ekyc-Device-Info'] || `browser|${deviceId}`,
        'X-Session-Id': `${sessionId}`,
        'X-Tid': `${tId}`,
        'correlationid': `${correlationid}`,
        'X-Ekyc-Token': ekycToken,
        ...headers
      };

      // Only add Authorization header if it exists and is not undefined
      if (headers['Authorization']) {
        requestHeaders['Authorization'] = headers['Authorization'];
      }
      // Make the GET request with headers only (no body)
      const response = await fetch(url, {
        method: 'GET',
        headers: requestHeaders,
      });

      // Check if the response is ok (status in the range 200-299)
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse and return the JSON response
      return await response.json();
    } catch (error) {
      console.error('Error getting FaceTec session token with eKYC token:', error);
      throw error;
    }
  }
}

module.exports = AuthApiDataSource;


/***/ }),

/***/ 592:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const Currency = __webpack_require__(189);

/**
 * FormatCurrencyUseCase
 * Use case for formatting a currency amount
 */
class FormatCurrencyUseCase {
  /**
   * Execute the use case
   * @param {number} amount - The amount to format
   * @param {string} currencyCode - The currency code (e.g., 'USD')
   * @returns {string} - The formatted currency amount
   */
  execute(amount, currencyCode = 'USD') {
    const currency = new Currency(amount, currencyCode);
    return currency.format();
  }
}

module.exports = FormatCurrencyUseCase;


/***/ }),

/***/ 599:
/***/ ((module) => {

/**
 * Utility class for generating UUIDs
 */
class UuidGenerator {
  /**
   * Generate a UUID v4
   * @returns {string} - A UUID v4 string
   */
  static generateUuid() {
    // Implementation of UUID v4 generation
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Get a device identifier
   * This will generate a UUID and store it in localStorage if running in a browser
   * If the UUID already exists in localStorage, it will be reused
   * @returns {string} - A device identifier
   */
  static getDeviceId() {
    if (typeof window !== 'undefined' && window.localStorage) {
      // Running in browser, check localStorage
      let deviceId = localStorage.getItem('ekyc_device_id');
      if (!deviceId) {
        // Generate a new UUID if not found in localStorage
        deviceId = this.generateUuid();
        localStorage.setItem('ekyc_device_id', deviceId);
      }
      return deviceId;
    } else {
      // Not running in browser, generate a new UUID
      return this.generateUuid();
    }
  }

  /**
   * Generate a new UUID every time
   * Unlike getDeviceId, this does not store or reuse UUIDs
   * @returns {string} - A new UUID v4 string
   */
  static getUniqueId() {
    return this.generateUuid();
  }
}

module.exports = UuidGenerator;


/***/ }),

/***/ 641:
/***/ ((module) => {

/**
 * Utility class for storing and retrieving tokens
 */
class TokenStorage {
  /**
   * Store a token in localStorage
   * @param {string} key - The key to store the token under
   * @param {string} token - The token to store
   * @returns {boolean} - True if the token was stored successfully
   */
  static storeToken(key, token) {
    if (typeof window !== 'undefined' && window.localStorage && token) {
      try {
        localStorage.setItem(key, token);
        return true;
      } catch (error) {
        console.error('Error storing token:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Retrieve a token from localStorage
   * @param {string} key - The key the token is stored under
   * @returns {string|null} - The token or null if not found
   */
  static getToken(key) {
    if (typeof window !== 'undefined' && window.localStorage) {
      return localStorage.getItem(key);
    }
    return null;
  }

  /**
   * Remove a token from localStorage
   * @param {string} key - The key the token is stored under
   * @returns {boolean} - True if the token was removed successfully
   */
  static removeToken(key) {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.removeItem(key);
        return true;
      } catch (error) {
        console.error('Error removing token:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Store the eKYC token
   * @param {string} token - The eKYC token to store
   * @returns {boolean} - True if the token was stored successfully
   */
  static storeEkycToken(token) {
    return this.storeToken('ekyc_token', token);
  }

  /**
   * Get the stored eKYC token
   * @returns {string|null} - The eKYC token or null if not found
   */
  static getEkycToken() {
    return this.getToken('ekyc_token');
  }

  /**
   * Remove the stored eKYC token
   * @returns {boolean} - True if the token was removed successfully
   */
  static removeEkycToken() {
    return this.removeToken('ekyc_token');
  }

  /**
   * Store the session ID
   * @param {string} sessionId - The session ID to store
   * @returns {boolean} - True if the session ID was stored successfully
   */
  static storeSessionId(sessionId) {
    return this.storeToken('ekyc_session_id', sessionId);
  }

  /**
   * Get the stored session ID
   * @returns {string|null} - The session ID or null if not found
   */
  static getSessionId() {
    return this.getToken('ekyc_session_id');
  }

  /**
   * Remove the stored session ID
   * @returns {boolean} - True if the session ID was removed successfully
   */
  static removeSessionId() {
    return this.removeToken('ekyc_session_id');
  }
}

module.exports = TokenStorage;


/***/ }),

/***/ 706:
/***/ ((module) => {

/**
 * FaceTecService
 * Service for interacting with the FaceTec SDK
 */
class FaceTecService {
  /**
   * Load the FaceTecSDK
   * @returns {Promise<Object>} - A promise that resolves to the FaceTecSDK
   */
  loadFaceTecSDK() {
    return new Promise((resolve, reject) => {
      // Check if FaceTecSDK is already loaded
      if (typeof window !== 'undefined' && window.FaceTecSDK) {
        resolve(window.FaceTecSDK);
        return;
      }

      // Create a script element to load the FaceTecSDK
      const script = document.createElement('script');
      script.src = '/core-sdk/FaceTecSDK.js/FaceTecSDK.js'; // Path to the FaceTecSDK script
      script.async = true;
      script.onload = () => {
        if (window.FaceTecSDK) {
          resolve(window.FaceTecSDK);
        } else {
          reject(new Error('FaceTecSDK not found after loading script'));
        }
      };
      script.onerror = () => {
        reject(new Error('Failed to load FaceTecSDK script'));
      };

      // Add the script to the document
      document.head.appendChild(script);
    });
  }

  /**
   * Initialize the FaceTecSDK
   * @param {string} deviceKeyIdentifier - The device key identifier
   * @param {string} publicEncryptionKey - The public encryption key
   * @returns {Promise<boolean>} - A promise that resolves to true if initialization was successful
   */
  async initializeFaceTec(deviceKeyIdentifier, publicEncryptionKey) {
    try {
      const FaceTecSDK = await this.loadFaceTecSDK();
      FaceTecSDK.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources");
      FaceTecSDK.setImagesDirectory("/core-sdk/FaceTec_images");
      return new Promise((resolve, reject) => {
        FaceTecSDK.initializeInDevelopmentMode(
          deviceKeyIdentifier,
          publicEncryptionKey,
          (initializedSuccessfully) => {
            if (initializedSuccessfully) {
              console.log('FaceTecSDK initialized successfully');
              resolve(true);
            } else {
              console.error('FaceTecSDK failed to initialize');
              reject(new Error('FaceTecSDK failed to initialize'));
            }
          }
        );
      });
    } catch (error) {
      console.error('Error loading FaceTecSDK:', error);
      throw error;
    }
  }

  /**
   * Get the FaceTecSDK version
   * @returns {Promise<string>} - A promise that resolves to the FaceTecSDK version
   */
  async getFaceTecVersion() {
    try {
      const FaceTecSDK = await this.loadFaceTecSDK();
      return FaceTecSDK.version();
    } catch (error) {
      console.error('Error getting FaceTecSDK version:', error);
      throw error;
    }
  }
}

module.exports = FaceTecService;


/***/ }),

/***/ 719:
/***/ ((module) => {

/**
 * Developer Status Messages Utility
 * Provides logging and status tracking functionality for development purposes
 */
class DeveloperStatusMessages {
    
    /**
     * Log a message with timestamp
     * @param {string} message - The message to log
     * @param {string} level - Log level (info, warn, error, success)
     */
    static logMessage(message, level = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
        
        switch (level) {
            case 'error':
                console.error(`${prefix} ${message}`);
                break;
            case 'warn':
                console.warn(`${prefix} ${message}`);
                break;
            case 'success':
                console.log(`%c${prefix} ${message}`, 'color: green; font-weight: bold;');
                break;
            case 'info':
            default:
                console.log(`${prefix} ${message}`);
                break;
        }
    }

    /**
     * Log FaceTec SDK status
     * @param {string} status - The status message
     */
    static logFaceTecStatus(status) {
        this.logMessage(`FaceTec SDK: ${status}`, 'info');
    }

    /**
     * Log API call status
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method
     * @param {string} status - Status message
     */
    static logApiCall(endpoint, method, status) {
        this.logMessage(`API ${method} ${endpoint}: ${status}`, 'info');
    }

    /**
     * Log success message
     * @param {string} message - Success message
     */
    static logSuccess(message) {
        this.logMessage(message, 'success');
    }

    /**
     * Log error message
     * @param {string} message - Error message
     * @param {Error} error - Error object (optional)
     */
    static logError(message, error = null) {
        let fullMessage = message;
        if (error) {
            fullMessage += ` - ${error.message}`;
        }
        this.logMessage(fullMessage, 'error');
        
        if (error && error.stack) {
            console.error('Stack trace:', error.stack);
        }
    }

    /**
     * Log warning message
     * @param {string} message - Warning message
     */
    static logWarning(message) {
        this.logMessage(message, 'warn');
    }

    /**
     * Log ID scan progress
     * @param {string} step - Current step
     * @param {number} progress - Progress percentage (0-100)
     */
    static logIDScanProgress(step, progress = null) {
        let message = `ID Scan: ${step}`;
        if (progress !== null) {
            message += ` (${Math.round(progress * 100)}%)`;
        }
        this.logMessage(message, 'info');
    }

    /**
     * Log liveness check progress
     * @param {string} step - Current step
     * @param {number} progress - Progress percentage (0-1)
     */
    static logLivenessProgress(step, progress = null) {
        let message = `Liveness Check: ${step}`;
        if (progress !== null) {
            message += ` (${Math.round(progress * 100)}%)`;
        }
        this.logMessage(message, 'info');
    }

    /**
     * Log session information
     * @param {string} sessionId - Session ID
     * @param {string} action - Action being performed
     */
    static logSession(sessionId, action) {
        this.logMessage(`Session ${sessionId}: ${action}`, 'info');
    }

    /**
     * Clear console (for development)
     */
    static clearConsole() {
        if (typeof console.clear === 'function') {
            console.clear();
        }
    }

    /**
     * Log object/data for debugging
     * @param {string} label - Label for the data
     * @param {any} data - Data to log
     */
    static logData(label, data) {
        console.group(`📊 ${label}`);
        console.log(data);
        console.groupEnd();
    }

    /**
     * Log performance timing
     * @param {string} operation - Operation name
     * @param {number} startTime - Start time (performance.now())
     * @param {number} endTime - End time (performance.now())
     */
    static logPerformance(operation, startTime, endTime) {
        const duration = endTime - startTime;
        this.logMessage(`Performance: ${operation} took ${duration.toFixed(2)}ms`, 'info');
    }
}

module.exports = DeveloperStatusMessages; 

/***/ }),

/***/ 777:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const DeveloperStatusMessages = __webpack_require__(719);

/**
 * Data source for Liveness Check API operations
 * Handles direct API communication for liveness check functionality
 */
class LivenessCheckDataSource {
    constructor() {
        this.baseUrl = '/api'; // Use Next.js API routes as proxy
    }

    /**
     * Post liveness check data to the backend API
     * @param {Object} livenessData - The liveness data including face scan and audit trail
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - API response
     */
    async postLivenessCheck(livenessData, headers = {}, onProgress = null) {
        return new Promise((resolve, reject) => {
            const startTime = performance.now();
            
            try {
                const url = `${this.baseUrl}/enrollment-3d`;

                DeveloperStatusMessages.logApiCall(url, 'POST', 'Starting request');
                
                // Create XMLHttpRequest for progress tracking
                const xhr = new XMLHttpRequest();
                
                // Set up progress tracking
                if (onProgress && typeof onProgress === 'function') {
                    xhr.upload.onprogress = function(event) {
                        if (event.lengthComputable) {
                            const progress = event.loaded / event.total;
                            DeveloperStatusMessages.logLivenessProgress('Uploading', progress);
                            onProgress(progress);
                        }
                    };
                }

                // Set up response handlers
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === XMLHttpRequest.DONE) {
                        const endTime = performance.now();
                        
                        try {
                            if (xhr.status >= 200 && xhr.status < 300) {
                                const responseData = JSON.parse(xhr.responseText);
                                DeveloperStatusMessages.logPerformance('LivenessCheckDataSource.postLivenessCheck', startTime, endTime);
                                DeveloperStatusMessages.logApiCall(url, 'POST', `Success (${xhr.status})`);
                                DeveloperStatusMessages.logData('API Response', {
                                    status: xhr.status,
                                    wasProcessed: responseData.wasProcessed,
                                    error: responseData.error,
                                    hasScanResultBlob: !!responseData.scanResultBlob
                                });
                                resolve(responseData);
                            } else {
                                DeveloperStatusMessages.logPerformance('LivenessCheckDataSource.postLivenessCheck (failed)', startTime, endTime);
                                DeveloperStatusMessages.logError(`API call failed with status ${xhr.status}`);
                                reject(new Error(`HTTP error! status: ${xhr.status}`));
                            }
                        } catch (error) {
                            DeveloperStatusMessages.logError('Error parsing response', error);
                            reject(error);
                        }
                    }
                };
                
                // Set up error handler
                xhr.onerror = function() {
                    DeveloperStatusMessages.logError('Network error occurred');
                    reject(new Error('Network error occurred'));
                };
                
                // Open and send the request
                xhr.open('POST', url, true);
                
                // Set headers
                xhr.setRequestHeader('Content-Type', 'application/json');
                for (const [key, value] of Object.entries(headers)) {
                    if (value !== undefined && value !== null) {
                        xhr.setRequestHeader(key, value);
                    }
                }
                
                // Send the request
                const requestBody = JSON.stringify(livenessData);
                DeveloperStatusMessages.logData('Request Body Keys', Object.keys(livenessData));
                xhr.send(requestBody);
            } catch (error) {
                DeveloperStatusMessages.logError('Error in postLivenessCheck', error);
                reject(error);
            }
        });
    }
}

module.exports = LivenessCheckDataSource;


/***/ }),

/***/ 863:
/***/ ((module) => {

/**
 * GreetUseCase
 * Use case for generating a greeting message
 */
class GreetUseCase {
  /**
   * Execute the use case
   * @param {string} name - The name to greet
   * @returns {string} - The greeting message
   */
  execute(name) {
    return `Hello 12, ${name}!`;
  }
}

module.exports = GreetUseCase;


/***/ }),

/***/ 955:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

//
// Welcome to the annotated FaceTec Device SDK core code for performing a secure Photo ID Scan.
//
//
// This is an example self-contained class to perform Photo ID Scans with the FaceTec SDK.
// You may choose to further componentize parts of this in your own Apps based on your specific requirements.
//

const PostIDScanOnlyUseCase = __webpack_require__(0);
const DeveloperStatusMessages = __webpack_require__(719);

var PhotoIDScanProcessor = /** @class */ (function () {
    function PhotoIDScanProcessor(sessionToken, sampleAppControllerReference, deviceKey, additionalHeaders) {
        var _this = this;
        this.latestNetworkRequest = new XMLHttpRequest();
        this.deviceKey = deviceKey || null; // Store the device key
        this.additionalHeaders = additionalHeaders || {}; // get additional headers
        this.postIDScanOnlyUseCase = new PostIDScanOnlyUseCase(); // Initialize use case
        
        //
        // Part 2:  Handling the Result of a Photo ID Scan
        //
        this.processIDScanResultWhileFaceTecSDKWaits = function (idScanResult, idScanResultCallback) {
            _this.latestIDScanResult = idScanResult;
            //
            // Part 3:  Handles early exit scenarios where there is no Photo ID Scan result to handle -- i.e. User Cancellation, Timeouts, etc.
            //
            if (idScanResult.status !== FaceTecSDK.FaceTecIDScanStatus.Success) {
                _this.latestNetworkRequest.abort();
                _this.latestNetworkRequest = new XMLHttpRequest();
                idScanResultCallback.cancel();
                return;
            }
            // IMPORTANT:  FaceTecSDK.FaceTecIDScanStatus.Success DOES NOT mean the Photo ID Scan was Successful.
            // It simply means the User completed the Session.  You still need to perform the Photo ID Scan result checks on your Servers.
            
            //
            // Part 4: Use Clean Architecture - Call UseCase instead of direct API
            //
            _this.executeIDScanUseCase(idScanResult, idScanResultCallback);
        };

        //
        // New method: Execute ID Scan using Clean Architecture UseCase
        //
        this.executeIDScanUseCase = async function(idScanResult, idScanResultCallback) {
            try {
                // Create progress callback for upload tracking
                const onProgress = function(progress) {
                    idScanResultCallback.uploadProgress(progress);
                };

                // Execute the use case
                const result = await _this.postIDScanOnlyUseCase.execute({
                    idScanResult: idScanResult,
                    deviceKey: _this.deviceKey,
                    additionalHeaders: _this.additionalHeaders,
                    onProgress: onProgress
                });

                // Handle the result
                if (result.success) {
                    // Configure success messages
                    FaceTecSDK.FaceTecCustomization.setIDScanResultScreenMessageOverrides(
                        "Front Scan Complete", // Successful scan of ID front-side (ID Types with no back-side).
                        "Front of ID<br/>Scanned", // Successful scan of ID front-side (ID Types that do have a back-side).
                        "ID Scan Complete", // Successful scan of the ID back-side.
                        "Passport Scan Complete", // Successful scan of a Passport
                        "Photo ID Scan<br/>Complete", // Successful upload of final Photo ID Scan result containing User-Confirmed ID Text.
                        "ID Photo Capture<br/>Complete", // Successful upload of final Photo ID Scan result but scan is flagged as requiring additional review.
                        "Face Didn't Match<br/>Highly Enough", // Case where a Retry is needed because the Face on the Photo ID did not Match the User's Face highly enough.
                        "ID Document<br/>Not Fully Visible", // Case where a Retry is needed because a Full ID was not detected with high enough confidence.
                        "ID Text Not Legible", // Case where a Retry is needed because the OCR did not produce good enough results and the User should Retry with a better capture.
                        "ID Type Mismatch<br/>Please Try Again" // Case where there is likely no OCR Template installed for the document the User is attempting to scan.
                    );
                    
                    // Proceed to next step with scanResultBlob
                    idScanResultCallback.proceedToNextStep(result.scanResultBlob);
                } else {
                    // Handle error case
                    _this.cancelDueToNetworkError(result.errorMessage || "Unexpected API response, cancelling out.", idScanResultCallback);
                }
            } catch (error) {
                console.error('PhotoIDScanProcessor - executeIDScanUseCase error:', error);
                _this.cancelDueToNetworkError(error.message || "Exception while handling API response, cancelling out.", idScanResultCallback);
            }
        };

        //
        // Part 9:  This function gets called after the FaceTec SDK is completely done.  There are no parameters because you have already been passed all data in the processSessionWhileFaceTecSDKWaits function and have already handled all of your own results.
        //
        this.onFaceTecSDKCompletelyDone = function () {
            //
            // DEVELOPER NOTE:  onFaceTecSDKCompletelyDone() is called after the Session has completed or you signal the FaceTec SDK with cancel().
            // Calling a custom function on the Sample App Controller is done for demonstration purposes to show you that here is where you get control back from the FaceTec SDK.
            //
            // If the Photo ID Scan was processed get the success result from isCompletelyDone
            if (_this.latestIDScanResult !== null) {
                _this.success = _this.latestIDScanResult.isCompletelyDone;
            }
            // Log success message
            if (_this.success) {
                DeveloperStatusMessages.logMessage("Id Scan Complete");
            }
            _this.sampleAppControllerReference.onComplete(null, _this.latestIDScanResult, 200); // Use 200 as default status
        };

        // Helper function to ensure the session is only cancelled once
        this.cancelDueToNetworkError = function (networkErrorMessage, faceTecIdScanResultCallback) {
            if (_this.cancelledDueToNetworkError === false) {
                console.error(networkErrorMessage);
                _this.cancelledDueToNetworkError = true;
                faceTecIdScanResultCallback.cancel();
            }
        };

        //
        // DEVELOPER NOTE:  This public convenience method is for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In your code, you may not even want or need to do this.
        //
        this.isSuccess = function () {
            return _this.success;
        };

        //
        // DEVELOPER NOTE:  These properties are for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In the code in your own App, you can pass around signals, flags, intermediates, and results however you would like.
        //
        this.success = false;
        this.sampleAppControllerReference = sampleAppControllerReference;
        this.latestIDScanResult = null;
        this.cancelledDueToNetworkError = false;

        // In v9.2.2+, configure the messages that will be displayed to the User in each of the possible cases.
        // Based on the internal processing and decision logic about how the flow gets advanced, the FaceTec SDK will use the appropriate, configured message.
        FaceTecSDK.FaceTecCustomization.setIDScanUploadMessageOverrides(
            "Uploading<br/>Encrypted<br/>ID Scan", // Upload of ID front-side has started.
            "Still Uploading...<br/>Slow Connection", // Upload of ID front-side is still uploading to Server after an extended period of time.
            "Upload Complete", // Upload of ID front-side to the Server is complete.
            "Processing<br/>ID Scan", // Upload of ID front-side is complete and we are waiting for the Server to finish processing and respond.
            "Uploading<br/>Encrypted<br/>Back of ID", // Upload of ID back-side has started.
            "Still Uploading...<br/>Slow Connection", // Upload of ID back-side is still uploading to Server after an extended period of time.
            "Upload Complete", // Upload of ID back-side to Server is complete.
            "Processing<br/>Back of ID", // Upload of ID back-side is complete and we are waiting for the Server to finish processing and respond.
            "Uploading<br/>Your Confirmed Info", // Upload of User Confirmed Info has started.
            "Still Uploading...<br/>Slow Connection", // Upload of User Confirmed Info is still uploading to Server after an extended period of time.
            "Info Saved", // Upload of User Confirmed Info to the Server is complete.
            "Processing" // Upload of User Confirmed Info is complete and we are waiting for the Server to finish processing and respond.
        );

        //
        // Part 1:  Starting the FaceTec Photo ID Scan Session
        //
        // Required parameters:
        // - FaceTecIDScanProcessor:  A class that implements FaceTecIDScanProcessor, which handles the Photo ID Scan when the User completes a Session.  In this example, "this" implements the class.
        // - sessionToken:  A valid Session Token you just created by calling your API to get a Session Token from the Server SDK.
        //
        new FaceTecSDK.FaceTecSession(this, sessionToken);
    }
    return PhotoIDScanProcessor;
}());

// Export the PhotoIDScanProcessor class for use in Node.js/webpack environments
module.exports = PhotoIDScanProcessor;


/***/ }),

/***/ 985:
/***/ ((module) => {

/**
 * GetFaceTecSessionTokenWithEkycTokenUseCase
 * Use case for getting a FaceTec session token using the stored eKYC token
 */
class GetFaceTecSessionTokenWithEkycTokenUseCase {
  /**
   * @param {Object} authRepository - The authentication repository
   */
  constructor(authRepository) {
    this.authRepository = authRepository;
  }

  /**
   * Execute the use case
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity
   */
  async execute(headers = {}) {
    return await this.authRepository.getFaceTecSessionTokenWithEkycToken(headers);
  }
}

module.exports = GetFaceTecSessionTokenWithEkycTokenUseCase;


/***/ }),

/***/ 995:
/***/ ((module) => {

/**
 * GetSessionTokenUseCase
 * Use case for getting a session token from the authentication API
 */
class GetSessionTokenUseCase {
  /**
   * @param {Object} authRepository - The authentication repository
   */
  constructor(authRepository) {
    this.authRepository = authRepository;
  }

  /**
   * Execute the use case
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity
   */
  async execute(headers = {}) {
    return await this.authRepository.getSessionToken(headers);
  }
}

module.exports = GetSessionTokenUseCase;


/***/ })

}]);
//# sourceMappingURL=347.js.map