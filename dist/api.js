!function(e,o){"object"==typeof exports&&"object"==typeof module?module.exports=o():"function"==typeof define&&define.amd?define("api",[],o):"object"==typeof exports?exports.api=o():e.api=o()}(this,(()=>{return e={22:e=>{function o(e,o){o.status(200).json({status:"UP"})}e.exports=o,e.exports.default=o},137:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/match-3d-2d-idscan/front`;if(console.log("=== FRONT ID SCAN API HANDLER - REQUEST ANALYSIS ==="),console.log("📤 Target URL:",s),console.log("📋 Request body type:",typeof e.body),console.log("📋 Request body constructor:",e.body?.constructor?.name),console.log("📋 Request body keys:",e.body?Object.keys(e.body):"No body"),!e.body)return console.error("❌ Request body is missing"),o.status(400).json({wasProcessed:!1,error:!0,errorMessage:"Request body is required"});const t={hasIdScan:!!e.body.idScan,hasIdScanFrontImage:!!e.body.idScanFrontImage,hasIdScanBackImage:!!e.body.idScanBackImage,hasEnableConfirmInfo:void 0!==e.body.enableConfirmInfo,enableConfirmInfoValue:e.body.enableConfirmInfo};console.log("📊 Front scan body analysis:",t),e.body.idScan||console.error("❌ Missing required parameter: idScan"),e.body.idScanFrontImage||console.error("❌ Missing required parameter: idScanFrontImage");const n={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":e.headers["x-ekyc-sdk-version"]||"1.0.0",correlationid:e.headers.correlationid};let r;if(Object.keys(n).forEach((e=>{void 0===n[e]&&delete n[e]})),console.log("📋 Request headers:",Object.keys(n)),"string"==typeof e.body?(console.log("⚠️ Request body is already a string - using directly"),r=e.body):(console.log("📦 Request body is object - stringifying"),r=JSON.stringify(e.body)),console.log("📏 Request body size:",r.length,"characters"),console.log("📋 Request body starts with:",r.substring(0,100)+"..."),r.startsWith('"{')||r.startsWith('"{')){console.error("🚨 DETECTED DOUBLE STRINGIFICATION!"),console.error("🚨 Request body appears to be double-stringified JSON"),console.error("🚨 First 200 chars:",r.substring(0,200));try{const e=JSON.parse(r);"object"==typeof e&&(console.log("🔧 Attempting to fix by using parsed object"),r=JSON.stringify(e),console.log("✅ Fixed request body starts with:",r.substring(0,100)+"..."))}catch(e){console.error("❌ Failed to fix double stringification:",e.message)}}if(r.length>1e3){const e=r.substring(0,500)+"...[truncated]..."+r.substring(r.length-500);console.log("📄 Request body (truncated):",e)}else console.log("📄 Request body:",r);console.log("📤 Sending request to backend...");const a=await fetch(s,{method:"POST",headers:n,body:r}),c=await a.json();if(console.log("=== FRONT ID SCAN API HANDLER - RESPONSE ANALYSIS ==="),console.log("📥 Response status:",a.status),console.log("📥 Response ok:",a.ok),console.log("📥 Response code:",c.code),console.log("📥 Response keys:",Object.keys(c)),c.data&&(console.log("📊 Response data keys:",Object.keys(c.data)),console.log("📊 Has scanResultBlob:",!!c.data.scanResultBlob),c.data.scanResultBlob&&console.log("📦 ScanResultBlob length:",c.data.scanResultBlob.length)),a.ok&&"CUS-KYC-1000"===c.code){const e={wasProcessed:!0,error:!1,scanResultBlob:c.data?.scanResultBlob||"",originalResponse:c};return console.log("✅ SUCCESS - Returning front scan success response"),console.log("📦 ScanResultBlob length:",e.scanResultBlob.length),console.log("=== FRONT ID SCAN API HANDLER - SUCCESS ==="),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:c.description||c.message||"Unknown error occurred during front ID scan",originalResponse:c};return console.error("❌ ERROR - Front scan failed"),console.error("❌ Error message:",e.errorMessage),console.error("❌ Full response data:",JSON.stringify(c,null,2)),console.error("=== FRONT ID SCAN API HANDLER - ERROR ==="),o.status(a.status).json(e)}}catch(e){return console.error("=== FRONT ID SCAN API HANDLER - EXCEPTION ==="),console.error("❌ Exception type:",e.constructor.name),console.error("❌ Exception message:",e.message),console.error("❌ Exception stack:",e.stack),e.cause&&console.error("❌ Error cause:",e.cause),console.error("=== END FRONT ID SCAN EXCEPTION ==="),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process front ID scan: "+e.message})}}e.exports=o,e.exports.default=o},154:(e,o,s)=>{const t=s(198),n=s(264),r=s(235),a=s(137),c=s(991),l=s(622),i=s(22);e.exports={sessionTokenHandler:t,facetecSessionTokenHandler:n,idscanOnlyHandler:r,frontIdScanHandler:a,backIdScanHandler:c,livenessCheckHandler:l,healthHandler:i}},198:(e,o,s)=>{const t=s(599);async function n(e,o){if("GET"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=t.getUniqueId();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(s,{method:"GET",headers:n}),a=await r.json();return o.status(r.status).json(a)}catch(e){return console.error("Error getting session token:",e),o.status(500).json({error:"Failed to get session token"})}}e.exports=n,e.exports.default=n},235:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/idscan-only`,t={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],correlationid:e.headers.correlationid};let n;Object.keys(t).forEach((e=>{void 0===t[e]&&delete t[e]})),console.log("=== IDSCAN-ONLY API HANDLER - REQUEST BODY ANALYSIS ==="),console.log("📋 Request body type:",typeof e.body),console.log("📋 Request body constructor:",e.body?.constructor?.name),"string"==typeof e.body?(console.log("⚠️ Request body is already a string - using directly"),n=e.body):(console.log("📦 Request body is object - stringifying"),n=JSON.stringify(e.body)),console.log("📏 Request body size:",n.length,"characters"),console.log("📋 Request body starts with:",n.substring(0,100)+"..."),n.startsWith('"{')||n.startsWith('"{')?(console.error("🚨 IDSCAN-ONLY: DETECTED DOUBLE STRINGIFICATION!"),console.error("🚨 Request body appears to be double-stringified JSON")):console.log("✅ IDSCAN-ONLY: Request body appears to be properly formatted");const r=await fetch(s,{method:"POST",headers:t,body:n}),a=await r.json();if(r.ok&&"CUS-KYC-1000"===a.code){const e={wasProcessed:!0,error:!1,scanResultBlob:a.data?.scanResultBlob||"",originalResponse:a};return console.log("Returning success response with scanResultBlob length:",e.scanResultBlob.length),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:a.description||a.message||"Unknown error",originalResponse:a};return console.log("Returning error response:",e.errorMessage),o.status(r.status).json(e)}}catch(e){return console.error("Error processing ID scan:",e),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process ID scan"})}}e.exports=o,e.exports.default=o},264:(e,o,s)=>{const t=s(599);async function n(e,o){if("GET"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken/facetec`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=t.getUniqueId();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(s,{method:"GET",headers:n}),a=await r.json();return o.status(r.status).json(a)}catch(e){return console.error("Error getting FaceTec session token:",e),o.status(500).json({error:"Failed to get FaceTec session token"})}}e.exports=n,e.exports.default=n},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const o=16*Math.random()|0;return("x"===e?o:3&o|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},622:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=(e,o=20)=>{if(!e||"object"!=typeof e)return e;const t={};for(const[n,r]of Object.entries(e))"string"==typeof r&&r.length>o?t[n]=r.substring(0,o)+"...":t[n]="object"==typeof r&&null!==r?s(r,o):r;return t};console.log("=== ENROLLMENT-3D API HANDLER - INCOMING REQUEST ==="),console.log("Request method:",e.method),console.log("Request headers received (truncated):",JSON.stringify(s(e.headers),null,2)),console.log("Request body received (truncated):",JSON.stringify(s(e.body),null,2)),console.log("Request body keys:",Object.keys(e.body||{}));const t=["function","faceScan","auditTrailImage","lowQualityAuditTrailImage"].filter((o=>!e.body||!e.body[o]));t.length>0?console.log("⚠️  MISSING REQUIRED BODY PARAMETERS:",t):console.log("✅ All required body parameters present");const n=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/enrollment-3d`,r={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":"1.0.0",correlationid:e.headers.correlationid};Object.keys(r).forEach((e=>{void 0===r[e]&&delete r[e]})),console.log("=== ENROLLMENT-3D API HANDLER - OUTGOING REQUEST ==="),console.log("Target URL:",n),console.log("Outgoing headers (truncated):",JSON.stringify(s(r),null,2)),console.log("Outgoing body (truncated):",JSON.stringify(s(e.body),null,2)),console.log("Outgoing body size (bytes):",JSON.stringify(e.body).length);const a=await fetch(n,{method:"POST",headers:r,body:JSON.stringify(e.body)}),c=await a.json();if(console.log("=== ENROLLMENT-3D API HANDLER - BACKEND RESPONSE ==="),console.log("Response status:",a.status),console.log("Response ok:",a.ok),console.log("Response data:",JSON.stringify(c,null,2)),a.ok&&"CUS-KYC-1000"===c.code){const e={wasProcessed:!0,error:!1,scanResultBlob:c.data?.scanResultBlob||"",originalResponse:c};return console.log("✅ SUCCESS - Returning success response with scanResultBlob length:",e.scanResultBlob.length),console.log("=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ==="),console.log("Final response:",JSON.stringify(e,null,2)),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:c.description||c.message||"Unknown error",originalResponse:c};return console.log("❌ ERROR - Returning error response:",e.errorMessage),console.log("=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ==="),console.log("Final response:",JSON.stringify(e,null,2)),o.status(a.status).json(e)}}catch(e){return console.error("Error processing liveness check:",e),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process liveness check"})}}e.exports=o,e.exports.default=o},991:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/match-3d-2d-idscan/back`;if(console.log("=== BACK ID SCAN API HANDLER - REQUEST ANALYSIS ==="),console.log("📤 Target URL:",s),console.log("📋 Request body type:",typeof e.body),console.log("📋 Request body constructor:",e.body?.constructor?.name),console.log("📋 Request body keys:",e.body?Object.keys(e.body):"No body"),!e.body)return console.error("❌ Request body is missing"),o.status(400).json({wasProcessed:!1,error:!0,errorMessage:"Request body is required"});const t={hasIdScan:!!e.body.idScan,hasIdScanFrontImage:!!e.body.idScanFrontImage,hasIdScanBackImage:!!e.body.idScanBackImage,hasEnableConfirmInfo:void 0!==e.body.enableConfirmInfo,enableConfirmInfoValue:e.body.enableConfirmInfo};console.log("📊 Back scan body analysis:",t),e.body.idScan||console.error("❌ Missing required parameter: idScan"),e.body.idScanBackImage||console.error("❌ Missing required parameter: idScanBackImage"),e.body.idScanFrontImage&&console.log("ℹ️ Front image also present in back scan request");const n={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":e.headers["x-ekyc-sdk-version"]||"1.0.0",correlationid:e.headers.correlationid};let r;if(Object.keys(n).forEach((e=>{void 0===n[e]&&delete n[e]})),console.log("📋 Request headers:",Object.keys(n)),"string"==typeof e.body?(console.log("⚠️ Request body is already a string - using directly"),r=e.body):(console.log("📦 Request body is object - stringifying"),r=JSON.stringify(e.body)),console.log("📏 Request body size:",r.length,"characters"),console.log("📋 Request body starts with:",r.substring(0,100)+"..."),r.startsWith('"{')||r.startsWith('"{')){console.error("🚨 DETECTED DOUBLE STRINGIFICATION!"),console.error("🚨 Request body appears to be double-stringified JSON"),console.error("🚨 First 200 chars:",r.substring(0,200));try{const e=JSON.parse(r);"object"==typeof e&&(console.log("🔧 Attempting to fix by using parsed object"),r=JSON.stringify(e),console.log("✅ Fixed request body starts with:",r.substring(0,100)+"..."))}catch(e){console.error("❌ Failed to fix double stringification:",e.message)}}if(r.length>1e3){const e=r.substring(0,500)+"...[truncated]..."+r.substring(r.length-500);console.log("📄 Request body (truncated):",e)}else console.log("📄 Request body:",r);console.log("📤 Sending request to backend...");const a=await fetch(s,{method:"POST",headers:n,body:r}),c=await a.json();if(console.log("=== BACK ID SCAN API HANDLER - RESPONSE ANALYSIS ==="),console.log("📥 Response status:",a.status),console.log("📥 Response ok:",a.ok),console.log("📥 Response code:",c.code),console.log("📥 Response keys:",Object.keys(c)),c.data&&(console.log("📊 Response data keys:",Object.keys(c.data)),console.log("📊 Has scanResultBlob:",!!c.data.scanResultBlob),console.log("📊 Has documentData:",!!c.data.documentData),c.data.scanResultBlob&&console.log("📦 ScanResultBlob length:",c.data.scanResultBlob.length),c.data.documentData)){console.log("📄 DocumentData length:",c.data.documentData.length);try{const e=JSON.parse(c.data.documentData);console.log("📊 DocumentData structure:",{hasScannedValues:!!e.scannedValues,groupCount:e.scannedValues?.groups?.length||0})}catch(e){console.error("❌ Failed to parse documentData:",e.message)}}if(a.ok&&"CUS-KYC-1000"===c.code){const e={wasProcessed:!0,error:!1,scanResultBlob:c.data?.scanResultBlob||"",originalResponse:c};return console.log("✅ SUCCESS - Returning back scan success response"),console.log("📦 ScanResultBlob length:",e.scanResultBlob.length),console.log("📄 Has OCR data:",!!c.data?.documentData),console.log("=== BACK ID SCAN API HANDLER - SUCCESS ==="),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:c.description||c.message||"Unknown error occurred during back ID scan",originalResponse:c};return console.error("❌ ERROR - Back scan failed"),console.error("❌ Error message:",e.errorMessage),console.error("❌ Full response data:",JSON.stringify(c,null,2)),console.error("=== BACK ID SCAN API HANDLER - ERROR ==="),o.status(a.status).json(e)}}catch(e){return console.error("=== BACK ID SCAN API HANDLER - EXCEPTION ==="),console.error("❌ Exception type:",e.constructor.name),console.error("❌ Exception message:",e.message),console.error("❌ Exception stack:",e.stack),e.cause&&console.error("❌ Error cause:",e.cause),console.error("=== END BACK ID SCAN EXCEPTION ==="),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process back ID scan: "+e.message})}}e.exports=o,e.exports.default=o}},o={},function s(t){var n=o[t];if(void 0!==n)return n.exports;var r=o[t]={exports:{}};return e[t](r,r.exports,s),r.exports}(154);var e,o}));