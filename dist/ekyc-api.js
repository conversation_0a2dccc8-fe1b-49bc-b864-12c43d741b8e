(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define("ScbTechXEkycSDK", [], factory);
	else if(typeof exports === 'object')
		exports["ScbTechXEkycSDK"] = factory();
	else
		root["ScbTechXEkycSDK"] = root["ScbTechXEkycSDK"] || {}, root["ScbTechXEkycSDK"]["ekyc-api"] = factory();
})(this, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ 22:
/***/ ((module) => {

/**
 * Health check API endpoint
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
function handler(req, res) {
  // Return a simple health status
  res.status(200).json({ status: 'UP' });
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports["default"] = handler;


/***/ }),

/***/ 154:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

/**
 * API handlers for the eKYC SDK
 * These handlers provide proxy functionality for eKYC API endpoints
 * and can be used both in Next.js applications and standalone SDK implementations
 */

const sessionTokenHandler = __webpack_require__(198);
const facetecSessionTokenHandler = __webpack_require__(264);
const idscanOnlyHandler = __webpack_require__(235);
const livenessCheckHandler = __webpack_require__(622);
const healthHandler = __webpack_require__(22);

module.exports = {
  sessionTokenHandler,
  facetecSessionTokenHandler,
  idscanOnlyHandler,
  livenessCheckHandler,
  healthHandler
};


/***/ }),

/***/ 198:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const UuidGenerator = __webpack_require__(599);

/**
 * API route to get a session token from the eKYC authentication API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/authen/sessiontoken`;

    // Forward any headers from the client request, except host-specific ones
    const headers = { ...req.headers };
    delete headers.host;
    delete headers.connection;

    // Add or override specific headers
    headers['Content-Type'] = 'application/json';
    headers['Accept'] = 'application/json';
    
    // Add default JWT token if not provided in request
    if (!headers['Authorization'] && process.env.JWT_TOKEN) {
      headers['Authorization'] = `Bearer ${process.env.JWT_TOKEN}`;
    }

    // Ensure X-Ekyc-Device-Info is preserved if it exists
    // This is important because it contains the device ID generated on the client side
    if (!headers['X-Ekyc-Device-Info']) {
      // If not provided, generate a fallback UUID for server-side requests
      const uuid = UuidGenerator.getUniqueId();
      headers['X-Ekyc-Device-Info'] = `browser|${uuid}`;
    }

    // Make the GET request with headers only (no body)
    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
    });

    // Get the response data
    const data = await response.json();

    // Return the response with the same status code
    return res.status(response.status).json(data);
  } catch (error) {
    console.error('Error getting session token:', error);
    return res.status(500).json({ error: 'Failed to get session token' });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports["default"] = handler;


/***/ }),

/***/ 235:
/***/ ((module) => {

/**
 * API route to process ID scan data to the eKYC backend API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/idscan-only`;

    // ✅ Forward headers follow spec
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': req.headers['authorization'] || (process.env.JWT_TOKEN ? `Bearer ${process.env.JWT_TOKEN}` : undefined),
      'X-Device-Key': req.headers['x-device-key'],
      'X-User-Agent': req.headers['x-user-agent'],
      'X-Session-Id': req.headers['x-session-id'],
      'X-Tid': req.headers['x-tid'],
      'X-Ekyc-Token': req.headers['x-ekyc-token'],
      'correlationid': req.headers.correlationid
    };

    // Remove undefined headers
    Object.keys(headers).forEach(key => {
      if (headers[key] === undefined) {
        delete headers[key];
      }
    });

    // Make the POST request with the body from the client
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(req.body),
    });

    // Get the response data
    const data = await response.json();

    // Transform the response to match PhotoIDScanProcessor expectations
    if (response.ok && data.code === 'CUS-KYC-1000') {
      // Success case - return format expected by PhotoIDScanProcessor
      const successResponse = {
        wasProcessed: true,
        error: false,
        scanResultBlob: data.data?.scanResultBlob || "", // Use scanResultBlob from backend if available
        originalResponse: data // Keep original response for debugging
      };
      
      console.log('Returning success response with scanResultBlob length:', successResponse.scanResultBlob.length);
      return res.status(200).json(successResponse);
    } else {
      // Error case - return format expected by PhotoIDScanProcessor
      const errorResponse = {
        wasProcessed: false,
        error: true,
        errorMessage: data.description || data.message || 'Unknown error',
        originalResponse: data // Keep original response for debugging
      };
      
      console.log('Returning error response:', errorResponse.errorMessage);
      return res.status(response.status).json(errorResponse);
    }
  } catch (error) {
    console.error('Error processing ID scan:', error);
    return res.status(500).json({
      wasProcessed: false,
      error: true,
      errorMessage: 'Failed to process ID scan'
    });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports["default"] = handler;

/***/ }),

/***/ 264:
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

const UuidGenerator = __webpack_require__(599);

/**
 * API route to get a FaceTec session token from the eKYC authentication API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/authen/sessiontoken/facetec`;

    // Forward any headers from the client request, except host-specific ones
    const headers = { ...req.headers };
    delete headers.host;
    delete headers.connection;

    // Add or override specific headers
    headers['Content-Type'] = 'application/json';
    headers['Accept'] = 'application/json';
    
    // Add default JWT token if not provided in request
    if (!headers['Authorization'] && process.env.JWT_TOKEN) {
      headers['Authorization'] = `Bearer ${process.env.JWT_TOKEN}`;
    }

    // Ensure X-Ekyc-Device-Info is preserved if it exists
    // This is important because it contains the device ID generated on the client side
    if (!headers['X-Ekyc-Device-Info']) {
      // If not provided, generate a fallback UUID for server-side requests
      const uuid = UuidGenerator.getUniqueId();
      headers['X-Ekyc-Device-Info'] = `browser|${uuid}`;
    }

    // Make the GET request with headers only (no body)
    const response = await fetch(url, {
      method: 'GET',
      headers: headers,
    });

    // Get the response data
    const data = await response.json();

    // Return the response with the same status code
    return res.status(response.status).json(data);
  } catch (error) {
    console.error('Error getting FaceTec session token:', error);
    return res.status(500).json({ error: 'Failed to get FaceTec session token' });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports["default"] = handler;


/***/ }),

/***/ 599:
/***/ ((module) => {

/**
 * Utility class for generating UUIDs
 */
class UuidGenerator {
  /**
   * Generate a UUID v4
   * @returns {string} - A UUID v4 string
   */
  static generateUuid() {
    // Implementation of UUID v4 generation
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Get a device identifier
   * This will generate a UUID and store it in localStorage if running in a browser
   * If the UUID already exists in localStorage, it will be reused
   * @returns {string} - A device identifier
   */
  static getDeviceId() {
    if (typeof window !== 'undefined' && window.localStorage) {
      // Running in browser, check localStorage
      let deviceId = localStorage.getItem('ekyc_device_id');
      if (!deviceId) {
        // Generate a new UUID if not found in localStorage
        deviceId = this.generateUuid();
        localStorage.setItem('ekyc_device_id', deviceId);
      }
      return deviceId;
    } else {
      // Not running in browser, generate a new UUID
      return this.generateUuid();
    }
  }

  /**
   * Generate a new UUID every time
   * Unlike getDeviceId, this does not store or reuse UUIDs
   * @returns {string} - A new UUID v4 string
   */
  static getUniqueId() {
    return this.generateUuid();
  }
}

module.exports = UuidGenerator;


/***/ }),

/***/ 622:
/***/ ((module) => {

/**
 * API route to process liveness check data to the eKYC backend API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/enrollment-3d`;

    // Forward headers follow spec
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': req.headers['authorization'] || (process.env.JWT_TOKEN ? `Bearer ${process.env.JWT_TOKEN}` : undefined),
      'X-Device-Key': req.headers['x-device-key'],
      'X-User-Agent': req.headers['x-user-agent'],
      'X-Session-Id': req.headers['x-session-id'],
      'X-Tid': req.headers['x-tid'],
      'X-Ekyc-Token': req.headers['x-ekyc-token'],
      'correlationid': req.headers.correlationid
    };

    // Remove undefined headers
    Object.keys(headers).forEach(key => {
      if (headers[key] === undefined) {
        delete headers[key];
      }
    });

    // Make the POST request with the body from the client
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(req.body),
    });

    // Get the response data
    const data = await response.json();

    // Transform the response to match LivenessCheckProcessor expectations
    if (response.ok && data.code === 'CUS-KYC-1000') {
      // Success case - return format expected by LivenessCheckProcessor
      const successResponse = {
        wasProcessed: true,
        error: false,
        scanResultBlob: data.data?.scanResultBlob || "", // Use scanResultBlob from backend if available
        originalResponse: data // Keep original response for debugging
      };
      
      console.log('Returning success response with scanResultBlob length:', successResponse.scanResultBlob.length);
      return res.status(200).json(successResponse);
    } else {
      // Error case - return format expected by LivenessCheckProcessor
      const errorResponse = {
        wasProcessed: false,
        error: true,
        errorMessage: data.description || data.message || 'Unknown error',
        originalResponse: data // Keep original response for debugging
      };
      
      console.log('Returning error response:', errorResponse.errorMessage);
      return res.status(response.status).json(errorResponse);
    }
  } catch (error) {
    console.error('Error processing liveness check:', error);
    return res.status(500).json({
      wasProcessed: false,
      error: true,
      errorMessage: 'Failed to process liveness check'
    });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports["default"] = handler;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module is referenced by other modules so it can't be inlined
/******/ 	var __webpack_exports__ = __webpack_require__(154);
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=ekyc-api.js.map