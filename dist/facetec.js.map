{"version": 3, "file": "facetec.js", "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,yDAAyD;AACzD,CAAC;AACD,O;;;;;;ACVA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,uBAAuB,mBAAO,CAAC,GAAW;AAC1C,QAAQ,qBAAqB,EAAE,mBAAO,CAAC,GAAU;AACjD,QAAQ,gBAAgB,EAAE,mBAAO,CAAC,GAAwB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,kBAAkB;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,wDAAwD,cAAc;AACtE;AACA,GAAG;;AAEH;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,qDAAqD,cAAc;AACnE;AACA,GAAG;;AAEH;AACA;AACA,eAAe,iBAAiB;AAChC;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,wDAAwD,cAAc;AACtE;AACA,GAAG;;AAEH;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,iBAAiB;AAChC;AACA;AACA,YAAY,gDAAgD;;AAE5D;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,MAAM;AACN;AACA,+CAA+C,cAAc;AAC7D;AACA,GAAG;;AAEH;AACA;AACA,eAAe,SAAS;AACxB;AACA;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA,qEAAqE,cAAc;AACnF;AACA,GAAG;;AAEH;AACA;AACA,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,aAAa,QAAQ;AACrB,eAAe,iBAAiB;AAChC;AACA;AACA,YAAY,mEAAmE;;AAE/E;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,yBAAsB;;;;;;;UCxLtB;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;UAEA;UACA;;;;;WCzBA;WACA;WACA;WACA;WACA,+BAA+B,wCAAwC;WACvE;WACA;WACA;WACA;WACA,iBAAiB,qBAAqB;WACtC;WACA;WACA,kBAAkB,qBAAqB;WACvC;WACA;WACA,KAAK;WACL;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;;;;WC3BA;;;;;WCAA;;WAEA;WACA;WACA;WACA;WACA;WACA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;;WAEA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA;WACA,MAAM,qBAAqB;WAC3B;WACA;WACA;WACA;WACA;WACA;WACA;WACA;;WAEA;WACA;WACA;;;;;UEhDA;UACA;UACA;UACA;UACA", "sources": ["webpack://ScbTechXEkycSDK/webpack/universalModuleDefinition", "webpack://ScbTechXEkycSDK/./lib/facetec-entry.js", "webpack://ScbTechXEkycSDK/webpack/bootstrap", "webpack://ScbTechXEkycSDK/webpack/runtime/chunk loaded", "webpack://ScbTechXEkycSDK/webpack/runtime/hasOwnProperty shorthand", "webpack://ScbTechXEkycSDK/webpack/runtime/jsonp chunk loading", "webpack://ScbTechXEkycSDK/webpack/before-startup", "webpack://ScbTechXEkycSDK/webpack/startup", "webpack://ScbTechXEkycSDK/webpack/after-startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"ScbTechXEkycSDK\", [], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ScbTechXEkycSDK\"] = factory();\n\telse\n\t\troot[\"ScbTechXEkycSDK\"] = root[\"ScbTechXEkycSDK\"] || {}, root[\"ScbTechXEkycSDK\"][\"facetec\"] = factory();\n})(this, () => {\nreturn ", "/**\n * eKYC SDK - FaceTec Module Entry Point\n * \n * This module provides FaceTec-specific functionality that can be imported separately\n * for applications that only need FaceTec integration without the full SDK.\n * \n * @version 1.0.0\n */\n\nconst facetecService = require('./facetec');\nconst { performPhotoIDScan } = require('./simple');\nconst { UuidGenerator } = require('./infrastructure/utils');\n\n/**\n * FaceTec SDK Integration Module\n */\nconst FaceTecModule = {\n  /**\n   * Initialize FaceTec SDK with device and encryption keys\n   * @param {string} deviceKey - Device key from session token response\n   * @param {string} encryptionKey - Encryption key from session token response\n   * @returns {Promise<boolean>} True if initialization successful\n   */\n  async initialize(deviceKey, encryptionKey) {\n    if (!deviceKey || !encryptionKey) {\n      throw new Error('Both deviceKey and encryptionKey are required for FaceTec initialization');\n    }\n    \n    try {\n      return await facetecService.initializeFaceTec(deviceKey, encryptionKey);\n    } catch (error) {\n      console.error('FaceTec initialization failed:', error);\n      throw new Error(`FaceTec initialization failed: ${error.message}`);\n    }\n  },\n\n  /**\n   * Load the FaceTec SDK\n   * @returns {Promise<Object>} FaceTec SDK instance\n   */\n  async loadSDK() {\n    try {\n      return await facetecService.loadFaceTecSDK();\n    } catch (error) {\n      console.error('Failed to load FaceTec SDK:', error);\n      throw new Error(`Failed to load FaceTec SDK: ${error.message}`);\n    }\n  },\n\n  /**\n   * Get FaceTec SDK version\n   * @returns {Promise<string>} SDK version string\n   */\n  async getVersion() {\n    try {\n      return await facetecService.getFaceTecVersion();\n    } catch (error) {\n      console.error('Failed to get FaceTec version:', error);\n      throw new Error(`Failed to get FaceTec version: ${error.message}`);\n    }\n  },\n\n  /**\n   * Perform Photo ID Scan using FaceTec\n   * @param {Object} config - Scan configuration\n   * @param {string} config.deviceKey - Device key for scanning\n   * @param {Object} config.sessionTokenResponse - Session token response containing FaceTec session data\n   * @param {Object} config.headers - Additional headers for the scan request\n   * @param {string} config.headers['X-Session-Id'] - Session ID\n   * @param {string} config.headers['X-Ekyc-Token'] - eKYC token\n   * @param {string} config.headers.correlationid - Correlation ID\n   * @returns {Promise<Object>} Scan result with sessionResult, idScanResult, and networkResponseStatus\n   */\n  async performIDScan(config) {\n    const { deviceKey, sessionTokenResponse, headers = {} } = config;\n\n    // Validation\n    if (!deviceKey) {\n      throw new Error('deviceKey is required for Photo ID Scan');\n    }\n\n    if (!sessionTokenResponse) {\n      throw new Error('sessionTokenResponse is required for Photo ID Scan');\n    }\n\n    if (!sessionTokenResponse.faceTecInitialized) {\n      throw new Error('FaceTec SDK must be initialized before performing ID scan');\n    }\n\n    // Ensure required headers are present\n    const scanHeaders = {\n      'X-Session-Id': headers['X-Session-Id'] || sessionTokenResponse.data?.sessionId,\n      'X-Ekyc-Token': headers['X-Ekyc-Token'] || sessionTokenResponse.data?.ekycToken,\n      'correlationid': headers.correlationid || UuidGenerator.getUniqueId(),\n      ...headers\n    };\n\n    try {\n      return await performPhotoIDScan(scanHeaders, deviceKey, sessionTokenResponse);\n    } catch (error) {\n      console.error('Photo ID Scan failed:', error);\n      throw new Error(`Photo ID Scan failed: ${error.message}`);\n    }\n  },\n\n  /**\n   * Check if FaceTec SDK is available in the current environment\n   * @returns {boolean} True if FaceTec SDK is available\n   */\n  isAvailable() {\n    return typeof window !== 'undefined' && \n           (typeof window.FaceTecSDK !== 'undefined' || \n            typeof document !== 'undefined');\n  },\n\n  /**\n   * Set FaceTec resource directories\n   * @param {string} resourceDir - Path to FaceTec resources directory\n   * @param {string} imagesDir - Path to FaceTec images directory\n   * @returns {Promise<void>}\n   */\n  async setResourceDirectories(resourceDir = '/core-sdk/FaceTecSDK.js/resources', imagesDir = '/core-sdk/FaceTec_images') {\n    try {\n      const FaceTecSDK = await this.loadSDK();\n      FaceTecSDK.setResourceDirectory(resourceDir);\n      FaceTecSDK.setImagesDirectory(imagesDir);\n    } catch (error) {\n      console.error('Failed to set FaceTec resource directories:', error);\n      throw new Error(`Failed to set FaceTec resource directories: ${error.message}`);\n    }\n  },\n\n  /**\n   * Create a complete FaceTec workflow\n   * @param {Object} config - Workflow configuration\n   * @param {string} config.deviceKey - Device key\n   * @param {string} config.encryptionKey - Encryption key\n   * @param {Object} config.sessionTokenResponse - Session token response\n   * @param {Object} config.scanHeaders - Headers for the scan request\n   * @returns {Promise<Object>} Complete workflow result\n   */\n  async performCompleteWorkflow(config) {\n    const { deviceKey, encryptionKey, sessionTokenResponse, scanHeaders = {} } = config;\n\n    try {\n      // Step 1: Initialize FaceTec\n      console.log('Initializing FaceTec SDK...');\n      await this.initialize(deviceKey, encryptionKey);\n\n      // Step 2: Set resource directories\n      console.log('Setting FaceTec resource directories...');\n      await this.setResourceDirectories();\n\n      // Step 3: Perform ID scan\n      console.log('Starting Photo ID Scan...');\n      const scanResult = await this.performIDScan({\n        deviceKey,\n        sessionTokenResponse: {\n          ...sessionTokenResponse,\n          faceTecInitialized: true // Mark as initialized\n        },\n        headers: scanHeaders\n      });\n\n      console.log('FaceTec workflow completed successfully');\n      return {\n        success: true,\n        scanResult,\n        workflow: 'complete'\n      };\n\n    } catch (error) {\n      console.error('FaceTec workflow failed:', error);\n      return {\n        success: false,\n        error: error.message,\n        workflow: 'failed'\n      };\n    }\n  }\n};\n\n// Export for different module systems\nmodule.exports = FaceTecModule;\nmodule.exports.default = FaceTecModule;\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = (result, chunkIds, fn, priority) => {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar [chunkIds, fn, priority] = deferred[i];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t956: 0\n};\n\n// no chunk on demand loading\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = this[\"webpackChunkScbTechXEkycSDK\"] = this[\"webpackChunkScbTechXEkycSDK\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [347], () => (__webpack_require__(107)))\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n", ""], "names": [], "sourceRoot": ""}