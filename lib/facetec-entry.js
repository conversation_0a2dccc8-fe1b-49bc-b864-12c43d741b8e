/**
 * eKYC SDK - FaceTec Module Entry Point
 * 
 * This module provides FaceTec-specific functionality that can be imported separately
 * for applications that only need FaceTec integration without the full SDK.
 * 
 * @version 1.0.0
 */

const facetecService = require('./facetec');
const { performPhotoIDScan } = require('./simple');
const { UuidGenerator } = require('./infrastructure/utils');

/**
 * FaceTec SDK Integration Module
 */
const FaceTecModule = {
  /**
   * Initialize FaceTec SDK with device and encryption keys
   * @param {string} deviceKey - Device key from session token response
   * @param {string} encryptionKey - Encryption key from session token response
   * @returns {Promise<boolean>} True if initialization successful
   */
  async initialize(deviceKey, encryptionKey) {
    if (!deviceKey || !encryptionKey) {
      throw new Error('Both deviceKey and encryptionKey are required for FaceTec initialization');
    }
    
    try {
      return await facetecService.initializeFaceTec(deviceKey, encryptionKey);
    } catch (error) {
      console.error('FaceTec initialization failed:', error);
      throw new Error(`FaceTec initialization failed: ${error.message}`);
    }
  },

  /**
   * Load the FaceTec SDK
   * @returns {Promise<Object>} FaceTec SDK instance
   */
  async loadSDK() {
    try {
      return await facetecService.loadFaceTecSDK();
    } catch (error) {
      console.error('Failed to load FaceTec SDK:', error);
      throw new Error(`Failed to load FaceTec SDK: ${error.message}`);
    }
  },

  /**
   * Get FaceTec SDK version
   * @returns {Promise<string>} SDK version string
   */
  async getVersion() {
    try {
      return await facetecService.getFaceTecVersion();
    } catch (error) {
      console.error('Failed to get FaceTec version:', error);
      throw new Error(`Failed to get FaceTec version: ${error.message}`);
    }
  },

  /**
   * Perform Photo ID Scan using FaceTec
   * @param {Object} config - Scan configuration
   * @param {string} config.deviceKey - Device key for scanning
   * @param {Object} config.sessionTokenResponse - Session token response containing FaceTec session data
   * @param {Object} config.headers - Additional headers for the scan request
   * @param {string} config.headers['X-Session-Id'] - Session ID
   * @param {string} config.headers['X-Ekyc-Token'] - eKYC token
   * @param {string} config.headers.correlationid - Correlation ID
   * @returns {Promise<Object>} Scan result with sessionResult, idScanResult, and networkResponseStatus
   */
  async performIDScan(config) {
    const { deviceKey, sessionTokenResponse, headers = {} } = config;

    // Validation
    if (!deviceKey) {
      throw new Error('deviceKey is required for Photo ID Scan');
    }

    if (!sessionTokenResponse) {
      throw new Error('sessionTokenResponse is required for Photo ID Scan');
    }

    if (!sessionTokenResponse.faceTecInitialized) {
      throw new Error('FaceTec SDK must be initialized before performing ID scan');
    }

    // Ensure required headers are present
    const scanHeaders = {
      'X-Session-Id': headers['X-Session-Id'] || sessionTokenResponse.data?.sessionId,
      'X-Ekyc-Token': headers['X-Ekyc-Token'] || sessionTokenResponse.data?.ekycToken,
      'correlationid': headers.correlationid || UuidGenerator.getUniqueId(),
      ...headers
    };

    try {
      return await performPhotoIDScan(scanHeaders, deviceKey, sessionTokenResponse);
    } catch (error) {
      console.error('Photo ID Scan failed:', error);
      throw new Error(`Photo ID Scan failed: ${error.message}`);
    }
  },

  /**
   * Check if FaceTec SDK is available in the current environment
   * @returns {boolean} True if FaceTec SDK is available
   */
  isAvailable() {
    return typeof window !== 'undefined' && 
           (typeof window.FaceTecSDK !== 'undefined' || 
            typeof document !== 'undefined');
  },

  /**
   * Set FaceTec resource directories
   * @param {string} resourceDir - Path to FaceTec resources directory
   * @param {string} imagesDir - Path to FaceTec images directory
   * @returns {Promise<void>}
   */
  async setResourceDirectories(resourceDir = '/core-sdk/FaceTecSDK.js/resources', imagesDir = '/core-sdk/FaceTec_images') {
    try {
      const FaceTecSDK = await this.loadSDK();
      FaceTecSDK.setResourceDirectory(resourceDir);
      FaceTecSDK.setImagesDirectory(imagesDir);
    } catch (error) {
      console.error('Failed to set FaceTec resource directories:', error);
      throw new Error(`Failed to set FaceTec resource directories: ${error.message}`);
    }
  },

  /**
   * Create a complete FaceTec workflow
   * @param {Object} config - Workflow configuration
   * @param {string} config.deviceKey - Device key
   * @param {string} config.encryptionKey - Encryption key
   * @param {Object} config.sessionTokenResponse - Session token response
   * @param {Object} config.scanHeaders - Headers for the scan request
   * @returns {Promise<Object>} Complete workflow result
   */
  async performCompleteWorkflow(config) {
    const { deviceKey, encryptionKey, sessionTokenResponse, scanHeaders = {} } = config;

    try {
      // Step 1: Initialize FaceTec
      console.log('Initializing FaceTec SDK...');
      await this.initialize(deviceKey, encryptionKey);

      // Step 2: Set resource directories
      console.log('Setting FaceTec resource directories...');
      await this.setResourceDirectories();

      // Step 3: Perform ID scan
      console.log('Starting Photo ID Scan...');
      const scanResult = await this.performIDScan({
        deviceKey,
        sessionTokenResponse: {
          ...sessionTokenResponse,
          faceTecInitialized: true // Mark as initialized
        },
        headers: scanHeaders
      });

      console.log('FaceTec workflow completed successfully');
      return {
        success: true,
        scanResult,
        workflow: 'complete'
      };

    } catch (error) {
      console.error('FaceTec workflow failed:', error);
      return {
        success: false,
        error: error.message,
        workflow: 'failed'
      };
    }
  }
};

// Export for different module systems
module.exports = FaceTecModule;
module.exports.default = FaceTecModule;
