/**
 * eKYC SDK - Main Entry Point
 *
 * This is the primary entry point for external developers using the eKYC SDK by SCB TechX.
 * It provides a clean, framework-agnostic API for authentication, FaceTec integration,
 * and ID scanning functionality.
 *
 * Usage: import EkycSDK from './ekyc-sdk/ekyc-sdk.js';
 *
 * @version 1.0.0
 * <AUTHOR> TechX
 */

// Import core functionality
const {
  getSessionToken: getSessionTokenCore,
  getFaceTecSessionTokenWithEkycToken: getFaceTecSessionTokenCore,
  performPhotoIDScan: performPhotoIDScanCore,
  getStoredEkycToken,
  clearEkycToken,
  // Import the 5 main SDK functions
  initEkyc: initEkycCore,
  ocrIdCard: ocrIdCardCore,
  ocrIdCardVerifyByFace: ocrIdCardVerifyByFaceCore,
  ndidVerification: ndidVerificationCore,
  livenessCheck: livenessCheckCore
} = require('./simple');

const { TokenStorage, UuidGenerator } = require('./infrastructure/utils');
const facetecService = require('./facetec');

/**
 * Authentication namespace
 * Handles session tokens and authentication flow
 */
const Auth = {
  /**
   * Get a session token from the eKYC API
   * @param {Object} options - Configuration options
   * @param {Object} options.headers - Additional headers to include
   * @param {string} options.apiKey - API key for authentication
   * @param {boolean} options.storeToken - Whether to store the token (default: true)
   * @returns {Promise<Object>} Session token response
   */
  async getSessionToken(options = {}) {
    const { headers = {}, apiKey, storeToken = true } = options;
    
    if (apiKey) {
      headers['Authorization'] = `Bearer ${apiKey}`;
    }
    
    return await getSessionTokenCore(headers, storeToken);
  },

  /**
   * Get a FaceTec session token using stored eKYC token
   * @param {Object} options - Configuration options
   * @param {Object} options.headers - Additional headers to include
   * @param {boolean} options.initializeFaceTec - Whether to initialize FaceTec SDK (default: true)
   * @returns {Promise<Object>} FaceTec session token response
   */
  async getFaceTecSessionToken(options = {}) {
    const { headers = {}, initializeFaceTec = true } = options;
    return await getFaceTecSessionTokenCore(headers, initializeFaceTec);
  },

  /**
   * Get the stored eKYC token
   * @returns {string|null} The stored token or null
   */
  getStoredToken() {
    return getStoredEkycToken();
  },

  /**
   * Clear the stored eKYC token
   * @returns {boolean} True if cleared successfully
   */
  clearToken() {
    return clearEkycToken();
  }
};

/**
 * FaceTec namespace
 * Handles FaceTec SDK integration and ID scanning
 */
const FaceTec = {
  /**
   * Initialize FaceTec SDK
   * @param {string} deviceKey - Device key from session token response
   * @param {string} encryptionKey - Encryption key from session token response
   * @returns {Promise<boolean>} True if initialized successfully
   */
  async initialize(deviceKey, encryptionKey) {
    return await facetecService.initializeFaceTec(deviceKey, encryptionKey);
  },

  /**
   * Load FaceTec SDK
   * @returns {Promise<Object>} FaceTec SDK instance
   */
  async loadSDK() {
    return await facetecService.loadFaceTecSDK();
  },

  /**
   * Get FaceTec SDK version
   * @returns {Promise<string>} SDK version
   */
  async getVersion() {
    return await facetecService.getFaceTecVersion();
  },

  /**
   * Perform Photo ID Scan
   * @param {Object} options - Scan configuration
   * @param {string} options.deviceKey - Device key for scanning
   * @param {Object} options.sessionTokenResponse - Session token response from Auth.getFaceTecSessionToken()
   * @param {Object} options.headers - Additional headers
   * @returns {Promise<Object>} Scan result
   */
  async performIDScan(options = {}) {
    const { deviceKey, sessionTokenResponse, headers = {} } = options;
    
    if (!deviceKey) {
      throw new Error('deviceKey is required for ID scanning');
    }
    
    if (!sessionTokenResponse) {
      throw new Error('sessionTokenResponse is required for ID scanning');
    }
    
    return await performPhotoIDScanCore(headers, deviceKey, sessionTokenResponse);
  }
};

/**
 * Utilities namespace
 * Provides utility functions for UUID generation and token storage
 */
const Utils = {
  /**
   * Generate a new UUID
   * @returns {string} UUID v4 string
   */
  generateUUID() {
    return UuidGenerator.getUniqueId();
  },

  /**
   * Get or generate a device ID (persisted in localStorage if available)
   * @returns {string} Device ID
   */
  getDeviceId() {
    return UuidGenerator.getDeviceId();
  },

  /**
   * Token storage utilities
   */
  TokenStorage: {
    /**
     * Store a token
     * @param {string} key - Storage key
     * @param {string} value - Token value
     * @returns {boolean} True if stored successfully
     */
    store(key, value) {
      return TokenStorage.storeToken(key, value);
    },

    /**
     * Get a stored token
     * @param {string} key - Storage key
     * @returns {string|null} Token value or null
     */
    get(key) {
      return TokenStorage.getToken(key);
    },

    /**
     * Remove a stored token
     * @param {string} key - Storage key
     * @returns {boolean} True if removed successfully
     */
    remove(key) {
      return TokenStorage.removeToken(key);
    }
  }
};

/**
 * Main SDK object with clean public API
 */
const EkycSDK = {
  // Namespaced APIs
  Auth,
  FaceTec,
  Utils,

  // Quick access methods for common operations
  async getSessionToken(apiKey, options = {}) {
    return Auth.getSessionToken({ apiKey, ...options });
  },

  async initializeFaceTec(sessionTokenResponse) {
    if (!sessionTokenResponse?.data?.deviceKey || !sessionTokenResponse?.data?.encryptionKey) {
      throw new Error('Invalid session token response: missing deviceKey or encryptionKey');
    }

    return FaceTec.initialize(
      sessionTokenResponse.data.deviceKey,
      sessionTokenResponse.data.encryptionKey
    );
  },

  async performIDScan(deviceKey, sessionTokenResponse, options = {}) {
    return FaceTec.performIDScan({
      deviceKey,
      sessionTokenResponse,
      ...options
    });
  },

  // ========================================
  // 5 MAIN SDK ENTRY POINT FUNCTIONS
  // ========================================

  /**
   * 1. Initialize the eKYC SDK
   */
  async initEkyc(options = {}) {
    return await initEkycCore(options);
  },

  /**
   * 2. Perform OCR ID Card scanning
   */
  async ocrIdCard(options = {}) {
    return await ocrIdCardCore(options);
  },

  /**
   * 3. OCR ID Card with facial verification
   */
  async ocrIdCardVerifyByFace(options = {}) {
    return await ocrIdCardVerifyByFaceCore(options);
  },

  /**
   * 4. NDID digital identity verification
   */
  async ndidVerification(options = {}) {
    return await ndidVerificationCore(options);
  },

  /**
   * 5. Facial liveness detection
   */
  async livenessCheck(options = {}) {
    return await livenessCheckCore(options);
  },



  // SDK metadata
  version: '1.0.0',
  name: 'SCB TechX eKYC SDK'
};

// Export for different module systems
module.exports = EkycSDK;
module.exports.default = EkycSDK;

// For ES6 modules
if (typeof exports !== 'undefined') {
  exports.EkycSDK = EkycSDK;
  exports.Auth = Auth;
  exports.FaceTec = FaceTec;
  exports.Utils = Utils;
}
