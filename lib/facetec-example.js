// Example file showing how to import from the core-sdk directory
// Using CommonJS style require for consistency
const FaceTecSDK = require('./core-sdk/FaceTecSDK.js/FaceTecSDK');

// Function to initialize FaceTecSDK
const initializeFaceTec = (deviceKeyIdentifier, publicEncryptionKey) => {
  return new Promise((resolve, reject) => {
    FaceTecSDK.initializeInDevelopmentMode(
      deviceKeyIdentifier,
      publicEncryptionKey,
      (initializedSuccessfully) => {
        if (initializedSuccessfully) {
          console.log('FaceTecSDK initialized successfully');
          resolve(true);
        } else {
          console.error('FaceTecSDK failed to initialize');
          reject(new Error('FaceTecSDK failed to initialize'));
        }
      }
    );
  });
};

// Function to get FaceTecSDK version
const getFaceTecVersion = () => {
  return FaceTecSDK.version();
};

// Export the functions
module.exports = {
  initializeFaceTec,
  getFaceTecVersion
};
