import React from 'react';

/**
 * Button component
 * @param {Object} props - The component props
 * @param {string} props.text - The button text
 * @param {Function} props.onClick - The click handler
 * @param {Object} props.style - Additional styles to apply
 * @returns {JSX.Element} - The button component
 */
const Button = ({ text, onClick, style = {} }) => {
  return (
    <button 
      onClick={onClick}
      style={{
        padding: '10px 20px',
        backgroundColor: '#0070f3',
        color: 'white',
        border: 'none',
        borderRadius: '5px',
        cursor: 'pointer',
        fontSize: '16px',
        ...style
      }}
    >
      {text}
    </button>
  );
};

export default Button;
