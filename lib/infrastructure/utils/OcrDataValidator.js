/**
 * OCR Data Validation Utility
 * Validates OCR fields extracted from ID card scanning
 */
class OcrDataValidator {
    
    /**
     * Validate all OCR fields
     * @param {Object} ocrData - OCR data object
     * @returns {Object} - Validation results for each field
     */
    static validateAll(ocrData) {
        if (!ocrData) {
            return {
                isValid: false,
                errors: ['No OCR data provided'],
                fieldValidations: {}
            };
        }

        const fieldValidations = {};
        const errors = [];

        // Validate National ID
        const nationalIdValidation = this.validateNationalId(ocrData.nationalId);
        fieldValidations.nationalId = nationalIdValidation;
        if (!nationalIdValidation.isValid) {
            errors.push(`National ID: ${nationalIdValidation.error}`);
        }

        // Validate Thai name fields
        fieldValidations.titleTh = this.validateRequiredField(ocrData.titleTh, 'Title (TH)');
        fieldValidations.firstNameTh = this.validateRequiredField(ocrData.firstNameTh, 'First Name (TH)');
        fieldValidations.middleNameTh = this.validateOptionalField(ocrData.middleNameTh, 'Middle Name (TH)');
        fieldValidations.lastNameTh = this.validateRequiredField(ocrData.lastNameTh, 'Last Name (TH)');

        // Validate English name fields
        fieldValidations.titleEn = this.validateRequiredField(ocrData.titleEn, 'Title (EN)');
        fieldValidations.firstNameEn = this.validateRequiredField(ocrData.firstNameEn, 'First Name (EN)');
        fieldValidations.middleNameEn = this.validateOptionalField(ocrData.middleNameEn, 'Middle Name (EN)');
        fieldValidations.lastNameEn = this.validateRequiredField(ocrData.lastNameEn, 'Last Name (EN)');

        // Validate date fields
        fieldValidations.dateOfBirth = this.validateDate(ocrData.dateOfBirth, 'Date of Birth');
        fieldValidations.dateOfIssue = this.validateDate(ocrData.dateOfIssue, 'Date of Issue');
        fieldValidations.dateOfExpiry = this.validateDate(ocrData.dateOfExpiry, 'Date of Expiry');

        // Validate Laser ID
        fieldValidations.laserId = this.validateLaserId(ocrData.laserId);

        // Collect errors from field validations
        Object.values(fieldValidations).forEach(validation => {
            if (!validation.isValid && validation.error) {
                errors.push(validation.error);
            }
        });

        // Check if ID card is expired
        const expiryValidation = this.validateIdCardExpiry(ocrData.dateOfExpiry);
        if (!expiryValidation.isValid) {
            errors.push(expiryValidation.error);
        }

        return {
            isValid: errors.length === 0,
            errors: errors,
            fieldValidations: fieldValidations,
            isExpired: !expiryValidation.isValid
        };
    }

    /**
     * Validate Thai National ID format
     * @param {string} nationalId - National ID number
     * @returns {Object} - Validation result
     */
    static validateNationalId(nationalId) {
        if (!nationalId || typeof nationalId !== 'string') {
            return {
                isValid: false,
                error: 'National ID is required',
                status: 'missing'
            };
        }

        // Remove any spaces or dashes
        const cleanId = nationalId.replace(/[\s-]/g, '');

        // Check if it's exactly 13 digits
        if (!/^\d{13}$/.test(cleanId)) {
            return {
                isValid: false,
                error: 'National ID must be 13 digits',
                status: 'invalid'
            };
        }

        // Validate Thai National ID checksum
        const isValidChecksum = this.validateThaiIdChecksum(cleanId);
        if (!isValidChecksum) {
            return {
                isValid: false,
                error: 'Invalid National ID checksum',
                status: 'invalid'
            };
        }

        return {
            isValid: true,
            status: 'valid'
        };
    }

    /**
     * Validate Thai National ID checksum
     * @param {string} id - 13-digit National ID
     * @returns {boolean} - True if checksum is valid
     */
    static validateThaiIdChecksum(id) {
        if (id.length !== 13) return false;

        let sum = 0;
        for (let i = 0; i < 12; i++) {
            sum += parseInt(id[i]) * (13 - i);
        }

        const remainder = sum % 11;
        const checkDigit = remainder < 2 ? remainder : 11 - remainder;
        
        return checkDigit === parseInt(id[12]);
    }

    /**
     * Validate required field
     * @param {string} value - Field value
     * @param {string} fieldName - Field name for error messages
     * @returns {Object} - Validation result
     */
    static validateRequiredField(value, fieldName) {
        if (!value || typeof value !== 'string' || value.trim() === '') {
            return {
                isValid: false,
                error: `${fieldName} is required`,
                status: 'missing'
            };
        }

        return {
            isValid: true,
            status: 'valid'
        };
    }

    /**
     * Validate optional field
     * @param {string} value - Field value
     * @param {string} fieldName - Field name for error messages
     * @returns {Object} - Validation result
     */
    static validateOptionalField(value, fieldName) {
        if (!value || typeof value !== 'string' || value.trim() === '') {
            return {
                isValid: true,
                status: 'optional'
            };
        }

        return {
            isValid: true,
            status: 'valid'
        };
    }

    /**
     * Validate date field
     * @param {string} dateValue - Date value
     * @param {string} fieldName - Field name for error messages
     * @returns {Object} - Validation result
     */
    static validateDate(dateValue, fieldName) {
        if (!dateValue || typeof dateValue !== 'string') {
            return {
                isValid: false,
                error: `${fieldName} is required`,
                status: 'missing'
            };
        }

        // Try to parse the date - support multiple formats
        const date = this.parseDate(dateValue);
        if (!date || isNaN(date.getTime())) {
            return {
                isValid: false,
                error: `${fieldName} has invalid date format`,
                status: 'invalid'
            };
        }

        return {
            isValid: true,
            status: 'valid',
            parsedDate: date
        };
    }

    /**
     * Parse date from various formats
     * @param {string} dateStr - Date string
     * @returns {Date|null} - Parsed date or null if invalid
     */
    static parseDate(dateStr) {
        if (!dateStr) return null;

        // Common date formats: DD/MM/YYYY, DD-MM-YYYY, YYYY-MM-DD, etc.
        const formats = [
            /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // DD/MM/YYYY
            /^(\d{1,2})-(\d{1,2})-(\d{4})$/, // DD-MM-YYYY
            /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // YYYY-MM-DD
            /^(\d{1,2})\.(\d{1,2})\.(\d{4})$/ // DD.MM.YYYY
        ];

        for (const format of formats) {
            const match = dateStr.match(format);
            if (match) {
                let day, month, year;
                
                if (format.source.startsWith('^(\\d{4})')) {
                    // YYYY-MM-DD format
                    year = parseInt(match[1]);
                    month = parseInt(match[2]) - 1; // Month is 0-indexed
                    day = parseInt(match[3]);
                } else {
                    // DD/MM/YYYY, DD-MM-YYYY, DD.MM.YYYY formats
                    day = parseInt(match[1]);
                    month = parseInt(match[2]) - 1; // Month is 0-indexed
                    year = parseInt(match[3]);
                }

                const date = new Date(year, month, day);
                
                // Validate that the date is actually valid
                if (date.getFullYear() === year && 
                    date.getMonth() === month && 
                    date.getDate() === day) {
                    return date;
                }
            }
        }

        return null;
    }

    /**
     * Validate Laser ID
     * @param {string} laserId - Laser ID value
     * @returns {Object} - Validation result
     */
    static validateLaserId(laserId) {
        if (!laserId || typeof laserId !== 'string' || laserId.trim() === '') {
            return {
                isValid: false,
                error: 'Laser ID is required',
                status: 'missing'
            };
        }

        // Basic validation - Laser ID should be alphanumeric
        if (!/^[A-Za-z0-9]+$/.test(laserId.trim())) {
            return {
                isValid: false,
                error: 'Laser ID contains invalid characters',
                status: 'invalid'
            };
        }

        return {
            isValid: true,
            status: 'valid'
        };
    }

    /**
     * Validate if ID card is expired
     * @param {string} expiryDate - Expiry date string
     * @returns {Object} - Validation result
     */
    static validateIdCardExpiry(expiryDate) {
        const dateValidation = this.validateDate(expiryDate, 'Expiry Date');
        if (!dateValidation.isValid) {
            return dateValidation;
        }

        const expiry = dateValidation.parsedDate;
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Reset time to start of day

        if (expiry < today) {
            return {
                isValid: false,
                error: 'ID card has expired',
                status: 'expired'
            };
        }

        return {
            isValid: true,
            status: 'valid'
        };
    }

    /**
     * Get validation status icon
     * @param {string} status - Validation status
     * @returns {string} - Icon character
     */
    static getStatusIcon(status) {
        switch (status) {
            case 'valid': return '✓';
            case 'invalid': return '✗';
            case 'missing': return '⚠';
            case 'optional': return '○';
            case 'expired': return '⏰';
            default: return '?';
        }
    }

    /**
     * Get validation status color
     * @param {string} status - Validation status
     * @returns {string} - CSS color
     */
    static getStatusColor(status) {
        switch (status) {
            case 'valid': return '#4CAF50';
            case 'invalid': return '#F44336';
            case 'missing': return '#FF9800';
            case 'optional': return '#9E9E9E';
            case 'expired': return '#FF5722';
            default: return '#757575';
        }
    }
}

module.exports = OcrDataValidator;
