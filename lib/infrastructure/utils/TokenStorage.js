/**
 * Utility class for storing and retrieving tokens
 */
class TokenStorage {
  /**
   * Store a token in localStorage
   * @param {string} key - The key to store the token under
   * @param {string} token - The token to store
   * @returns {boolean} - True if the token was stored successfully
   */
  static storeToken(key, token) {
    if (typeof window !== 'undefined' && window.localStorage && token) {
      try {
        localStorage.setItem(key, token);
        return true;
      } catch (error) {
        console.error('Error storing token:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Retrieve a token from localStorage
   * @param {string} key - The key the token is stored under
   * @returns {string|null} - The token or null if not found
   */
  static getToken(key) {
    if (typeof window !== 'undefined' && window.localStorage) {
      return localStorage.getItem(key);
    }
    return null;
  }

  /**
   * Remove a token from localStorage
   * @param {string} key - The key the token is stored under
   * @returns {boolean} - True if the token was removed successfully
   */
  static removeToken(key) {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.removeItem(key);
        return true;
      } catch (error) {
        console.error('Error removing token:', error);
        return false;
      }
    }
    return false;
  }

  /**
   * Store the eKYC token
   * @param {string} token - The eKYC token to store
   * @returns {boolean} - True if the token was stored successfully
   */
  static storeEkycToken(token) {
    return this.storeToken('ekyc_token', token);
  }

  /**
   * Get the stored eKYC token
   * @returns {string|null} - The eKYC token or null if not found
   */
  static getEkycToken() {
    return this.getToken('ekyc_token');
  }

  /**
   * Remove the stored eKYC token
   * @returns {boolean} - True if the token was removed successfully
   */
  static removeEkycToken() {
    return this.removeToken('ekyc_token');
  }

  /**
   * Store the session ID
   * @param {string} sessionId - The session ID to store
   * @returns {boolean} - True if the session ID was stored successfully
   */
  static storeSessionId(sessionId) {
    return this.storeToken('ekyc_session_id', sessionId);
  }

  /**
   * Get the stored session ID
   * @returns {string|null} - The session ID or null if not found
   */
  static getSessionId() {
    return this.getToken('ekyc_session_id');
  }

  /**
   * Remove the stored session ID
   * @returns {boolean} - True if the session ID was removed successfully
   */
  static removeSessionId() {
    return this.removeToken('ekyc_session_id');
  }
}

module.exports = TokenStorage;
