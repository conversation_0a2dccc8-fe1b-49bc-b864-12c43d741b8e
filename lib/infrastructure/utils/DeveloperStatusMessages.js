/**
 * Developer Status Messages Utility
 * Provides logging and status tracking functionality for development purposes
 */
class DeveloperStatusMessages {
    
    /**
     * Log a message with timestamp
     * @param {string} message - The message to log
     * @param {string} level - Log level (info, warn, error, success)
     */
    static logMessage(message, level = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
        
        switch (level) {
            case 'error':
                console.error(`${prefix} ${message}`);
                break;
            case 'warn':
                console.warn(`${prefix} ${message}`);
                break;
            case 'success':
                console.log(`%c${prefix} ${message}`, 'color: green; font-weight: bold;');
                break;
            case 'info':
            default:
                console.log(`${prefix} ${message}`);
                break;
        }
    }

    /**
     * Log FaceTec SDK status
     * @param {string} status - The status message
     */
    static logFaceTecStatus(status) {
        this.logMessage(`FaceTec SDK: ${status}`, 'info');
    }

    /**
     * Log API call status
     * @param {string} endpoint - API endpoint
     * @param {string} method - HTTP method
     * @param {string} status - Status message
     */
    static logApiCall(endpoint, method, status) {
        this.logMessage(`API ${method} ${endpoint}: ${status}`, 'info');
    }

    /**
     * Log success message
     * @param {string} message - Success message
     */
    static logSuccess(message) {
        this.logMessage(message, 'success');
    }

    /**
     * Log error message
     * @param {string} message - Error message
     * @param {Error} error - Error object (optional)
     */
    static logError(message, error = null) {
        let fullMessage = message;
        if (error) {
            fullMessage += ` - ${error.message}`;
        }
        this.logMessage(fullMessage, 'error');
        
        if (error && error.stack) {
            console.error('Stack trace:', error.stack);
        }
    }

    /**
     * Log warning message
     * @param {string} message - Warning message
     */
    static logWarning(message) {
        this.logMessage(message, 'warn');
    }

    /**
     * Log ID scan progress
     * @param {string} step - Current step
     * @param {number} progress - Progress percentage (0-100)
     */
    static logIDScanProgress(step, progress = null) {
        let message = `ID Scan: ${step}`;
        if (progress !== null) {
            message += ` (${Math.round(progress * 100)}%)`;
        }
        this.logMessage(message, 'info');
    }

    /**
     * Log liveness check progress
     * @param {string} step - Current step
     * @param {number} progress - Progress percentage (0-1)
     */
    static logLivenessProgress(step, progress = null) {
        let message = `Liveness Check: ${step}`;
        if (progress !== null) {
            message += ` (${Math.round(progress * 100)}%)`;
        }
        this.logMessage(message, 'info');
    }

    /**
     * Log session information
     * @param {string} sessionId - Session ID
     * @param {string} action - Action being performed
     */
    static logSession(sessionId, action) {
        this.logMessage(`Session ${sessionId}: ${action}`, 'info');
    }

    /**
     * Clear console (for development)
     */
    static clearConsole() {
        if (typeof console.clear === 'function') {
            console.clear();
        }
    }

    /**
     * Log object/data for debugging
     * @param {string} label - Label for the data
     * @param {any} data - Data to log
     */
    static logData(label, data) {
        console.group(`📊 ${label}`);
        console.log(data);
        console.groupEnd();
    }

    /**
     * Log performance timing
     * @param {string} operation - Operation name
     * @param {number} startTime - Start time (performance.now())
     * @param {number} endTime - End time (performance.now())
     */
    static logPerformance(operation, startTime, endTime) {
        const duration = endTime - startTime;
        this.logMessage(`Performance: ${operation} took ${duration.toFixed(2)}ms`, 'info');
    }
}

module.exports = DeveloperStatusMessages; 