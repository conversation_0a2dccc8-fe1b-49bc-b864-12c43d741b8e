/**
 * Utility class for generating UUIDs
 */
class UuidGenerator {
  /**
   * Generate a UUID v4
   * @returns {string} - A UUID v4 string
   */
  static generateUuid() {
    // Implementation of UUID v4 generation
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Get a device identifier
   * This will generate a UUID and store it in localStorage if running in a browser
   * If the UUID already exists in localStorage, it will be reused
   * @returns {string} - A device identifier
   */
  static getDeviceId() {
    if (typeof window !== 'undefined' && window.localStorage) {
      // Running in browser, check localStorage
      let deviceId = localStorage.getItem('ekyc_device_id');
      if (!deviceId) {
        // Generate a new UUID if not found in localStorage
        deviceId = this.generateUuid();
        localStorage.setItem('ekyc_device_id', deviceId);
      }
      return deviceId;
    } else {
      // Not running in browser, generate a new UUID
      return this.generateUuid();
    }
  }

  /**
   * Generate a new UUID every time
   * Unlike getDeviceId, this does not store or reuse UUIDs
   * @returns {string} - A new UUID v4 string
   */
  static getUniqueId() {
    return this.generateUuid();
  }
}

module.exports = UuidGenerator;
