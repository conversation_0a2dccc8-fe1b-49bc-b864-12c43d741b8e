/**
 * FaceTecService
 * Service for interacting with the FaceTec SDK
 */
class FaceTecService {
  /**
   * Load the FaceTecSDK
   * @returns {Promise<Object>} - A promise that resolves to the FaceTecSDK
   */
  loadFaceTecSDK() {
    return new Promise((resolve, reject) => {
      // Check if FaceTecSDK is already loaded
      if (typeof window !== 'undefined' && window.FaceTecSDK) {
        resolve(window.FaceTecSDK);
        return;
      }

      // Create a script element to load the FaceTecSDK
      const script = document.createElement('script');
      script.src = '/core-sdk/FaceTecSDK.js/FaceTecSDK.js'; // Path to the FaceTecSDK script
      script.async = true;
      script.onload = () => {
        if (window.FaceTecSDK) {
          resolve(window.FaceTecSDK);
        } else {
          reject(new Error('FaceTecSDK not found after loading script'));
        }
      };
      script.onerror = () => {
        reject(new Error('Failed to load FaceTecSDK script'));
      };

      // Add the script to the document
      document.head.appendChild(script);
    });
  }

  /**
   * Initialize the FaceTecSDK
   * @param {string} deviceKeyIdentifier - The device key identifier
   * @param {string} publicEncryptionKey - The public encryption key
   * @returns {Promise<boolean>} - A promise that resolves to true if initialization was successful
   */
  async initializeFaceTec(deviceKeyIdentifier, publicEncryptionKey) {
    try {
      const FaceTecSDK = await this.loadFaceTecSDK();
      FaceTecSDK.setResourceDirectory("/core-sdk/FaceTecSDK.js/resources");
      FaceTecSDK.setImagesDirectory("/core-sdk/FaceTec_images");
      return new Promise((resolve, reject) => {
        FaceTecSDK.initializeInDevelopmentMode(
          deviceKeyIdentifier,
          publicEncryptionKey,
          (initializedSuccessfully) => {
            if (initializedSuccessfully) {
              console.log('FaceTecSDK initialized successfully');
              resolve(true);
            } else {
              console.error('FaceTecSDK failed to initialize');
              reject(new Error('FaceTecSDK failed to initialize'));
            }
          }
        );
      });
    } catch (error) {
      console.error('Error loading FaceTecSDK:', error);
      throw error;
    }
  }

  /**
   * Get the FaceTecSDK version
   * @returns {Promise<string>} - A promise that resolves to the FaceTecSDK version
   */
  async getFaceTecVersion() {
    try {
      const FaceTecSDK = await this.loadFaceTecSDK();
      return FaceTecSDK.version();
    } catch (error) {
      console.error('Error getting FaceTecSDK version:', error);
      throw error;
    }
  }
}

module.exports = FaceTecService;
