/**
 * API route to process ID scan data to the eKYC backend API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/idscan-only`;

    // ✅ Forward headers follow spec
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': req.headers['authorization'] || (process.env.JWT_TOKEN ? `Bearer ${process.env.JWT_TOKEN}` : undefined),
      'X-Device-Key': req.headers['x-device-key'],
      'X-User-Agent': req.headers['x-user-agent'],
      'X-Session-Id': req.headers['x-session-id'],
      'X-Tid': req.headers['x-tid'],
      'X-Ekyc-Token': req.headers['x-ekyc-token'],
      'correlationid': req.headers.correlationid
    };

    // Remove undefined headers
    Object.keys(headers).forEach(key => {
      if (headers[key] === undefined) {
        delete headers[key];
      }
    });

    // ✅ Log request body details for debugging (to compare with new handlers)
    console.log('=== IDSCAN-ONLY API HANDLER - REQUEST BODY ANALYSIS ===');
    console.log('📋 Request body type:', typeof req.body);
    console.log('📋 Request body constructor:', req.body?.constructor?.name);

    // Prepare request body - check if already stringified to avoid double stringification
    let requestBody;
    if (typeof req.body === 'string') {
      console.log('⚠️ Request body is already a string - using directly');
      requestBody = req.body;
    } else {
      console.log('📦 Request body is object - stringifying');
      requestBody = JSON.stringify(req.body);
    }

    console.log('📏 Request body size:', requestBody.length, 'characters');
    console.log('📋 Request body starts with:', requestBody.substring(0, 100) + '...');

    // Check for double stringification issue
    if (requestBody.startsWith('"{') || requestBody.startsWith('\"{')) {
      console.error('🚨 IDSCAN-ONLY: DETECTED DOUBLE STRINGIFICATION!');
      console.error('🚨 Request body appears to be double-stringified JSON');
    } else {
      console.log('✅ IDSCAN-ONLY: Request body appears to be properly formatted');
    }

    // Make the POST request with the body from the client
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: requestBody,
    });

    // Get the response data
    const data = await response.json();

    // Transform the response to match PhotoIDScanProcessor expectations
    if (response.ok && data.code === 'CUS-KYC-1000') {
      // Success case - return format expected by PhotoIDScanProcessor
      const successResponse = {
        wasProcessed: true,
        error: false,
        scanResultBlob: data.data?.scanResultBlob || "", // Use scanResultBlob from backend if available
        originalResponse: data // Keep original response for debugging
      };
      
      console.log('Returning success response with scanResultBlob length:', successResponse.scanResultBlob.length);
      return res.status(200).json(successResponse);
    } else {
      // Error case - return format expected by PhotoIDScanProcessor
      const errorResponse = {
        wasProcessed: false,
        error: true,
        errorMessage: data.description || data.message || 'Unknown error',
        originalResponse: data // Keep original response for debugging
      };
      
      console.log('Returning error response:', errorResponse.errorMessage);
      return res.status(response.status).json(errorResponse);
    }
  } catch (error) {
    console.error('Error processing ID scan:', error);
    return res.status(500).json({
      wasProcessed: false,
      error: true,
      errorMessage: 'Failed to process ID scan'
    });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports.default = handler;