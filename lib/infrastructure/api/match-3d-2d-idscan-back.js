/**
 * API route to process back ID scan data to the eKYC backend API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/match-3d-2d-idscan/back`;

    // ✅ Log request body details for debugging
    console.log('=== BACK ID SCAN API HANDLER - REQUEST ANALYSIS ===');
    console.log('📤 Target URL:', url);
    console.log('📋 Request body type:', typeof req.body);
    console.log('📋 Request body constructor:', req.body?.constructor?.name);
    console.log('📋 Request body keys:', req.body ? Object.keys(req.body) : 'No body');

    // Validate request body structure
    if (!req.body) {
      console.error('❌ Request body is missing');
      return res.status(400).json({
        wasProcessed: false,
        error: true,
        errorMessage: 'Request body is required'
      });
    }

    // Log specific parameters for back scan
    const bodyAnalysis = {
      hasIdScan: !!req.body.idScan,
      hasIdScanFrontImage: !!req.body.idScanFrontImage,
      hasIdScanBackImage: !!req.body.idScanBackImage,
      hasEnableConfirmInfo: req.body.enableConfirmInfo !== undefined,
      enableConfirmInfoValue: req.body.enableConfirmInfo
    };
    console.log('📊 Back scan body analysis:', bodyAnalysis);

    // Check for required back scan parameters
    if (!req.body.idScan) {
      console.error('❌ Missing required parameter: idScan');
    }
    if (!req.body.idScanBackImage) {
      console.error('❌ Missing required parameter: idScanBackImage');
    }
    if (req.body.idScanFrontImage) {
      console.log('ℹ️ Front image also present in back scan request');
    }

    // ✅ Forward headers follow spec
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': req.headers['authorization'] || (process.env.JWT_TOKEN ? `Bearer ${process.env.JWT_TOKEN}` : undefined),
      'X-Device-Key': req.headers['x-device-key'],
      'X-User-Agent': req.headers['x-user-agent'],
      'X-Session-Id': req.headers['x-session-id'],
      'X-Tid': req.headers['x-tid'],
      'X-Ekyc-Token': req.headers['x-ekyc-token'],
      'X-Ekyc-Sdk-Version': req.headers['x-ekyc-sdk-version'] || '1.0.0',
      'correlationid': req.headers.correlationid
    };

    // Remove undefined headers
    Object.keys(headers).forEach(key => {
      if (headers[key] === undefined) {
        delete headers[key];
      }
    });

    console.log('📋 Request headers:', Object.keys(headers));

    // Prepare request body - check if already stringified to avoid double stringification
    let requestBody;
    if (typeof req.body === 'string') {
      console.log('⚠️ Request body is already a string - using directly');
      requestBody = req.body;
    } else {
      console.log('📦 Request body is object - stringifying');
      requestBody = JSON.stringify(req.body);
    }

    console.log('📏 Request body size:', requestBody.length, 'characters');
    console.log('📋 Request body starts with:', requestBody.substring(0, 100) + '...');

    // Check for double stringification issue
    if (requestBody.startsWith('"{') || requestBody.startsWith('\"{')) {
      console.error('🚨 DETECTED DOUBLE STRINGIFICATION!');
      console.error('🚨 Request body appears to be double-stringified JSON');
      console.error('🚨 First 200 chars:', requestBody.substring(0, 200));

      // Try to fix by parsing once
      try {
        const parsed = JSON.parse(requestBody);
        if (typeof parsed === 'object') {
          console.log('🔧 Attempting to fix by using parsed object');
          requestBody = JSON.stringify(parsed);
          console.log('✅ Fixed request body starts with:', requestBody.substring(0, 100) + '...');
        }
      } catch (parseError) {
        console.error('❌ Failed to fix double stringification:', parseError.message);
      }
    }

    // Log truncated body for debugging (avoid console spam)
    if (requestBody.length > 1000) {
      const truncated = requestBody.substring(0, 500) + '...[truncated]...' + requestBody.substring(requestBody.length - 500);
      console.log('📄 Request body (truncated):', truncated);
    } else {
      console.log('📄 Request body:', requestBody);
    }

    // Make the POST request with the body from the client
    console.log('📤 Sending request to backend...');
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: requestBody,
    });

    // Get the response data
    const data = await response.json();

    console.log('=== BACK ID SCAN API HANDLER - RESPONSE ANALYSIS ===');
    console.log('📥 Response status:', response.status);
    console.log('📥 Response ok:', response.ok);
    console.log('📥 Response code:', data.code);
    console.log('📥 Response keys:', Object.keys(data));

    // Log response data structure
    if (data.data) {
      console.log('📊 Response data keys:', Object.keys(data.data));
      console.log('📊 Has scanResultBlob:', !!data.data.scanResultBlob);
      console.log('📊 Has documentData:', !!data.data.documentData);

      if (data.data.scanResultBlob) {
        console.log('📦 ScanResultBlob length:', data.data.scanResultBlob.length);
      }

      if (data.data.documentData) {
        console.log('📄 DocumentData length:', data.data.documentData.length);
        try {
          const parsedDocData = JSON.parse(data.data.documentData);
          console.log('📊 DocumentData structure:', {
            hasScannedValues: !!parsedDocData.scannedValues,
            groupCount: parsedDocData.scannedValues?.groups?.length || 0
          });
        } catch (parseError) {
          console.error('❌ Failed to parse documentData:', parseError.message);
        }
      }
    }

    // Transform the response to match PhotoIDMatchProcessor expectations
    if (response.ok && data.code === 'CUS-KYC-1000') {
      // Success case - return format expected by PhotoIDMatchProcessor
      const successResponse = {
        wasProcessed: true,
        error: false,
        scanResultBlob: data.data?.scanResultBlob || "", // Use scanResultBlob from backend if available
        originalResponse: data // Keep original response for debugging
      };

      console.log('✅ SUCCESS - Returning back scan success response');
      console.log('📦 ScanResultBlob length:', successResponse.scanResultBlob.length);
      console.log('📄 Has OCR data:', !!(data.data?.documentData));
      console.log('=== BACK ID SCAN API HANDLER - SUCCESS ===');
      return res.status(200).json(successResponse);
    } else {
      // Error case - return format expected by PhotoIDMatchProcessor
      const errorResponse = {
        wasProcessed: false,
        error: true,
        errorMessage: data.description || data.message || 'Unknown error occurred during back ID scan',
        originalResponse: data // Keep original response for debugging
      };

      console.error('❌ ERROR - Back scan failed');
      console.error('❌ Error message:', errorResponse.errorMessage);
      console.error('❌ Full response data:', JSON.stringify(data, null, 2));
      console.error('=== BACK ID SCAN API HANDLER - ERROR ===');
      return res.status(response.status).json(errorResponse);
    }
  } catch (error) {
    console.error('=== BACK ID SCAN API HANDLER - EXCEPTION ===');
    console.error('❌ Exception type:', error.constructor.name);
    console.error('❌ Exception message:', error.message);
    console.error('❌ Exception stack:', error.stack);

    // Log additional context for network errors
    if (error.cause) {
      console.error('❌ Error cause:', error.cause);
    }

    console.error('=== END BACK ID SCAN EXCEPTION ===');

    return res.status(500).json({
      wasProcessed: false,
      error: true,
      errorMessage: 'Failed to process back ID scan: ' + error.message
    });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports.default = handler;
