/**
 * API route to process liveness check data to the eKYC backend API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Helper function to truncate values for logging
    const truncateForLogging = (obj, maxLength = 20) => {
      if (!obj || typeof obj !== 'object') return obj;

      const truncated = {};
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string' && value.length > maxLength) {
          truncated[key] = value.substring(0, maxLength) + '...';
        } else if (typeof value === 'object' && value !== null) {
          truncated[key] = truncateForLogging(value, maxLength);
        } else {
          truncated[key] = value;
        }
      }
      return truncated;
    };

    // Log incoming request details
    console.log('=== ENROLLMENT-3D API HANDLER - INCOMING REQUEST ===');
    console.log('Request method:', req.method);
    console.log('Request headers received (truncated):', JSON.stringify(truncateForLogging(req.headers), null, 2));
    console.log('Request body received (truncated):', JSON.stringify(truncateForLogging(req.body), null, 2));
    console.log('Request body keys:', Object.keys(req.body || {}));

    // Check for required body parameters
    const requiredBodyParams = ['function', 'faceScan', 'auditTrailImage', 'lowQualityAuditTrailImage'];
    const missingParams = requiredBodyParams.filter(param => !req.body || !req.body[param]);
    if (missingParams.length > 0) {
      console.log('⚠️  MISSING REQUIRED BODY PARAMETERS:', missingParams);
    } else {
      console.log('✅ All required body parameters present');
    }



    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/enrollment-3d`;



    // Forward headers follow spec
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': req.headers['authorization'] || (process.env.JWT_TOKEN ? `Bearer ${process.env.JWT_TOKEN}` : undefined),
      'X-Device-Key': req.headers['x-device-key'],
      'X-User-Agent': req.headers['x-user-agent'],
      'X-Session-Id': req.headers['x-session-id'],
      'X-Tid': req.headers['x-tid'],
      'X-Ekyc-Token': req.headers['x-ekyc-token'],
      'X-Ekyc-Sdk-Version': '1.0.0',
      'correlationid': req.headers.correlationid
    };

    // Remove undefined headers
    Object.keys(headers).forEach(key => {
      if (headers[key] === undefined) {
        delete headers[key];
      }
    });

    // Log outgoing request details
    console.log('=== ENROLLMENT-3D API HANDLER - OUTGOING REQUEST ===');
    console.log('Target URL:', url);
    console.log('Outgoing headers (truncated):', JSON.stringify(truncateForLogging(headers), null, 2));
    console.log('Outgoing body (truncated):', JSON.stringify(truncateForLogging(req.body), null, 2));
    console.log('Outgoing body size (bytes):', JSON.stringify(req.body).length);

    // Make the POST request with the body from the client
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(req.body),
    });

    // Get the response data
    const data = await response.json();

    // Log response details
    console.log('=== ENROLLMENT-3D API HANDLER - BACKEND RESPONSE ===');
    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);
    console.log('Response data:', JSON.stringify(data, null, 2));

    // Transform the response to match LivenessCheckProcessor expectations
    if (response.ok && data.code === 'CUS-KYC-1000') {
      // Success case - return format expected by LivenessCheckProcessor
      const successResponse = {
        wasProcessed: true,
        error: false,
        scanResultBlob: data.data?.scanResultBlob || "", // Use scanResultBlob from backend if available
        originalResponse: data // Keep original response for debugging
      };

      console.log('✅ SUCCESS - Returning success response with scanResultBlob length:', successResponse.scanResultBlob.length);
      console.log('=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ===');
      console.log('Final response:', JSON.stringify(successResponse, null, 2));
      return res.status(200).json(successResponse);
    } else {
      // Error case - return format expected by LivenessCheckProcessor
      const errorResponse = {
        wasProcessed: false,
        error: true,
        errorMessage: data.description || data.message || 'Unknown error',
        originalResponse: data // Keep original response for debugging
      };

      console.log('❌ ERROR - Returning error response:', errorResponse.errorMessage);
      console.log('=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ===');
      console.log('Final response:', JSON.stringify(errorResponse, null, 2));
      return res.status(response.status).json(errorResponse);
    }
  } catch (error) {
    console.error('Error processing liveness check:', error);
    return res.status(500).json({
      wasProcessed: false,
      error: true,
      errorMessage: 'Failed to process liveness check'
    });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports.default = handler;