// Import services
const FaceTecService = require('./infrastructure/services/FaceTecService');

// Create instance of the service
const faceTecService = new FaceTecService();

/**
 * Load the FaceTecSDK
 * @returns {Promise<Object>} - A promise that resolves to the FaceTecSDK
 */
const loadFaceTecSDK = () => {
  return faceTecService.loadFaceTecSDK();
};

/**
 * Initialize the FaceTecSDK
 * @param {string} deviceKeyIdentifier - The device key identifier
 * @param {string} publicEncryptionKey - The public encryption key
 * @returns {Promise<boolean>} - A promise that resolves to true if initialization was successful
 */
const initializeFaceTec = (deviceKeyIdentifier, publicEncryptionKey) => {
  return faceTecService.initializeFaceTec(deviceKeyIdentifier, publicEncryptionKey);
};

/**
 * Get the FaceTecSDK version
 * @returns {Promise<string>} - A promise that resolves to the FaceTecSDK version
 */
const getFaceTecVersion = () => {
  return faceTecService.getFaceTecVersion();
};

// Export the functions
module.exports = {
  loadFaceTecSDK,
  initializeFaceTec,
  getFaceTecVersion
};
