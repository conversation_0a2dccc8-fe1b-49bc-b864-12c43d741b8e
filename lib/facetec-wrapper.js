// This is a simple wrapper module that helps with importing FaceTecSDK in the browser environment

// Function to dynamically load the FaceTecSDK script
const loadFaceTecSDK = () => {
  return new Promise((resolve, reject) => {
    // Check if FaceTecSDK is already loaded
    if (typeof window !== 'undefined' && window.FaceTecSDK) {
      resolve(window.FaceTecSDK);
      return;
    }

    // Create a script element to load the FaceTecSDK
    const script = document.createElement('script');
    script.src = '/core-sdk/FaceTecSDK.js/FaceTecSDK.js'; // Path to the FaceTecSDK script
    script.async = true;
    script.onload = () => {
      if (window.FaceTecSDK) {
        resolve(window.FaceTecSDK);
      } else {
        reject(new Error('FaceTecSDK not found after loading script'));
      }
    };
    script.onerror = () => {
      reject(new Error('Failed to load FaceTecSDK script'));
    };

    // Add the script to the document
    document.head.appendChild(script);
  });
};

// Function to initialize FaceTecSD<PERSON>
const initializeFaceTec = async (deviceKeyIdentifier, publicEncryptionKey) => {
  try {
    const FaceTecSDK = await loadFaceTecSDK();
    
    return new Promise((resolve, reject) => {
      FaceTecSDK.initializeInDevelopmentMode(
        deviceKeyIdentifier,
        publicEncryptionKey,
        (initializedSuccessfully) => {
          if (initializedSuccessfully) {
            console.log('FaceTecSDK initialized successfully');
            resolve(true);
          } else {
            console.error('FaceTecSDK failed to initialize');
            reject(new Error('FaceTecSDK failed to initialize'));
          }
        }
      );
    });
  } catch (error) {
    console.error('Error loading FaceTecSDK:', error);
    throw error;
  }
};

// Function to get FaceTecSDK version
const getFaceTecVersion = async () => {
  try {
    const FaceTecSDK = await loadFaceTecSDK();
    return FaceTecSDK.version();
  } catch (error) {
    console.error('Error getting FaceTecSDK version:', error);
    throw error;
  }
};

// Export the functions
module.exports = {
  loadFaceTecSDK,
  initializeFaceTec,
  getFaceTecVersion
};
