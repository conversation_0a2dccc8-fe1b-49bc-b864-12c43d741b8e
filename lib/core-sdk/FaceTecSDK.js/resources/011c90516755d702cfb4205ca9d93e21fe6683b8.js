var h;h||(h=typeof Module !== 'undefined' ? Module : {});h.lc||(h.lc=0);h.lc++;
h.ENVIRONMENT_IS_PTHREAD||h.$ww||function(a){function b(k,n,p){var q=new XMLHttpRequest;q.open("GET",k,!0);q.responseType="arraybuffer";q.onprogress=function(r){var l=n;r.total&&(l=r.total);if(r.loaded){q.dd?h.Nb[k].loaded=r.loaded:(q.dd=!0,h.Nb||(h.Nb={}),h.Nb[k]={loaded:r.loaded,total:l});var t=l=r=0,v;for(v in h.Nb){var z=h.Nb[v];r+=z.total;l+=z.loaded;t++}r=Math.ceil(r*h.lc/t);h.setStatus&&h.setStatus(`Downloading data... (${l}/${r})`)}else!h.Nb&&h.setStatus&&h.setStatus("Downloading data...")};
q.onerror=function(){throw Error("NetworkError for: "+k);};q.onload=function(){if(200==q.status||304==q.status||206==q.status||0==q.status&&q.response)p(q.response);else throw Error(q.statusText+" : "+q.responseURL);};q.send(null)}function c(k){console.error("package error:",k)}function d(){function k(r,l,t){this.start=r;this.end=l;this.audio=t}function n(r){if(!r)throw"Loading data file failed."+Error().stack;if(r.constructor.name!==ArrayBuffer.name)throw"bad input to processPackageData"+Error().stack;
r=new Uint8Array(r);k.prototype.fd=r;r=a.files;for(var l=0;l<r.length;++l)k.prototype.uc[r[l].filename].onload();h.removeRunDependency("datafile_011c90516755d702cfb4205ca9d93e21fe6683b8.data")}h.FS_createPath("/","models",!0,!0);k.prototype={uc:{},open:function(r,l){this.name=l;this.uc[l]=this;h.addRunDependency(`fp ${this.name}`)},send:function(){},onload:function(){this.finish(this.fd.subarray(this.start,this.end))},finish:function(r){h.FS_createDataFile(this.name,null,r,!0,!0,!0);h.removeRunDependency(`fp ${this.name}`);
this.uc[this.name]=null}};for(var p=a.files,q=0;q<p.length;++q)(new k(p[q].start,p[q].end,p[q].audio||0)).open("GET",p[q].filename);h.addRunDependency("datafile_011c90516755d702cfb4205ca9d93e21fe6683b8.data");h.Lc||(h.Lc={});h.Lc["011c90516755d702cfb4205ca9d93e21fe6683b8.data"]={me:!1};m?(n(m),m=null):g=n}"object"===typeof window?window.encodeURIComponent(window.location.pathname.toString().substring(0,window.location.pathname.toString().lastIndexOf("/"))+"/"):"undefined"===typeof process&&"undefined"!==
typeof location&&encodeURIComponent(location.pathname.toString().substring(0,location.pathname.toString().lastIndexOf("/"))+"/");"function"!==typeof h.locateFilePackage||h.locateFile||(h.locateFile=h.locateFilePackage,aa("warning: you defined Module.locateFilePackage, that has been renamed to Module.locateFile (using your locateFilePackage for now)"));var e=h.locateFile?h.locateFile("011c90516755d702cfb4205ca9d93e21fe6683b8.data",""):"011c90516755d702cfb4205ca9d93e21fe6683b8.data",f=a.remote_package_size,
g=null,m=h.getPreloadedPackage?h.getPreloadedPackage(e,f):null;m||b(e,f,function(k){g?(g(k),g=null):m=k},c);h.calledRun?d():(h.preRun||(h.preRun=[]),h.preRun.push(d))}({files:[{filename:"/models/1f5b84f51ce0fcfbb76e904b7bcaa7560f601e1394a0b29367a09385312287eb",start:0,end:11840},{filename:"/models/2b075ac1a6132b5b8a4c9ef0ba6b0cd84db7838aca9a000e50d907f40770a4ab",start:11840,end:23824},{filename:"/models/4c4774668f9b9333977fc891e7695420a0b4c27cc2c1cd3920ce9e0870e3c0e8",start:23824,end:76848},{filename:"/models/59cc2a9af81aaca2376702c2490650f4da2775fa673274db98aad41b7ef101c0",
start:76848,end:129456},{filename:"/models/5b63e98b991aedabb60665503384f30bffd939decf9433883b30b78011ee501a",start:129456,end:182912},{filename:"/models/66388dc76dc16bc6b76b682edd218a575bf45b9b",start:182912,end:231504},{filename:"/models/6b3133f0f39ff89a2a169d61176ee17cafacc5e288f334e2b64ee82892d11ccd",start:231504,end:1731936},{filename:"/models/9077d16225f9314163ef1e7db6fc7d4088bb903d134bd95f23d5591ca4dfbfca",start:1731936,end:1785008},{filename:"/models/a74f2afb9d20f2375ccbd14e67c094b85c89ceb608f7cf8ae04f3f646a6c5672",
start:1785008,end:1839904},{filename:"/models/b501893e75f62ee1707643e35b21109927b07ed5b202321c961b424cbc2e4695",start:1839904,end:1892832},{filename:"/models/dbd7a353f0130bb983d6ba05917e9be991d70e8f028df4b74e30bc6497ef7f71",start:1892832,end:1945712},{filename:"/models/f2.xml",start:1945712,end:2073232},{filename:"/models/fd6d368a5658496536e2bfae170d1b823a3629b242cafc09784bfba4e56d8c80",start:2073232,end:2082160},{filename:"/models/vu0ilin6we3lrzo5f83f7qs2jul4aq7v4aoynrmch8zfvpi8ezrfyafa4t0fx87l",
start:2082160,end:2091664}],remote_package_size:2091664});var crypto;crypto||(crypto={getRandomValues:function(a){for(var b=0;b<a.length;b++)a[b]=h.eJcfPvLCbm.apply(null)}});var ba=Object.assign({},h),ca=[],da="./this.program",ea=(a,b)=>{throw b;},fa="object"==typeof window,ha="function"==typeof importScripts,ia="",ja,ka,ma;
if(fa||ha)ha?ia=self.location.href:"undefined"!=typeof document&&document.currentScript&&(ia=document.currentScript.src),ia=0!==ia.indexOf("blob:")?ia.substr(0,ia.replace(/[?#].*/,"").lastIndexOf("/")+1):"",ja=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.send(null);return b.responseText},ha&&(ma=a=>{var b=new XMLHttpRequest;b.open("GET",a,!1);b.responseType="arraybuffer";b.send(null);return new Uint8Array(b.response)}),ka=(a,b,c)=>{var d=new XMLHttpRequest;d.open("GET",a,!0);d.responseType="arraybuffer";
d.onload=()=>{200==d.status||0==d.status&&d.response?b(d.response):c()};d.onerror=c;d.send(null)};var na=h.print||console.log.bind(console),aa=h.printErr||console.error.bind(console);Object.assign(h,ba);ba=null;h.arguments&&(ca=h.arguments);h.thisProgram&&(da=h.thisProgram);h.quit&&(ea=h.quit);var oa;h.wasmBinary&&(oa=h.wasmBinary);var noExitRuntime=h.noExitRuntime||!0;"object"!=typeof WebAssembly&&u("no native wasm support detected");var pa,qa=!1,w,x,y,ra,A,B,sa,ta;
function ua(){var a=pa.buffer;h.HEAP8=w=new Int8Array(a);h.HEAP16=y=new Int16Array(a);h.HEAP32=A=new Int32Array(a);h.HEAPU8=x=new Uint8Array(a);h.HEAPU16=ra=new Uint16Array(a);h.HEAPU32=B=new Uint32Array(a);h.HEAPF32=sa=new Float32Array(a);h.HEAPF64=ta=new Float64Array(a)}var C,va=[],wa=[],xa=[],ya=[];function za(){var a=h.preRun.shift();va.unshift(a)}var Aa=0,Ba=null,Ca=null;function Da(){Aa++;h.monitorRunDependencies&&h.monitorRunDependencies(Aa)}
function Ea(){Aa--;h.monitorRunDependencies&&h.monitorRunDependencies(Aa);if(0==Aa&&(null!==Ba&&(clearInterval(Ba),Ba=null),Ca)){var a=Ca;Ca=null;a()}}function u(a){if(h.onAbort)h.onAbort(a);a="Aborted("+a+")";aa(a);qa=!0;throw new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");}function Fa(a){return a.startsWith("data:application/octet-stream;base64,")}var Ga;Ga="011c90516755d702cfb4205ca9d93e21fe6683b8.wasm";if(!Fa(Ga)){var Ha=Ga;Ga=h.locateFile?h.locateFile(Ha,ia):ia+Ha}
function Ia(a){try{if(a==Ga&&oa)return new Uint8Array(oa);if(ma)return ma(a);throw"both async and sync fetching of the wasm failed";}catch(b){u(b)}}function Ja(a){return oa||!fa&&!ha||"function"!=typeof fetch?Promise.resolve().then(()=>Ia(a)):fetch(a,{credentials:"same-origin"}).then(b=>{if(!b.ok)throw"failed to load wasm binary file at '"+a+"'";return b.arrayBuffer()}).catch(()=>Ia(a))}
function Ka(a,b,c){return Ja(a).then(d=>WebAssembly.instantiate(d,b)).then(d=>d).then(c,d=>{aa("failed to asynchronously prepare wasm: "+d);u(d)})}function La(a,b){var c=Ga;oa||"function"!=typeof WebAssembly.instantiateStreaming||Fa(c)||"function"!=typeof fetch?Ka(c,a,b):fetch(c,{credentials:"same-origin"}).then(d=>WebAssembly.instantiateStreaming(d,a).then(b,function(e){aa("wasm streaming compile failed: "+e);aa("falling back to ArrayBuffer instantiation");return Ka(c,a,b)}))}var Ma,Na;
function Oa(a){this.name="ExitStatus";this.message=`Program terminated with exit(${a})`;this.status=a}var Pa=a=>{for(;0<a.length;)a.shift()(h)},Qa=[],Ra=0,D=0;
function Sa(a){this.Rb=a;this.mb=a-24;this.Id=function(b){B[this.mb+4>>2]=b};this.Gc=function(){return B[this.mb+4>>2]};this.Hd=function(b){B[this.mb+8>>2]=b};this.Tc=function(b){w[this.mb+12>>0]=b?1:0};this.od=function(){return 0!=w[this.mb+12>>0]};this.Uc=function(b){w[this.mb+13>>0]=b?1:0};this.qd=function(){return 0!=w[this.mb+13>>0]};this.Hc=function(b,c){this.Sc(0);this.Id(b);this.Hd(c)};this.Sc=function(b){B[this.mb+16>>2]=b};this.nd=function(){return B[this.mb+16>>2]};this.pd=function(){if(Ta(this.Gc()))return B[this.Rb>>
2];var b=this.nd();return 0!==b?b:this.Rb}}function Ua(){var a=D;if(!a)return Va(0),0;var b=new Sa(a);b.Sc(a);var c=b.Gc();if(!c)return Va(0),a;for(var d=0;d<arguments.length;d++){var e=arguments[d];if(0===e||e===c)break;if(Wa(e,c,b.mb+16))return Va(e),a}Va(c);return a}
var Xa=(a,b)=>{for(var c=0,d=a.length-1;0<=d;d--){var e=a[d];"."===e?a.splice(d,1):".."===e?(a.splice(d,1),c++):c&&(a.splice(d,1),c--)}if(b)for(;c;c--)a.unshift("..");return a},E=a=>{var b="/"===a.charAt(0),c="/"===a.substr(-1);(a=Xa(a.split("/").filter(d=>!!d),!b).join("/"))||b||(a=".");a&&c&&(a+="/");return(b?"/":"")+a},Ya=a=>{var b=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(a).slice(1);a=b[0];b=b[1];if(!a&&!b)return".";b&&(b=b.substr(0,b.length-1));return a+b},Za=a=>{if("/"===
a)return"/";a=E(a);a=a.replace(/\/$/,"");var b=a.lastIndexOf("/");return-1===b?a:a.substr(b+1)},$a=()=>{if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return a=>crypto.getRandomValues(a);u("initRandomDevice")},ab=a=>(ab=$a())(a);
function bb(){for(var a="",b=!1,c=arguments.length-1;-1<=c&&!b;c--){b=0<=c?arguments[c]:"/";if("string"!=typeof b)throw new TypeError("Arguments to path.resolve must be strings");if(!b)return"";a=b+"/"+a;b="/"===b.charAt(0)}a=Xa(a.split("/").filter(d=>!!d),!b).join("/");return(b?"/":"")+a||"."}
var cb=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);127>=d?b++:2047>=d?b+=2:55296<=d&&57343>=d?(b+=4,++c):b+=3}return b},db=(a,b,c,d)=>{if(!(0<d))return 0;var e=c;d=c+d-1;for(var f=0;f<a.length;++f){var g=a.charCodeAt(f);if(55296<=g&&57343>=g){var m=a.charCodeAt(++f);g=65536+((g&1023)<<10)|m&1023}if(127>=g){if(c>=d)break;b[c++]=g}else{if(2047>=g){if(c+1>=d)break;b[c++]=192|g>>6}else{if(65535>=g){if(c+2>=d)break;b[c++]=224|g>>12}else{if(c+3>=d)break;b[c++]=240|g>>18;b[c++]=128|g>>12&63}b[c++]=
128|g>>6&63}b[c++]=128|g&63}}b[c]=0;return c-e};function eb(a,b){var c=Array(cb(a)+1);a=db(a,c,0,c.length);b&&(c.length=a);return c}
var fb="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0,G=(a,b,c)=>{var d=b+c;for(c=b;a[c]&&!(c>=d);)++c;if(16<c-b&&a.buffer&&fb)return fb.decode(a.subarray(b,c));for(d="";b<c;){var e=a[b++];if(e&128){var f=a[b++]&63;if(192==(e&224))d+=String.fromCharCode((e&31)<<6|f);else{var g=a[b++]&63;e=224==(e&240)?(e&15)<<12|f<<6|g:(e&7)<<18|f<<12|g<<6|a[b++]&63;65536>e?d+=String.fromCharCode(e):(e-=65536,d+=String.fromCharCode(55296|e>>10,56320|e&1023))}}else d+=String.fromCharCode(e)}return d},
gb=[];function hb(a,b){gb[a]={input:[],tb:[],Eb:b};ib(a,jb)}
var jb={open:function(a){var b=gb[a.node.dc];if(!b)throw new I(43);a.pb=b;a.seekable=!1},close:function(a){a.pb.Eb.Zb(a.pb)},Zb:function(a){a.pb.Eb.Zb(a.pb)},read:function(a,b,c,d){if(!a.pb||!a.pb.Eb.Fc)throw new I(60);for(var e=0,f=0;f<d;f++){try{var g=a.pb.Eb.Fc(a.pb)}catch(m){throw new I(29);}if(void 0===g&&0===e)throw new I(6);if(null===g||void 0===g)break;e++;b[c+f]=g}e&&(a.node.timestamp=Date.now());return e},write:function(a,b,c,d){if(!a.pb||!a.pb.Eb.rc)throw new I(60);try{for(var e=0;e<d;e++)a.pb.Eb.rc(a.pb,
b[c+e])}catch(f){throw new I(29);}d&&(a.node.timestamp=Date.now());return e}},kb={Fc:function(a){if(!a.input.length){var b=null;"undefined"!=typeof window&&"function"==typeof window.prompt?(b=window.prompt("Input: "),null!==b&&(b+="\n")):"function"==typeof readline&&(b=readline(),null!==b&&(b+="\n"));if(!b)return null;a.input=eb(b,!0)}return a.input.shift()},rc:function(a,b){null===b||10===b?(na(G(a.tb,0)),a.tb=[]):0!=b&&a.tb.push(b)},Zb:function(a){a.tb&&0<a.tb.length&&(na(G(a.tb,0)),a.tb=[])},vd:function(){return{he:25856,
je:5,ge:191,ie:35387,fe:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},wd:function(){return 0},xd:function(){return[24,80]}},lb={rc:function(a,b){null===b||10===b?(aa(G(a.tb,0)),a.tb=[]):0!=b&&a.tb.push(b)},Zb:function(a){a.tb&&0<a.tb.length&&(aa(G(a.tb,0)),a.tb=[])}},J={Bb:null,Db:function(){return J.createNode(null,"/",16895,0)},createNode:function(a,b,c,d){if(24576===(c&61440)||4096===(c&61440))throw new I(63);J.Bb||(J.Bb={dir:{node:{Hb:J.lb.Hb,wb:J.lb.wb,Sb:J.lb.Sb,
cc:J.lb.cc,Pc:J.lb.Pc,kc:J.lb.kc,ec:J.lb.ec,Nc:J.lb.Nc,hc:J.lb.hc},stream:{Kb:J.nb.Kb}},file:{node:{Hb:J.lb.Hb,wb:J.lb.wb},stream:{Kb:J.nb.Kb,read:J.nb.read,write:J.nb.write,Wb:J.nb.Wb,qc:J.nb.qc,Kc:J.nb.Kc}},link:{node:{Hb:J.lb.Hb,wb:J.lb.wb,Ub:J.lb.Ub},stream:{}},yc:{node:{Hb:J.lb.Hb,wb:J.lb.wb},stream:mb}});c=nb(a,b,c,d);16384===(c.mode&61440)?(c.lb=J.Bb.dir.node,c.nb=J.Bb.dir.stream,c.kb={}):32768===(c.mode&61440)?(c.lb=J.Bb.file.node,c.nb=J.Bb.file.stream,c.qb=0,c.kb=null):40960===(c.mode&61440)?
(c.lb=J.Bb.link.node,c.nb=J.Bb.link.stream):8192===(c.mode&61440)&&(c.lb=J.Bb.yc.node,c.nb=J.Bb.yc.stream);c.timestamp=Date.now();a&&(a.kb[b]=c,a.timestamp=c.timestamp);return c},ne:function(a){return a.kb?a.kb.subarray?a.kb.subarray(0,a.qb):new Uint8Array(a.kb):new Uint8Array(0)},Bc:function(a,b){var c=a.kb?a.kb.length:0;c>=b||(b=Math.max(b,c*(1048576>c?2:1.125)>>>0),0!=c&&(b=Math.max(b,256)),c=a.kb,a.kb=new Uint8Array(b),0<a.qb&&a.kb.set(c.subarray(0,a.qb),0))},Ed:function(a,b){if(a.qb!=b)if(0==
b)a.kb=null,a.qb=0;else{var c=a.kb;a.kb=new Uint8Array(b);c&&a.kb.set(c.subarray(0,Math.min(b,a.qb)));a.qb=b}},lb:{Hb:function(a){var b={};b.le=8192===(a.mode&61440)?a.id:1;b.pe=a.id;b.mode=a.mode;b.se=1;b.uid=0;b.oe=0;b.dc=a.dc;b.size=16384===(a.mode&61440)?4096:32768===(a.mode&61440)?a.qb:40960===(a.mode&61440)?a.link.length:0;b.de=new Date(a.timestamp);b.re=new Date(a.timestamp);b.ke=new Date(a.timestamp);b.ed=4096;b.ee=Math.ceil(b.size/b.ed);return b},wb:function(a,b){void 0!==b.mode&&(a.mode=
b.mode);void 0!==b.timestamp&&(a.timestamp=b.timestamp);void 0!==b.size&&J.Ed(a,b.size)},Sb:function(){throw ob[44];},cc:function(a,b,c,d){return J.createNode(a,b,c,d)},Pc:function(a,b,c){if(16384===(a.mode&61440)){try{var d=pb(b,c)}catch(f){}if(d)for(var e in d.kb)throw new I(55);}delete a.parent.kb[a.name];a.parent.timestamp=Date.now();a.name=c;b.kb[c]=a;b.timestamp=a.parent.timestamp;a.parent=b},kc:function(a,b){delete a.kb[b];a.timestamp=Date.now()},ec:function(a,b){var c=pb(a,b),d;for(d in c.kb)throw new I(55);
delete a.kb[b];a.timestamp=Date.now()},Nc:function(a){var b=[".",".."],c;for(c in a.kb)a.kb.hasOwnProperty(c)&&b.push(c);return b},hc:function(a,b,c){a=J.createNode(a,b,41471,0);a.link=c;return a},Ub:function(a){if(40960!==(a.mode&61440))throw new I(28);return a.link}},nb:{read:function(a,b,c,d,e){var f=a.node.kb;if(e>=a.node.qb)return 0;a=Math.min(a.node.qb-e,d);if(8<a&&f.subarray)b.set(f.subarray(e,e+a),c);else for(d=0;d<a;d++)b[c+d]=f[e+d];return a},write:function(a,b,c,d,e,f){b.buffer===w.buffer&&
(f=!1);if(!d)return 0;a=a.node;a.timestamp=Date.now();if(b.subarray&&(!a.kb||a.kb.subarray)){if(f)return a.kb=b.subarray(c,c+d),a.qb=d;if(0===a.qb&&0===e)return a.kb=b.slice(c,c+d),a.qb=d;if(e+d<=a.qb)return a.kb.set(b.subarray(c,c+d),e),d}J.Bc(a,e+d);if(a.kb.subarray&&b.subarray)a.kb.set(b.subarray(c,c+d),e);else for(f=0;f<d;f++)a.kb[e+f]=b[c+f];a.qb=Math.max(a.qb,e+d);return d},Kb:function(a,b,c){1===c?b+=a.position:2===c&&32768===(a.node.mode&61440)&&(b+=a.node.qb);if(0>b)throw new I(28);return b},
Wb:function(a,b,c){J.Bc(a.node,b+c);a.node.qb=Math.max(a.node.qb,b+c)},qc:function(a,b,c,d,e){if(32768!==(a.node.mode&61440))throw new I(43);a=a.node.kb;if(e&2||a.buffer!==w.buffer){if(0<c||c+b<a.length)a=a.subarray?a.subarray(c,c+b):Array.prototype.slice.call(a,c,c+b);c=!0;u();b=void 0;if(!b)throw new I(48);w.set(a,b)}else c=!1,b=a.byteOffset;return{mb:b,zb:c}},Kc:function(a,b,c,d){J.nb.write(a,b,0,d,c,!1);return 0}}},qb=(a,b,c)=>{var d=`al ${a}`;ka(a,e=>{e||u(`Loading data file "${a}" failed (no arrayBuffer).`);
b(new Uint8Array(e));d&&Ea(d)},()=>{if(c)c();else throw`Loading data file "${a}" failed.`;});d&&Da(d)},rb=h.preloadPlugins||[];function sb(a,b,c,d){"undefined"!=typeof Browser&&Browser.Hc();var e=!1;rb.forEach(function(f){!e&&f.canHandle(b)&&(f.handle(a,b,c,d),e=!0)});return e}function tb(a,b,c,d,e,f,g,m,k,n){function p(l){function t(v){n&&n();m||ub(a,b,v,d,e,k);f&&f();Ea(r)}sb(l,q,t,()=>{g&&g();Ea(r)})||t(l)}var q=b?bb(E(a+"/"+b)):a,r=`cp ${q}`;Da(r);"string"==typeof c?qb(c,l=>p(l),g):p(c)}
function vb(a,b){var c=0;a&&(c|=365);b&&(c|=146);return c}
var wb=null,xb={},yb=[],zb=1,Ab=null,Bb=!0,I=null,ob={},L=(a,b={})=>{a=bb(a);if(!a)return{path:"",node:null};b=Object.assign({Dc:!0,tc:0},b);if(8<b.tc)throw new I(32);a=a.split("/").filter(g=>!!g);for(var c=wb,d="/",e=0;e<a.length;e++){var f=e===a.length-1;if(f&&b.parent)break;c=pb(c,a[e]);d=E(d+"/"+a[e]);c.Pb&&(!f||f&&b.Dc)&&(c=c.Pb.root);if(!f||b.mc)for(f=0;40960===(c.mode&61440);)if(c=Cb(d),d=bb(Ya(d),c),c=L(d,{tc:b.tc+1}).node,40<f++)throw new I(32);}return{path:d,node:c}},Db=a=>{for(var b;;){if(a===
a.parent)return a=a.Db.Jc,b?"/"!==a[a.length-1]?`${a}/${b}`:a+b:a;b=b?`${a.name}/${b}`:a.name;a=a.parent}},Eb=(a,b)=>{for(var c=0,d=0;d<b.length;d++)c=(c<<5)-c+b.charCodeAt(d)|0;return(a+c>>>0)%Ab.length},Fb=a=>{var b=Eb(a.parent.id,a.name);if(Ab[b]===a)Ab[b]=a.Qb;else for(b=Ab[b];b;){if(b.Qb===a){b.Qb=a.Qb;break}b=b.Qb}},pb=(a,b)=>{var c;if(c=(c=Gb(a,"x"))?c:a.lb.Sb?0:2)throw new I(c,a);for(c=Ab[Eb(a.id,b)];c;c=c.Qb){var d=c.name;if(c.parent.id===a.id&&d===b)return c}return a.lb.Sb(a,b)},nb=(a,b,
c,d)=>{a=new Hb(a,b,c,d);b=Eb(a.parent.id,a.name);a.Qb=Ab[b];return Ab[b]=a},Ib=a=>{var b=["r","w","rw"][a&3];a&512&&(b+="w");return b},Gb=(a,b)=>{if(Bb)return 0;if(!b.includes("r")||a.mode&292){if(b.includes("w")&&!(a.mode&146)||b.includes("x")&&!(a.mode&73))return 2}else return 2;return 0},Jb=(a,b)=>{try{return pb(a,b),20}catch(c){}return Gb(a,"wx")},Kb=(a,b,c)=>{try{var d=pb(a,b)}catch(e){return e.Ab}if(a=Gb(a,"wx"))return a;if(c){if(16384!==(d.mode&61440))return 54;if(d===d.parent||"/"===Db(d))return 10}else if(16384===
(d.mode&61440))return 31;return 0},Lb=()=>{for(var a=0;4096>=a;a++)if(!yb[a])return a;throw new I(33);},Mb=a=>{a=yb[a];if(!a)throw new I(8);return a},Ob=(a,b=-1)=>{Nb||(Nb=function(){this.fc={}},Nb.prototype={},Object.defineProperties(Nb.prototype,{object:{get:function(){return this.node},set:function(c){this.node=c}},flags:{get:function(){return this.fc.flags},set:function(c){this.fc.flags=c}},position:{get:function(){return this.fc.position},set:function(c){this.fc.position=c}}}));a=Object.assign(new Nb,
a);-1==b&&(b=Lb());a.Gb=b;return yb[b]=a},mb={open:a=>{a.nb=xb[a.node.dc].nb;a.nb.open&&a.nb.open(a)},Kb:()=>{throw new I(70);}},ib=(a,b)=>{xb[a]={nb:b}},Pb=(a,b)=>{var c="/"===b,d=!b;if(c&&wb)throw new I(10);if(!c&&!d){var e=L(b,{Dc:!1});b=e.path;e=e.node;if(e.Pb)throw new I(10);if(16384!==(e.mode&61440))throw new I(54);}b={type:a,ue:{},Jc:b,Ad:[]};a=a.Db(b);a.Db=b;b.root=a;c?wb=a:e&&(e.Pb=b,e.Db&&e.Db.Ad.push(b))},M=(a,b,c)=>{var d=L(a,{parent:!0}).node;a=Za(a);if(!a||"."===a||".."===a)throw new I(28);
var e=Jb(d,a);if(e)throw new I(e);if(!d.lb.cc)throw new I(63);return d.lb.cc(d,a,b,c)},Qb=(a,b,c)=>{"undefined"==typeof c&&(c=b,b=438);return M(a,b|8192,c)},Rb=(a,b)=>{if(!bb(a))throw new I(44);var c=L(b,{parent:!0}).node;if(!c)throw new I(44);b=Za(b);var d=Jb(c,b);if(d)throw new I(d);if(!c.lb.hc)throw new I(63);c.lb.hc(c,b,a)},Sb=a=>{var b=L(a,{parent:!0}).node;a=Za(a);var c=pb(b,a),d=Kb(b,a,!0);if(d)throw new I(d);if(!b.lb.ec)throw new I(63);if(c.Pb)throw new I(10);b.lb.ec(b,a);Fb(c)},Tb=a=>{var b=
L(a,{parent:!0}).node;if(!b)throw new I(44);a=Za(a);var c=pb(b,a),d=Kb(b,a,!1);if(d)throw new I(d);if(!b.lb.kc)throw new I(63);if(c.Pb)throw new I(10);b.lb.kc(b,a);Fb(c)},Cb=a=>{a=L(a).node;if(!a)throw new I(44);if(!a.lb.Ub)throw new I(28);return bb(Db(a.parent),a.lb.Ub(a))},Ub=(a,b)=>{a="string"==typeof a?L(a,{mc:!0}).node:a;if(!a.lb.wb)throw new I(63);a.lb.wb(a,{mode:b&4095|a.mode&-4096,timestamp:Date.now()})},Wb=(a,b,c)=>{if(""===a)throw new I(44);if("string"==typeof b){var d={r:0,"r+":2,w:577,
"w+":578,a:1089,"a+":1090}[b];if("undefined"==typeof d)throw Error(`Unknown file open mode: ${b}`);b=d}c=b&64?("undefined"==typeof c?438:c)&4095|32768:0;if("object"==typeof a)var e=a;else{a=E(a);try{e=L(a,{mc:!(b&131072)}).node}catch(f){}}d=!1;if(b&64)if(e){if(b&128)throw new I(20);}else e=M(a,c,0),d=!0;if(!e)throw new I(44);8192===(e.mode&61440)&&(b&=-513);if(b&65536&&16384!==(e.mode&61440))throw new I(54);if(!d&&(c=e?40960===(e.mode&61440)?32:16384===(e.mode&61440)&&("r"!==Ib(b)||b&512)?31:Gb(e,
Ib(b)):44))throw new I(c);if(b&512&&!d){c=e;c="string"==typeof c?L(c,{mc:!0}).node:c;if(!c.lb.wb)throw new I(63);if(16384===(c.mode&61440))throw new I(31);if(32768!==(c.mode&61440))throw new I(28);if(d=Gb(c,"w"))throw new I(d);c.lb.wb(c,{size:0,timestamp:Date.now()})}b&=-131713;e=Ob({node:e,path:Db(e),flags:b,seekable:!0,position:0,nb:e.nb,Rd:[],error:!1});e.nb.open&&e.nb.open(e);!h.logReadFiles||b&1||(Vb||(Vb={}),a in Vb||(Vb[a]=1));return e},Xb=a=>{if(null===a.Gb)throw new I(8);a.nc&&(a.nc=null);
try{a.nb.close&&a.nb.close(a)}catch(b){throw b;}finally{yb[a.Gb]=null}a.Gb=null},Yb=(a,b,c)=>{if(null===a.Gb)throw new I(8);if(!a.seekable||!a.nb.Kb)throw new I(70);if(0!=c&&1!=c&&2!=c)throw new I(28);a.position=a.nb.Kb(a,b,c);a.Rd=[]},Zb=(a,b,c,d,e,f)=>{if(0>d||0>e)throw new I(28);if(null===a.Gb)throw new I(8);if(0===(a.flags&2097155))throw new I(8);if(16384===(a.node.mode&61440))throw new I(31);if(!a.nb.write)throw new I(28);a.seekable&&a.flags&1024&&Yb(a,0,2);var g="undefined"!=typeof e;if(!g)e=
a.position;else if(!a.seekable)throw new I(70);b=a.nb.write(a,b,c,d,e,f);g||(a.position+=b);return b},$b=()=>{I||(I=function(a,b){this.name="ErrnoError";this.node=b;this.Gd=function(c){this.Ab=c};this.Gd(a);this.message="FS error"},I.prototype=Error(),I.prototype.constructor=I,[44].forEach(a=>{ob[a]=new I(a);ob[a].stack="<generic error, no stack>"}))},ac,bc=(a,b)=>{a="string"==typeof a?a:Db(a);for(b=b.split("/").reverse();b.length;){var c=b.pop();if(c){var d=E(a+"/"+c);try{M(d,16895,0)}catch(e){}a=
d}}return d},cc=(a,b,c,d)=>{a=E(("string"==typeof a?a:Db(a))+"/"+b);c=vb(c,d);return M(a,(void 0!==c?c:438)&4095|32768,0)},ub=(a,b,c,d,e,f)=>{var g=b;a&&(a="string"==typeof a?a:Db(a),g=b?E(a+"/"+b):a);a=vb(d,e);g=M(g,(void 0!==a?a:438)&4095|32768,0);if(c){if("string"==typeof c){b=Array(c.length);d=0;for(e=c.length;d<e;++d)b[d]=c.charCodeAt(d);c=b}Ub(g,a|146);b=Wb(g,577);Zb(b,c,0,c.length,0,f);Xb(b);Ub(g,a)}return g},N=(a,b,c,d)=>{a=E(("string"==typeof a?a:Db(a))+"/"+b);b=vb(!!c,!!d);N.Ic||(N.Ic=64);
var e=N.Ic++<<8|0;ib(e,{open:f=>{f.seekable=!1},close:()=>{d&&d.buffer&&d.buffer.length&&d(10)},read:(f,g,m,k)=>{for(var n=0,p=0;p<k;p++){try{var q=c()}catch(r){throw new I(29);}if(void 0===q&&0===n)throw new I(6);if(null===q||void 0===q)break;n++;g[m+p]=q}n&&(f.node.timestamp=Date.now());return n},write:(f,g,m,k)=>{for(var n=0;n<k;n++)try{d(g[m+n])}catch(p){throw new I(29);}k&&(f.node.timestamp=Date.now());return n}});return Qb(a,b,e)},dc=a=>{if(!(a.yd||a.zd||a.link||a.kb)){if("undefined"!=typeof XMLHttpRequest)throw Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");
if(ja)try{a.kb=eb(ja(a.url),!0),a.qb=a.kb.length}catch(b){throw new I(29);}else throw Error("Cannot load without read() or XMLHttpRequest.");}},ec=(a,b,c,d,e)=>{function f(){this.pc=!1;this.Yb=[]}f.prototype.get=function(p){if(!(p>this.length-1||0>p)){var q=p%this.zc;return this.$b(p/this.zc|0)[q]}};f.prototype.Fd=function(p){this.$b=p};f.prototype.xc=function(){var p=new XMLHttpRequest;p.open("HEAD",c,!1);p.send(null);if(!(200<=p.status&&300>p.status||304===p.status))throw Error("Couldn't load "+
c+". Status: "+p.status);var q=Number(p.getResponseHeader("Content-length")),r,l=(r=p.getResponseHeader("Accept-Ranges"))&&"bytes"===r;p=(r=p.getResponseHeader("Content-Encoding"))&&"gzip"===r;var t=1048576;l||(t=q);var v=this;v.Fd(z=>{var H=z*t,F=(z+1)*t-1;F=Math.min(F,q-1);if("undefined"==typeof v.Yb[z]){var la=v.Yb;if(H>F)throw Error("invalid range ("+H+", "+F+") or no bytes requested!");if(F>q-1)throw Error("only "+q+" bytes available! programmer error!");var K=new XMLHttpRequest;K.open("GET",
c,!1);q!==t&&K.setRequestHeader("Range","bytes="+H+"-"+F);K.responseType="arraybuffer";K.overrideMimeType&&K.overrideMimeType("text/plain; charset=x-user-defined");K.send(null);if(!(200<=K.status&&300>K.status||304===K.status))throw Error("Couldn't load "+c+". Status: "+K.status);H=void 0!==K.response?new Uint8Array(K.response||[]):eb(K.responseText||"",!0);la[z]=H}if("undefined"==typeof v.Yb[z])throw Error("doXHR failed!");return v.Yb[z]});if(p||!q)t=q=1,t=q=this.$b(0).length,na("LazyFiles on gzip forces download of the whole file when length is accessed");
this.cd=q;this.bd=t;this.pc=!0};if("undefined"!=typeof XMLHttpRequest){if(!ha)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var g=new f;Object.defineProperties(g,{length:{get:function(){this.pc||this.xc();return this.cd}},zc:{get:function(){this.pc||this.xc();return this.bd}}});var m=void 0}else m=c,g=void 0;var k=cc(a,b,d,e);g?k.kb=g:m&&(k.kb=null,k.url=m);Object.defineProperties(k,{qb:{get:function(){return this.kb.length}}});
var n={};Object.keys(k.nb).forEach(p=>{var q=k.nb[p];n[p]=function(){dc(k);return q.apply(null,arguments)}});n.read=(p,q,r,l,t)=>{dc(k);p=p.node.kb;if(t>=p.length)q=0;else{l=Math.min(p.length-t,l);if(p.slice)for(var v=0;v<l;v++)q[r+v]=p[t+v];else for(v=0;v<l;v++)q[r+v]=p.get(t+v);q=l}return q};n.qc=()=>{dc(k);u();throw new I(48);};k.nb=n;return k},O={},Nb,Vb;function fc(a,b){if("/"===b.charAt(0))return b;a=-100===a?"/":Mb(a).path;if(0==b.length)throw new I(44);return E(a+"/"+b)}var gc=void 0;
function P(){gc+=4;return A[gc-4>>2]}var hc={};function ic(a){for(;a.length;){var b=a.pop();a.pop()(b)}}function jc(a){return this.fromWireType(A[a>>2])}var kc={},lc={},mc={};function nc(a){if(void 0===a)return"_unknown";a=a.replace(/[^a-zA-Z0-9_]/g,"$");var b=a.charCodeAt(0);return 48<=b&&57>=b?`_${a}`:a}function oc(a,b){a=nc(a);return{[a]:function(){return b.apply(this,arguments)}}[a]}
function pc(a){var b=Error,c=oc(a,function(d){this.name=a;this.message=d;d=Error(d).stack;void 0!==d&&(this.stack=this.toString()+"\n"+d.replace(/^Error(:[^\n]*)?\n/,""))});c.prototype=Object.create(b.prototype);c.prototype.constructor=c;c.prototype.toString=function(){return void 0===this.message?this.name:`${this.name}: ${this.message}`};return c}var qc=void 0;function rc(a){throw new qc(a);}
function sc(a,b,c){function d(m){m=c(m);m.length!==a.length&&rc("Mismatched type converter count");for(var k=0;k<a.length;++k)Q(a[k],m[k])}a.forEach(function(m){mc[m]=b});var e=Array(b.length),f=[],g=0;b.forEach((m,k)=>{lc.hasOwnProperty(m)?e[k]=lc[m]:(f.push(m),kc.hasOwnProperty(m)||(kc[m]=[]),kc[m].push(()=>{e[k]=lc[m];++g;g===f.length&&d(e)}))});0===f.length&&d(e)}
function tc(a){switch(a){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${a}`);}}var uc=void 0;function R(a){for(var b="";x[a];)b+=uc[x[a++]];return b}var vc=void 0;function S(a){throw new vc(a);}
function Q(a,b,c={}){if(!("argPackAdvance"in b))throw new TypeError("registerType registeredInstance requires argPackAdvance");var d=b.name;a||S(`type "${d}" must have a positive integer typeid pointer`);if(lc.hasOwnProperty(a)){if(c.td)return;S(`Cannot register type '${d}' twice`)}lc[a]=b;delete mc[a];kc.hasOwnProperty(a)&&(b=kc[a],delete kc[a],b.forEach(e=>e()))}function wc(a){S(a.jb.rb.ob.name+" instance already deleted")}var xc=!1;function yc(){}
function zc(a){--a.count.value;0===a.count.value&&(a.vb?a.yb.Fb(a.vb):a.rb.ob.Fb(a.mb))}function Ac(a,b,c){if(b===c)return a;if(void 0===c.sb)return null;a=Ac(a,b,c.sb);return null===a?null:c.hd(a)}var Bc={},Cc=[];function Dc(){for(;Cc.length;){var a=Cc.pop();a.jb.Ob=!1;a["delete"]()}}var Ec=void 0,Fc={};function Gc(a,b){for(void 0===b&&S("ptr should not be undefined");a.sb;)b=a.Vb(b),a=a.sb;return Fc[b]}
function Hc(a,b){b.rb&&b.mb||rc("makeClassHandle requires ptr and ptrType");!!b.yb!==!!b.vb&&rc("Both smartPtrType and smartPtr must be specified");b.count={value:1};return Ic(Object.create(a,{jb:{value:b}}))}function Ic(a){if("undefined"===typeof FinalizationRegistry)return Ic=b=>b,a;xc=new FinalizationRegistry(b=>{zc(b.jb)});Ic=b=>{var c=b.jb;c.vb&&xc.register(b,{jb:c},b);return b};yc=b=>{xc.unregister(b)};return Ic(a)}function Jc(){}
function Kc(a,b,c){if(void 0===a[b].ub){var d=a[b];a[b]=function(){a[b].ub.hasOwnProperty(arguments.length)||S(`Function '${c}' called with an invalid number of arguments (${arguments.length}) - expects one of (${a[b].ub})!`);return a[b].ub[arguments.length].apply(this,arguments)};a[b].ub=[];a[b].ub[d.Xb]=d}}
function Lc(a,b,c){h.hasOwnProperty(a)?((void 0===c||void 0!==h[a].ub&&void 0!==h[a].ub[c])&&S(`Cannot register public name '${a}' twice`),Kc(h,a,a),h.hasOwnProperty(c)&&S(`Cannot register multiple overloads of a function with the same number of arguments (${c})!`),h[a].ub[c]=b):(h[a]=b,void 0!==c&&(h[a].te=c))}function Mc(a,b,c,d,e,f,g,m){this.name=a;this.constructor=b;this.Ib=c;this.Fb=d;this.sb=e;this.ld=f;this.Vb=g;this.hd=m;this.Cd=[]}
function Nc(a,b,c){for(;b!==c;)b.Vb||S(`Expected null or instance of ${c.name}, got an instance of ${b.name}`),a=b.Vb(a),b=b.sb;return a}function Oc(a,b){if(null===b)return this.oc&&S(`null is not a valid ${this.name}`),0;b.jb||S(`Cannot pass "${Pc(b)}" as a ${this.name}`);b.jb.mb||S(`Cannot pass deleted object as a pointer of type ${this.name}`);return Nc(b.jb.mb,b.jb.rb.ob,this.ob)}
function Qc(a,b){if(null===b){this.oc&&S(`null is not a valid ${this.name}`);if(this.bc){var c=this.sc();null!==a&&a.push(this.Fb,c);return c}return 0}b.jb||S(`Cannot pass "${Pc(b)}" as a ${this.name}`);b.jb.mb||S(`Cannot pass deleted object as a pointer of type ${this.name}`);!this.ac&&b.jb.rb.ac&&S(`Cannot convert argument of type ${b.jb.yb?b.jb.yb.name:b.jb.rb.name} to parameter type ${this.name}`);c=Nc(b.jb.mb,b.jb.rb.ob,this.ob);if(this.bc)switch(void 0===b.jb.vb&&S("Passing raw pointer to smart pointer is illegal"),
this.Md){case 0:b.jb.yb===this?c=b.jb.vb:S(`Cannot convert argument of type ${b.jb.yb?b.jb.yb.name:b.jb.rb.name} to parameter type ${this.name}`);break;case 1:c=b.jb.vb;break;case 2:if(b.jb.yb===this)c=b.jb.vb;else{var d=b.clone();c=this.Dd(c,Rc(function(){d["delete"]()}));null!==a&&a.push(this.Fb,c)}break;default:S("Unsupporting sharing policy")}return c}
function Sc(a,b){if(null===b)return this.oc&&S(`null is not a valid ${this.name}`),0;b.jb||S(`Cannot pass "${Pc(b)}" as a ${this.name}`);b.jb.mb||S(`Cannot pass deleted object as a pointer of type ${this.name}`);b.jb.rb.ac&&S(`Cannot convert argument of type ${b.jb.rb.name} to parameter type ${this.name}`);return Nc(b.jb.mb,b.jb.rb.ob,this.ob)}
function Tc(a,b,c,d){this.name=a;this.ob=b;this.oc=c;this.ac=d;this.bc=!1;this.Fb=this.Dd=this.sc=this.Mc=this.Md=this.Bd=void 0;void 0!==b.sb?this.toWireType=Qc:(this.toWireType=d?Oc:Sc,this.xb=null)}function Uc(a,b,c){h.hasOwnProperty(a)||rc("Replacing nonexistant public symbol");void 0!==h[a].ub&&void 0!==c?h[a].ub[c]=b:(h[a]=b,h[a].Xb=c)}
var Vc=(a,b)=>{var c=[];return function(){c.length=0;Object.assign(c,arguments);if(a.includes("j")){var d=h["dynCall_"+a];d=c&&c.length?d.apply(null,[b].concat(c)):d.call(null,b)}else d=C.get(b).apply(null,c);return d}};function T(a,b){a=R(a);var c=a.includes("j")?Vc(a,b):C.get(b);"function"!=typeof c&&S(`unknown function pointer with signature ${a}: ${b}`);return c}var Wc=void 0;function Xc(a){a=Yc(a);var b=R(a);U(a);return b}
function Zc(a,b){function c(f){e[f]||lc[f]||(mc[f]?mc[f].forEach(c):(d.push(f),e[f]=!0))}var d=[],e={};b.forEach(c);throw new Wc(`${a}: `+d.map(Xc).join([", "]));}function $c(a,b){for(var c=[],d=0;d<a;d++)c.push(B[b+4*d>>2]);return c}
function ad(a){var b=Function;if(!(b instanceof Function))throw new TypeError(`new_ called with constructor type ${typeof b} which is not a function`);var c=oc(b.name||"unknownFunctionName",function(){});c.prototype=b.prototype;c=new c;a=b.apply(c,a);return a instanceof Object?a:c}
function bd(a,b,c,d,e,f){var g=b.length;2>g&&S("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var m=null!==b[1]&&null!==c,k=!1,n=1;n<b.length;++n)if(null!==b[n]&&void 0===b[n].xb){k=!0;break}var p="void"!==b[0].name,q="",r="";for(n=0;n<g-2;++n)q+=(0!==n?", ":"")+"arg"+n,r+=(0!==n?", ":"")+"arg"+n+"Wired";q=`\n        return function ${nc(a)}(${q}) {\n        if (arguments.length !== ${g-2}) {\n          throwBindingError('function ${a} called with ${arguments.length} arguments, expected ${g-
2} args!');\n        }`;k&&(q+="var destructors = [];\n");var l=k?"destructors":"null",t="throwBindingError invoker fn runDestructors retType classParam".split(" "),v=[S,d,e,ic,b[0],b[1]];m&&(q+="var thisWired = classParam.toWireType("+l+", this);\n");for(n=0;n<g-2;++n)q+="var arg"+n+"Wired = argType"+n+".toWireType("+l+", arg"+n+"); // "+b[n+2].name+"\n",t.push("argType"+n),v.push(b[n+2]);m&&(r="thisWired"+(0<r.length?", ":"")+r);q+=(p||f?"var rv = ":"")+"invoker(fn"+(0<r.length?", ":"")+r+");\n";
if(k)q+="runDestructors(destructors);\n";else for(n=m?1:2;n<b.length;++n)g=1===n?"thisWired":"arg"+(n-2)+"Wired",null!==b[n].xb&&(q+=g+"_dtor("+g+"); // "+b[n].name+"\n",t.push(g+"_dtor"),v.push(b[n].xb));p&&(q+="var ret = retType.fromWireType(rv);\nreturn ret;\n");t.push(q+"}\n");return ad(t).apply(null,v)}
function cd(a,b,c){a instanceof Object||S(`${c} with invalid "this": ${a}`);a instanceof b.ob.constructor||S(`${c} incompatible with "this" of type ${a.constructor.name}`);a.jb.mb||S(`cannot call emscripten binding method ${c} on deleted object`);return Nc(a.jb.mb,a.jb.rb.ob,b.ob)}
var V=new function(){this.zb=[void 0];this.Ec=[];this.get=function(a){return this.zb[a]};this.has=function(a){return void 0!==this.zb[a]};this.Wb=function(a){var b=this.Ec.pop()||this.zb.length;this.zb[b]=a;return b};this.kd=function(a){this.zb[a]=void 0;this.Ec.push(a)}};function dd(a){a>=V.Qc&&0===--V.get(a).Oc&&V.kd(a)}
var ed=a=>{a||S("Cannot use deleted val. handle = "+a);return V.get(a).value},Rc=a=>{switch(a){case void 0:return 1;case null:return 2;case !0:return 3;case !1:return 4;default:return V.Wb({Oc:1,value:a})}};function fd(a,b,c){switch(b){case 0:return function(d){return this.fromWireType((c?w:x)[d])};case 1:return function(d){return this.fromWireType((c?y:ra)[d>>1])};case 2:return function(d){return this.fromWireType((c?A:B)[d>>2])};default:throw new TypeError("Unknown integer type: "+a);}}
function gd(a,b){var c=lc[a];void 0===c&&S(b+" has unknown type "+Xc(a));return c}function Pc(a){if(null===a)return"null";var b=typeof a;return"object"===b||"array"===b||"function"===b?a.toString():""+a}function hd(a,b){switch(b){case 2:return function(c){return this.fromWireType(sa[c>>2])};case 3:return function(c){return this.fromWireType(ta[c>>3])};default:throw new TypeError("Unknown float type: "+a);}}
function jd(a,b,c){switch(b){case 0:return c?function(d){return w[d]}:function(d){return x[d]};case 1:return c?function(d){return y[d>>1]}:function(d){return ra[d>>1]};case 2:return c?function(d){return A[d>>2]}:function(d){return B[d>>2]};default:throw new TypeError("Unknown integer type: "+a);}}
var kd="undefined"!=typeof TextDecoder?new TextDecoder("utf-16le"):void 0,ld=(a,b)=>{var c=a>>1;for(var d=c+b/2;!(c>=d)&&ra[c];)++c;c<<=1;if(32<c-a&&kd)return kd.decode(x.subarray(a,c));c="";for(d=0;!(d>=b/2);++d){var e=y[a+2*d>>1];if(0==e)break;c+=String.fromCharCode(e)}return c},md=(a,b,c)=>{void 0===c&&(c=2147483647);if(2>c)return 0;c-=2;var d=b;c=c<2*a.length?c/2:a.length;for(var e=0;e<c;++e)y[b>>1]=a.charCodeAt(e),b+=2;y[b>>1]=0;return b-d},nd=a=>2*a.length,od=(a,b)=>{for(var c=0,d="";!(c>=b/
4);){var e=A[a+4*c>>2];if(0==e)break;++c;65536<=e?(e-=65536,d+=String.fromCharCode(55296|e>>10,56320|e&1023)):d+=String.fromCharCode(e)}return d},pd=(a,b,c)=>{void 0===c&&(c=2147483647);if(4>c)return 0;var d=b;c=d+c-4;for(var e=0;e<a.length;++e){var f=a.charCodeAt(e);if(55296<=f&&57343>=f){var g=a.charCodeAt(++e);f=65536+((f&1023)<<10)|g&1023}A[b>>2]=f;b+=4;if(b+4>c)break}A[b>>2]=0;return b-d},qd=a=>{for(var b=0,c=0;c<a.length;++c){var d=a.charCodeAt(c);55296<=d&&57343>=d&&++c;b+=4}return b},rd=a=>
0===a%4&&(0!==a%100||0===a%400),sd=[0,31,60,91,121,152,182,213,244,274,305,335],td=[0,31,59,90,120,151,181,212,243,273,304,334],vd=a=>{var b=cb(a)+1,c=ud(b);c&&db(a,x,c,b);return c},W=a=>{a=eval(a?G(x,a):"");if(null==a)return 0;a+="";var b=cb(a);if(!W.bufferSize||W.bufferSize<b+1)W.bufferSize&&U(W.buffer),W.bufferSize=b+1,W.buffer=ud(W.bufferSize);db(a,x,W.buffer,W.bufferSize);return W.buffer},wd={},yd=()=>{if(!xd){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==
typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:da||"./this.program"},b;for(b in wd)void 0===wd[b]?delete a[b]:a[b]=wd[b];var c=[];for(b in a)c.push(`${b}=${a[b]}`);xd=c}return xd},xd,zd=a=>{if(!noExitRuntime){if(h.onExit)h.onExit(a);qa=!0}ea(a,new Oa(a))},Ad=(a,b)=>{for(var c=0,d=0;d<=b;c+=a[d++]);return c},Bd=[31,29,31,30,31,30,31,31,30,31,30,31],Cd=[31,28,31,30,31,30,31,31,30,31,30,31],Dd=(a,b)=>{for(a=new Date(a.getTime());0<b;){var c=a.getMonth(),
d=(rd(a.getFullYear())?Bd:Cd)[c];if(b>d-a.getDate())b-=d-a.getDate()+1,a.setDate(1),11>c?a.setMonth(c+1):(a.setMonth(0),a.setFullYear(a.getFullYear()+1));else{a.setDate(a.getDate()+b);break}}return a},Ed=(a,b,c,d)=>{function e(l,t,v){for(l="number"==typeof l?l.toString():l||"";l.length<t;)l=v[0]+l;return l}function f(l,t){return e(l,t,"0")}function g(l,t){function v(H){return 0>H?-1:0<H?1:0}var z;0===(z=v(l.getFullYear()-t.getFullYear()))&&0===(z=v(l.getMonth()-t.getMonth()))&&(z=v(l.getDate()-t.getDate()));
return z}function m(l){switch(l.getDay()){case 0:return new Date(l.getFullYear()-1,11,29);case 1:return l;case 2:return new Date(l.getFullYear(),0,3);case 3:return new Date(l.getFullYear(),0,2);case 4:return new Date(l.getFullYear(),0,1);case 5:return new Date(l.getFullYear()-1,11,31);case 6:return new Date(l.getFullYear()-1,11,30)}}function k(l){l=Dd(new Date(l.Mb+1900,0,1),l.Lb);var t=new Date(l.getFullYear()+1,0,4),v=m(new Date(l.getFullYear(),0,4));t=m(t);return 0>=g(v,l)?0>=g(t,l)?l.getFullYear()+
1:l.getFullYear():l.getFullYear()-1}var n=A[d+40>>2];d={Pd:A[d>>2],Od:A[d+4>>2],ic:A[d+8>>2],vc:A[d+12>>2],jc:A[d+16>>2],Mb:A[d+20>>2],Cb:A[d+24>>2],Lb:A[d+28>>2],ve:A[d+32>>2],Nd:A[d+36>>2],Qd:n?n?G(x,n):"":""};c=c?G(x,c):"";n={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I",
"%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var p in n)c=c.replace(new RegExp(p,"g"),n[p]);var q="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),r="January February March April May June July August September October November December".split(" ");n={"%a":l=>q[l.Cb].substring(0,3),"%A":l=>q[l.Cb],"%b":l=>r[l.jc].substring(0,3),"%B":l=>r[l.jc],"%C":l=>f((l.Mb+1900)/100|0,2),"%d":l=>f(l.vc,2),"%e":l=>e(l.vc,2," "),"%g":l=>
k(l).toString().substring(2),"%G":l=>k(l),"%H":l=>f(l.ic,2),"%I":l=>{l=l.ic;0==l?l=12:12<l&&(l-=12);return f(l,2)},"%j":l=>f(l.vc+Ad(rd(l.Mb+1900)?Bd:Cd,l.jc-1),3),"%m":l=>f(l.jc+1,2),"%M":l=>f(l.Od,2),"%n":()=>"\n","%p":l=>0<=l.ic&&12>l.ic?"AM":"PM","%S":l=>f(l.Pd,2),"%t":()=>"\t","%u":l=>l.Cb||7,"%U":l=>f(Math.floor((l.Lb+7-l.Cb)/7),2),"%V":l=>{var t=Math.floor((l.Lb+7-(l.Cb+6)%7)/7);2>=(l.Cb+371-l.Lb-2)%7&&t++;if(t)53==t&&(v=(l.Cb+371-l.Lb)%7,4==v||3==v&&rd(l.Mb)||(t=1));else{t=52;var v=(l.Cb+
7-l.Lb-1)%7;(4==v||5==v&&rd(l.Mb%400-1))&&t++}return f(t,2)},"%w":l=>l.Cb,"%W":l=>f(Math.floor((l.Lb+7-(l.Cb+6)%7)/7),2),"%y":l=>(l.Mb+1900).toString().substring(2),"%Y":l=>l.Mb+1900,"%z":l=>{l=l.Nd;var t=0<=l;l=Math.abs(l)/60;return(t?"+":"-")+String("0000"+(l/60*100+l%60)).slice(-4)},"%Z":l=>l.Qd,"%%":()=>"%"};c=c.replace(/%%/g,"\x00\x00");for(p in n)c.includes(p)&&(c=c.replace(new RegExp(p,"g"),n[p](d)));c=c.replace(/\0\0/g,"%");p=eb(c,!1);if(p.length>b)return 0;w.set(p,a);return p.length-1};
function Hb(a,b,c,d){a||(a=this);this.parent=a;this.Db=a.Db;this.Pb=null;this.id=zb++;this.name=b;this.mode=c;this.lb={};this.nb={};this.dc=d}Object.defineProperties(Hb.prototype,{read:{get:function(){return 365===(this.mode&365)},set:function(a){a?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146===(this.mode&146)},set:function(a){a?this.mode|=146:this.mode&=-147}},zd:{get:function(){return 16384===(this.mode&61440)}},yd:{get:function(){return 8192===(this.mode&61440)}}});$b();
Ab=Array(4096);Pb(J,"/");M("/tmp",16895,0);M("/home",16895,0);M("/home/<USER>",16895,0);(()=>{M("/dev",16895,0);ib(259,{read:()=>0,write:(d,e,f,g)=>g});Qb("/dev/null",259);hb(1280,kb);hb(1536,lb);Qb("/dev/tty",1280);Qb("/dev/tty1",1536);var a=new Uint8Array(1024),b=0,c=()=>{0===b&&(b=ab(a).byteLength);return a[--b]};N("/dev","random",c);N("/dev","urandom",c);M("/dev/shm",16895,0);M("/dev/shm/tmp",16895,0)})();
(()=>{M("/proc",16895,0);var a=M("/proc/self",16895,0);M("/proc/self/fd",16895,0);Pb({Db:()=>{var b=nb(a,"fd",16895,73);b.lb={Sb:(c,d)=>{var e=Mb(+d);c={parent:null,Db:{Jc:"fake"},lb:{Ub:()=>e.path}};return c.parent=c}};return b}},"/proc/self/fd")})();h.FS_createPath=bc;h.FS_createDataFile=ub;h.FS_createPreloadedFile=tb;h.FS_unlink=Tb;h.FS_createLazyFile=ec;h.FS_createDevice=N;qc=h.InternalError=pc("InternalError");for(var Fd=Array(256),Gd=0;256>Gd;++Gd)Fd[Gd]=String.fromCharCode(Gd);uc=Fd;
vc=h.BindingError=pc("BindingError");Jc.prototype.isAliasOf=function(a){if(!(this instanceof Jc&&a instanceof Jc))return!1;var b=this.jb.rb.ob,c=this.jb.mb,d=a.jb.rb.ob;for(a=a.jb.mb;b.sb;)c=b.Vb(c),b=b.sb;for(;d.sb;)a=d.Vb(a),d=d.sb;return b===d&&c===a};
Jc.prototype.clone=function(){this.jb.mb||wc(this);if(this.jb.Tb)return this.jb.count.value+=1,this;var a=Ic,b=Object,c=b.create,d=Object.getPrototypeOf(this),e=this.jb;a=a(c.call(b,d,{jb:{value:{count:e.count,Ob:e.Ob,Tb:e.Tb,mb:e.mb,rb:e.rb,vb:e.vb,yb:e.yb}}}));a.jb.count.value+=1;a.jb.Ob=!1;return a};Jc.prototype["delete"]=function(){this.jb.mb||wc(this);this.jb.Ob&&!this.jb.Tb&&S("Object already scheduled for deletion");yc(this);zc(this.jb);this.jb.Tb||(this.jb.vb=void 0,this.jb.mb=void 0)};
Jc.prototype.isDeleted=function(){return!this.jb.mb};Jc.prototype.deleteLater=function(){this.jb.mb||wc(this);this.jb.Ob&&!this.jb.Tb&&S("Object already scheduled for deletion");Cc.push(this);1===Cc.length&&Ec&&Ec(Dc);this.jb.Ob=!0;return this};h.getInheritedInstanceCount=function(){return Object.keys(Fc).length};h.getLiveInheritedInstances=function(){var a=[],b;for(b in Fc)Fc.hasOwnProperty(b)&&a.push(Fc[b]);return a};h.flushPendingDeletes=Dc;h.setDelayFunction=function(a){Ec=a;Cc.length&&Ec&&Ec(Dc)};
Tc.prototype.md=function(a){this.Mc&&(a=this.Mc(a));return a};Tc.prototype.Ac=function(a){this.Fb&&this.Fb(a)};Tc.prototype.argPackAdvance=8;Tc.prototype.readValueFromPointer=jc;Tc.prototype.deleteObject=function(a){if(null!==a)a["delete"]()};
Tc.prototype.fromWireType=function(a){function b(){return this.bc?Hc(this.ob.Ib,{rb:this.Bd,mb:c,yb:this,vb:a}):Hc(this.ob.Ib,{rb:this,mb:a})}var c=this.md(a);if(!c)return this.Ac(a),null;var d=Gc(this.ob,c);if(void 0!==d){if(0===d.jb.count.value)return d.jb.mb=c,d.jb.vb=a,d.clone();d=d.clone();this.Ac(a);return d}d=this.ob.ld(c);d=Bc[d];if(!d)return b.call(this);d=this.ac?d.gd:d.pointerType;var e=Ac(c,this.ob,d.ob);return null===e?b.call(this):this.bc?Hc(d.ob.Ib,{rb:d,mb:e,yb:this,vb:a}):Hc(d.ob.Ib,
{rb:d,mb:e})};Wc=h.UnboundTypeError=pc("UnboundTypeError");V.zb.push({value:void 0},{value:null},{value:!0},{value:!1});V.Qc=V.zb.length;h.count_emval_handles=function(){for(var a=0,b=V.Qc;b<V.zb.length;++b)void 0!==V.zb[b]&&++a;return a};
var oe={q:function(a){a=new Sa(a);a.od()||(a.Tc(!0),Ra--);a.Uc(!1);Qa.push(a);Hd(a.Rb);return a.pd()},t:function(){X(0);var a=Qa.pop();Id(a.Rb);D=0},b:Ua,g:Ua,ba:function(){var a=Qa.pop();a||u("no exception to throw");var b=a.Rb;a.qd()||(Qa.push(a),a.Uc(!0),a.Tc(!1),Ra++);D=b;throw D;},a:function(a,b,c){(new Sa(a)).Hc(b,c);D=a;Ra++;throw D;},Ia:function(){return Ra},e:function(a){D||(D=a);throw D;},aa:function(a,b,c){gc=c;try{var d=Mb(a);switch(b){case 0:var e=P();return 0>e?-28:Ob(d,e).Gb;case 1:case 2:return 0;
case 3:return d.flags;case 4:return e=P(),d.flags|=e,0;case 5:return e=P(),y[e+0>>1]=2,0;case 6:case 7:return 0;case 16:case 8:return-28;case 9:return A[Jd()>>2]=28,-1;default:return-28}}catch(f){if("undefined"==typeof O||"ErrnoError"!==f.name)throw f;return-f.Ab}},Ha:function(a,b,c){gc=c;try{var d=Mb(a);switch(b){case 21509:return d.pb?0:-59;case 21505:if(!d.pb)return-59;if(d.pb.Eb.vd){b=[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];var e=P();A[e>>2]=25856;A[e+4>>2]=
5;A[e+8>>2]=191;A[e+12>>2]=35387;for(var f=0;32>f;f++)w[e+f+17>>0]=b[f]||0}return 0;case 21510:case 21511:case 21512:return d.pb?0:-59;case 21506:case 21507:case 21508:if(!d.pb)return-59;if(d.pb.Eb.wd)for(e=P(),b=[],f=0;32>f;f++)b.push(w[e+f+17>>0]);return 0;case 21519:if(!d.pb)return-59;e=P();return A[e>>2]=0;case 21520:return d.pb?-28:-59;case 21531:e=P();if(!d.nb.ud)throw new I(59);return d.nb.ud(d,b,e);case 21523:if(!d.pb)return-59;d.pb.Eb.xd&&(f=[24,80],e=P(),y[e>>1]=f[0],y[e+2>>1]=f[1]);return 0;
case 21524:return d.pb?0:-59;case 21515:return d.pb?0:-59;default:return-28}}catch(g){if("undefined"==typeof O||"ErrnoError"!==g.name)throw g;return-g.Ab}},$:function(a,b,c,d){gc=d;try{b=b?G(x,b):"";b=fc(a,b);var e=d?P():0;return Wb(b,c,e).Gb}catch(f){if("undefined"==typeof O||"ErrnoError"!==f.name)throw f;return-f.Ab}},Ga:function(a){try{return a=a?G(x,a):"",Sb(a),0}catch(b){if("undefined"==typeof O||"ErrnoError"!==b.name)throw b;return-b.Ab}},Fa:function(a,b,c){try{return b=b?G(x,b):"",b=fc(a,b),
0===c?Tb(b):512===c?Sb(b):u("Invalid flags passed to unlinkat"),0}catch(d){if("undefined"==typeof O||"ErrnoError"!==d.name)throw d;return-d.Ab}},I:function(a){var b=hc[a];delete hc[a];var c=b.sc,d=b.Fb,e=b.Cc,f=e.map(g=>g.sd).concat(e.map(g=>g.Kd));sc([a],f,g=>{var m={};e.forEach((k,n)=>{var p=g[n],q=k.$b,r=k.rd,l=g[n+e.length],t=k.Jd,v=k.Ld;m[k.jd]={read:z=>p.fromWireType(q(r,z)),write:(z,H)=>{var F=[];t(v,z,l.toWireType(F,H));ic(F)}}});return[{name:b.name,fromWireType:function(k){var n={},p;for(p in m)n[p]=
m[p].read(k);d(k);return n},toWireType:function(k,n){for(var p in m)if(!(p in n))throw new TypeError(`Missing field: "${p}"`);var q=c();for(p in m)m[p].write(q,n[p]);null!==k&&k.push(d,q);return q},argPackAdvance:8,readValueFromPointer:jc,xb:d}]})},ia:function(){},Ca:function(a,b,c,d,e){var f=tc(c);b=R(b);Q(a,{name:b,fromWireType:function(g){return!!g},toWireType:function(g,m){return m?d:e},argPackAdvance:8,readValueFromPointer:function(g){if(1===c)var m=w;else if(2===c)m=y;else if(4===c)m=A;else throw new TypeError("Unknown boolean type size: "+
b);return this.fromWireType(m[g>>f])},xb:null})},u:function(a,b,c,d,e,f,g,m,k,n,p,q,r){p=R(p);f=T(e,f);m&&(m=T(g,m));n&&(n=T(k,n));r=T(q,r);var l=nc(p);Lc(l,function(){Zc(`Cannot construct ${p} due to unbound types`,[d])});sc([a,b,c],d?[d]:[],function(t){t=t[0];if(d){var v=t.ob;var z=v.Ib}else z=Jc.prototype;t=oc(l,function(){if(Object.getPrototypeOf(this)!==H)throw new vc("Use 'new' to construct "+p);if(void 0===F.Jb)throw new vc(p+" has no accessible constructor");var K=F.Jb[arguments.length];if(void 0===
K)throw new vc(`Tried to invoke ctor of ${p} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(F.Jb).toString()}) parameters instead!`);return K.apply(this,arguments)});var H=Object.create(z,{constructor:{value:t}});t.prototype=H;var F=new Mc(p,t,H,r,v,f,m,n);F.sb&&(void 0===F.sb.wc&&(F.sb.wc=[]),F.sb.wc.push(F));v=new Tc(p,F,!0,!1);z=new Tc(p+"*",F,!1,!1);var la=new Tc(p+" const*",F,!1,!0);Bc[a]={pointerType:z,gd:la};Uc(l,t);return[v,z,la]})},E:function(a,b,c,d,e,
f){0<b||u();var g=$c(b,c);e=T(d,e);sc([],[a],function(m){m=m[0];var k=`constructor ${m.name}`;void 0===m.ob.Jb&&(m.ob.Jb=[]);if(void 0!==m.ob.Jb[b-1])throw new vc(`Cannot register multiple constructors with identical number of parameters (${b-1}) for class '${m.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);m.ob.Jb[b-1]=()=>{Zc(`Cannot construct ${m.name} due to unbound types`,g)};sc([],g,function(n){n.splice(1,0,null);m.ob.Jb[b-1]=bd(k,
n,null,e,f);return[]});return[]})},m:function(a,b,c,d,e,f,g,m,k){var n=$c(c,d);b=R(b);f=T(e,f);sc([],[a],function(p){function q(){Zc(`Cannot call ${r} due to unbound types`,n)}p=p[0];var r=`${p.name}.${b}`;b.startsWith("@@")&&(b=Symbol[b.substring(2)]);m&&p.ob.Cd.push(b);var l=p.ob.Ib,t=l[b];void 0===t||void 0===t.ub&&t.className!==p.name&&t.Xb===c-2?(q.Xb=c-2,q.className=p.name,l[b]=q):(Kc(l,b,r),l[b].ub[c-2]=q);sc([],n,function(v){v=bd(r,v,p,f,g,k);void 0===l[b].ub?(v.Xb=c-2,l[b]=v):l[b].ub[c-2]=
v;return[]});return[]})},j:function(a,b,c,d,e,f,g,m,k,n){b=R(b);e=T(d,e);sc([],[a],function(p){p=p[0];var q=`${p.name}.${b}`,r={get:function(){Zc(`Cannot access ${q} due to unbound types`,[c,g])},enumerable:!0,configurable:!0};r.set=k?()=>{Zc(`Cannot access ${q} due to unbound types`,[c,g])}:()=>{S(q+" is a read-only property")};Object.defineProperty(p.ob.Ib,b,r);sc([],k?[c,g]:[c],function(l){var t=l[0],v={get:function(){var H=cd(this,p,q+" getter");return t.fromWireType(e(f,H))},enumerable:!0};if(k){k=
T(m,k);var z=l[1];v.set=function(H){var F=cd(this,p,q+" setter"),la=[];k(n,F,z.toWireType(la,H));ic(la)}}Object.defineProperty(p.ob.Ib,b,v);return[]});return[]})},Ba:function(a,b){b=R(b);Q(a,{name:b,fromWireType:function(c){var d=ed(c);dd(c);return d},toWireType:function(c,d){return Rc(d)},argPackAdvance:8,readValueFromPointer:jc,xb:null})},D:function(a,b,c,d){function e(){}c=tc(c);b=R(b);e.values={};Q(a,{name:b,constructor:e,fromWireType:function(f){return this.constructor.values[f]},toWireType:function(f,
g){return g.value},argPackAdvance:8,readValueFromPointer:fd(b,c,d),xb:null});Lc(b,e)},l:function(a,b,c){var d=gd(a,"enum");b=R(b);a=d.constructor;d=Object.create(d.constructor.prototype,{value:{value:c},constructor:{value:oc(`${d.name}_${b}`,function(){})}});a.values[c]=d;a[b]=d},Z:function(a,b,c){c=tc(c);b=R(b);Q(a,{name:b,fromWireType:function(d){return d},toWireType:function(d,e){return e},argPackAdvance:8,readValueFromPointer:hd(b,c),xb:null})},p:function(a,b,c,d,e,f,g){var m=$c(b,c);a=R(a);e=
T(d,e);Lc(a,function(){Zc(`Cannot call ${a} due to unbound types`,m)},b-1);sc([],m,function(k){Uc(a,bd(a,[k[0],null].concat(k.slice(1)),null,e,f,g),b-1);return[]})},v:function(a,b,c,d,e){b=R(b);-1===e&&(e=4294967295);e=tc(c);var f=m=>m;if(0===d){var g=32-8*c;f=m=>m<<g>>>g}c=b.includes("unsigned")?function(m,k){return k>>>0}:function(m,k){return k};Q(a,{name:b,fromWireType:f,toWireType:c,argPackAdvance:8,readValueFromPointer:jd(b,e,0!==d),xb:null})},s:function(a,b,c){function d(f){f>>=2;var g=B;return new e(g.buffer,
g[f+1],g[f])}var e=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][b];c=R(c);Q(a,{name:c,fromWireType:d,argPackAdvance:8,readValueFromPointer:d},{td:!0})},Y:function(a,b){b=R(b);var c="std::string"===b;Q(a,{name:b,fromWireType:function(d){var e=B[d>>2],f=d+4;if(c)for(var g=f,m=0;m<=e;++m){var k=f+m;if(m==e||0==x[k]){g=g?G(x,g,k-g):"";if(void 0===n)var n=g;else n+=String.fromCharCode(0),n+=g;g=k+1}}else{n=Array(e);for(m=0;m<e;++m)n[m]=String.fromCharCode(x[f+
m]);n=n.join("")}U(d);return n},toWireType:function(d,e){e instanceof ArrayBuffer&&(e=new Uint8Array(e));var f="string"==typeof e;f||e instanceof Uint8Array||e instanceof Uint8ClampedArray||e instanceof Int8Array||S("Cannot pass non-string to std::string");var g=c&&f?cb(e):e.length;var m=ud(4+g+1),k=m+4;B[m>>2]=g;if(c&&f)db(e,x,k,g+1);else if(f)for(f=0;f<g;++f){var n=e.charCodeAt(f);255<n&&(U(k),S("String has UTF-16 code units that do not fit in 8 bits"));x[k+f]=n}else for(f=0;f<g;++f)x[k+f]=e[f];
null!==d&&d.push(U,m);return m},argPackAdvance:8,readValueFromPointer:jc,xb:function(d){U(d)}})},Q:function(a,b,c){c=R(c);if(2===b){var d=ld;var e=md;var f=nd;var g=()=>ra;var m=1}else 4===b&&(d=od,e=pd,f=qd,g=()=>B,m=2);Q(a,{name:c,fromWireType:function(k){for(var n=B[k>>2],p=g(),q,r=k+4,l=0;l<=n;++l){var t=k+4+l*b;if(l==n||0==p[t>>m])r=d(r,t-r),void 0===q?q=r:(q+=String.fromCharCode(0),q+=r),r=t+b}U(k);return q},toWireType:function(k,n){"string"!=typeof n&&S(`Cannot pass non-string to C++ string type ${c}`);
var p=f(n),q=ud(4+p+b);B[q>>2]=p>>m;e(n,q+4,p+b);null!==k&&k.push(U,q);return q},argPackAdvance:8,readValueFromPointer:jc,xb:function(k){U(k)}})},H:function(a,b,c,d,e,f){hc[a]={name:R(b),sc:T(c,d),Fb:T(e,f),Cc:[]}},C:function(a,b,c,d,e,f,g,m,k,n){hc[a].Cc.push({jd:R(b),sd:c,$b:T(d,e),rd:f,Kd:g,Jd:T(m,k),Ld:n})},Aa:function(a,b){b=R(b);Q(a,{qe:!0,name:b,argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},za:()=>!0,ya:()=>{throw Infinity;},B:dd,P:function(a){4<a&&(V.get(a).Oc+=1)},
X:function(){return Rc([])},xa:function(a,b,c){a=ed(a);b=ed(b);c=ed(c);a[b]=c},G:function(a,b){a=gd(a,"_emval_take_value");a=a.readValueFromPointer(b);return Rc(a)},wa:a=>{var b=new Date(A[a+20>>2]+1900,A[a+16>>2],A[a+12>>2],A[a+8>>2],A[a+4>>2],A[a>>2],0),c=A[a+32>>2],d=b.getTimezoneOffset(),e=(new Date(b.getFullYear(),6,1)).getTimezoneOffset(),f=(new Date(b.getFullYear(),0,1)).getTimezoneOffset(),g=Math.min(f,e);0>c?A[a+32>>2]=Number(e!=f&&g==d):0<c!=(g==d)&&(e=Math.max(f,e),b.setTime(b.getTime()+
6E4*((0<c?g:e)-d)));A[a+24>>2]=b.getDay();A[a+28>>2]=(rd(b.getFullYear())?sd:td)[b.getMonth()]+b.getDate()-1|0;A[a>>2]=b.getSeconds();A[a+4>>2]=b.getMinutes();A[a+8>>2]=b.getHours();A[a+12>>2]=b.getDate();A[a+16>>2]=b.getMonth();A[a+20>>2]=b.getYear();return b.getTime()/1E3|0},va:(a,b,c)=>{function d(k){return(k=k.toTimeString().match(/\(([A-Za-z ]+)\)$/))?k[1]:"GMT"}var e=(new Date).getFullYear(),f=new Date(e,0,1),g=new Date(e,6,1);e=f.getTimezoneOffset();var m=g.getTimezoneOffset();B[a>>2]=60*Math.max(e,
m);A[b>>2]=Number(e!=m);a=d(f);b=d(g);a=vd(a);b=vd(b);m<e?(B[c>>2]=a,B[c+4>>2]=b):(B[c>>2]=b,B[c+4>>2]=a)},A:()=>{u("")},O:function(){return Date.now()},ua:()=>2147483648,W:()=>performance.now(),ta:a=>{var b=x.length;a>>>=0;if(2147483648<a)return!1;for(var c=1;4>=c;c*=2){var d=b*(1+.2/c);d=Math.min(d,a+100663296);var e=Math;d=Math.max(a,d);a:{e=e.min.call(e,2147483648,d+(65536-d%65536)%65536)-pa.buffer.byteLength+65535>>>16;try{pa.grow(e);ua();var f=1;break a}catch(g){}f=void 0}if(f)return!0}return!1},
sa:W,Ea:(a,b)=>{var c=0;yd().forEach(function(d,e){var f=b+c;e=B[a+4*e>>2]=f;for(f=0;f<d.length;++f)w[e++>>0]=d.charCodeAt(f);w[e>>0]=0;c+=d.length+1});return 0},Da:(a,b)=>{var c=yd();B[a>>2]=c.length;var d=0;c.forEach(function(e){d+=e.length+1});B[b>>2]=d;return 0},ra:zd,S:function(a){try{var b=Mb(a);Xb(b);return 0}catch(c){if("undefined"==typeof O||"ErrnoError"!==c.name)throw c;return c.Ab}},_:function(a,b,c,d){try{a:{var e=Mb(a);a=b;for(var f,g=b=0;g<c;g++){var m=B[a>>2],k=B[a+4>>2];a+=8;var n=
e,p=m,q=k,r=f,l=w;if(0>q||0>r)throw new I(28);if(null===n.Gb)throw new I(8);if(1===(n.flags&2097155))throw new I(8);if(16384===(n.node.mode&61440))throw new I(31);if(!n.nb.read)throw new I(28);var t="undefined"!=typeof r;if(!t)r=n.position;else if(!n.seekable)throw new I(70);var v=n.nb.read(n,l,p,q,r);t||(n.position+=v);var z=v;if(0>z){var H=-1;break a}b+=z;if(z<k)break;"undefined"!==typeof f&&(f+=z)}H=b}B[d>>2]=H;return 0}catch(F){if("undefined"==typeof O||"ErrnoError"!==F.name)throw F;return F.Ab}},
ja:function(a,b,c,d,e){try{b=c+2097152>>>0<4194305-!!b?(b>>>0)+4294967296*c:NaN;if(isNaN(b))return 61;var f=Mb(a);Yb(f,b,d);Na=[f.position>>>0,(Ma=f.position,1<=+Math.abs(Ma)?0<Ma?+Math.floor(Ma/4294967296)>>>0:~~+Math.ceil((Ma-+(~~Ma>>>0))/4294967296)>>>0:0)];A[e>>2]=Na[0];A[e+4>>2]=Na[1];f.nc&&0===b&&0===d&&(f.nc=null);return 0}catch(g){if("undefined"==typeof O||"ErrnoError"!==g.name)throw g;return g.Ab}},R:function(a,b,c,d){try{a:{var e=Mb(a);a=b;for(var f,g=b=0;g<c;g++){var m=B[a>>2],k=B[a+4>>
2];a+=8;var n=Zb(e,w,m,k,f);if(0>n){var p=-1;break a}b+=n;"undefined"!==typeof f&&(f+=n)}p=b}B[d>>2]=p;return 0}catch(q){if("undefined"==typeof O||"ErrnoError"!==q.name)throw q;return q.Ab}},N:(a,b)=>{ab(x.subarray(a,a+b));return 0},M:Kd,V:Ld,r:Md,c:Nd,f:Od,o:Pd,k:Qd,U:Rd,z:Sd,x:Td,T:Ud,qa:Vd,L:Wd,ha:Xd,ga:Yd,fa:Zd,h:$d,i:ae,d:be,pa:ce,oa:de,na:ee,n:fe,F:ge,K:he,ma:ie,w:je,la:ke,y:le,J:me,ea:ne,ka:function(){var a=location.hostname,b=cb(a)+1,c=ud(b);db(a,x,c,b+1);return c},da:(a,b,c,d)=>Ed(a,b,c,
d),ca:(a,b,c)=>{function d(){function r(l,t,v){return"number"!=typeof l||isNaN(l)?t:l>=t?l<=v?l:v:t}return{year:r(A[c+20>>2]+1900,1970,9999),month:r(A[c+16>>2],0,11),day:r(A[c+12>>2],1,31),hour:r(A[c+8>>2],0,23),min:r(A[c+4>>2],0,59),Rc:r(A[c>>2],0,59)}}for(var e=b?G(x,b):"",f=0;25>f;++f)e=e.replace(new RegExp("\\"+"\\!@#$^&*()+=-[]/{}|:<>?,."[f],"g"),"\\"+"\\!@#$^&*()+=-[]/{}|:<>?,."[f]);b={"%A":"%a","%B":"%b","%c":"%a %b %d %H:%M:%S %Y","%D":"%m\\/%d\\/%y","%e":"%d","%F":"%Y-%m-%d","%h":"%b","%R":"%H\\:%M",
"%r":"%I\\:%M\\:%S\\s%p","%T":"%H\\:%M\\:%S","%x":"%m\\/%d\\/(?:%y|%Y)","%X":"%H\\:%M\\:%S"};for(var g in b)e=e.replace(g,b[g]);f={"%a":"(?:Sun(?:day)?)|(?:Mon(?:day)?)|(?:Tue(?:sday)?)|(?:Wed(?:nesday)?)|(?:Thu(?:rsday)?)|(?:Fri(?:day)?)|(?:Sat(?:urday)?)","%b":"(?:Jan(?:uary)?)|(?:Feb(?:ruary)?)|(?:Mar(?:ch)?)|(?:Apr(?:il)?)|May|(?:Jun(?:e)?)|(?:Jul(?:y)?)|(?:Aug(?:ust)?)|(?:Sep(?:tember)?)|(?:Oct(?:ober)?)|(?:Nov(?:ember)?)|(?:Dec(?:ember)?)","%C":"\\d\\d","%d":"0[1-9]|[1-9](?!\\d)|1\\d|2\\d|30|31",
"%H":"\\d(?!\\d)|[0,1]\\d|20|21|22|23","%I":"\\d(?!\\d)|0\\d|10|11|12","%j":"00[1-9]|0?[1-9](?!\\d)|0?[1-9]\\d(?!\\d)|[1,2]\\d\\d|3[0-6]\\d","%m":"0[1-9]|[1-9](?!\\d)|10|11|12","%M":"0\\d|\\d(?!\\d)|[1-5]\\d","%n":"\\s","%p":"AM|am|PM|pm|A\\.M\\.|a\\.m\\.|P\\.M\\.|p\\.m\\.","%S":"0\\d|\\d(?!\\d)|[1-5]\\d|60","%U":"0\\d|\\d(?!\\d)|[1-4]\\d|50|51|52|53","%W":"0\\d|\\d(?!\\d)|[1-4]\\d|50|51|52|53","%w":"[0-6]","%y":"\\d\\d","%Y":"\\d\\d\\d\\d","%%":"%","%t":"\\s"};var m={Wd:0,Vd:1,Zd:2,Sd:3,$d:4,Yd:5,
Xd:6,Td:7,ce:8,be:9,ae:10,Ud:11};g={Yc:0,Wc:1,$c:2,ad:3,Zc:4,Vc:5,Xc:6};b={Wc:0,$c:1,ad:2,Zc:3,Vc:4,Xc:5,Yc:6};for(var k in f)e=e.replace(k,"("+k+f[k]+")");var n=[];for(f=e.indexOf("%");0<=f;f=e.indexOf("%"))n.push(e[f+1]),e=e.replace(new RegExp("\\%"+e[f+1],"g"),"");var p=(new RegExp("^"+e,"i")).exec(a?G(x,a):"");if(p){k=d();f=r=>{r=n.indexOf(r);if(0<=r)return p[r+1]};if(e=f("S"))k.Rc=parseInt(e);if(e=f("M"))k.min=parseInt(e);if(e=f("H"))k.hour=parseInt(e);else if(e=f("I")){var q=parseInt(e);if(e=
f("p"))q+="P"===e.toUpperCase()[0]?12:0;k.hour=q}if(e=f("Y"))k.year=parseInt(e);else if(e=f("y"))q=parseInt(e),q=(e=f("C"))?q+100*parseInt(e):q+(69>q?2E3:1900),k.year=q;if(e=f("m"))k.month=parseInt(e)-1;else if(e=f("b"))k.month=m[e.substring(0,3).toUpperCase()]||0;if(e=f("d"))k.day=parseInt(e);else if(e=f("j"))for(g=parseInt(e),b=rd(k.year),m=0;12>m;++m)e=Ad(b?Bd:Cd,m-1),g<=e+(b?Bd:Cd)[m]&&(k.day=g-e);else if(e=f("a"))if(m=e.substring(0,3).toUpperCase(),e=f("U"))g=g[m],b=parseInt(e),m=new Date(k.year,
0,1),g=0===m.getDay()?Dd(m,g+7*(b-1)):Dd(m,7-m.getDay()+g+7*(b-1)),k.day=g.getDate(),k.month=g.getMonth();else if(e=f("W"))g=b[m],b=parseInt(e),m=new Date(k.year,0,1),g=1===m.getDay()?Dd(m,g+7*(b-1)):Dd(m,7-m.getDay()+1+g+7*(b-1)),k.day=g.getDate(),k.month=g.getMonth();k=new Date(k.year,k.month,k.day,k.hour,k.min,k.Rc,0);A[c>>2]=k.getSeconds();A[c+4>>2]=k.getMinutes();A[c+8>>2]=k.getHours();A[c+12>>2]=k.getDate();A[c+16>>2]=k.getMonth();A[c+20>>2]=k.getFullYear()-1900;A[c+24>>2]=k.getDay();A[c+28>>
2]=Ad(rd(k.getFullYear())?Bd:Cd,k.getMonth()-1)+k.getDate()-1;A[c+32>>2]=0;return a+eb(p[0]).length-1}return 0}};(function(){function a(c){c=c.exports;h.asm=c;pa=h.asm.Ja;ua();C=h.asm.La;wa.unshift(h.asm.Ka);Ea("wasm-instantiate");return c}var b={a:oe};Da("wasm-instantiate");if(h.instantiateWasm)try{return h.instantiateWasm(b,a)}catch(c){return aa("Module.instantiateWasm callback failed with error: "+c),!1}La(b,function(c){a(c.instance)});return{}})();
function Jd(){return(Jd=h.asm.Ma).apply(null,arguments)}function U(){return(U=h.asm.Na).apply(null,arguments)}function ud(){return(ud=h.asm.Oa).apply(null,arguments)}var pe=h._main=function(){return(pe=h._main=h.asm.Pa).apply(null,arguments)};function Va(){return(Va=h.asm.Qa).apply(null,arguments)}function Yc(){return(Yc=h.asm.Ra).apply(null,arguments)}h.__embind_initialize_bindings=function(){return(h.__embind_initialize_bindings=h.asm.Sa).apply(null,arguments)};
function X(){return(X=h.asm.Ta).apply(null,arguments)}function Y(){return(Y=h.asm.Ua).apply(null,arguments)}function Z(){return(Z=h.asm.Va).apply(null,arguments)}function qe(){return(qe=h.asm.Wa).apply(null,arguments)}function Id(){return(Id=h.asm.Xa).apply(null,arguments)}function Hd(){return(Hd=h.asm.Ya).apply(null,arguments)}function Wa(){return(Wa=h.asm.Za).apply(null,arguments)}function Ta(){return(Ta=h.asm._a).apply(null,arguments)}
h.dynCall_ji=function(){return(h.dynCall_ji=h.asm.$a).apply(null,arguments)};var re=h.dynCall_viijii=function(){return(re=h.dynCall_viijii=h.asm.ab).apply(null,arguments)};h.dynCall_jiji=function(){return(h.dynCall_jiji=h.asm.bb).apply(null,arguments)};
var se=h.dynCall_j=function(){return(se=h.dynCall_j=h.asm.cb).apply(null,arguments)},te=h.dynCall_iiiiij=function(){return(te=h.dynCall_iiiiij=h.asm.db).apply(null,arguments)},ue=h.dynCall_jiiii=function(){return(ue=h.dynCall_jiiii=h.asm.eb).apply(null,arguments)};h.dynCall_iiiiijj=function(){return(h.dynCall_iiiiijj=h.asm.fb).apply(null,arguments)};h.dynCall_iiiiiijj=function(){return(h.dynCall_iiiiiijj=h.asm.gb).apply(null,arguments)};
h.dynCall_iiiij=function(){return(h.dynCall_iiiij=h.asm.hb).apply(null,arguments)};h.dynCall_vij=function(){return(h.dynCall_vij=h.asm.ib).apply(null,arguments)};h.___start_em_js=381888;h.___stop_em_js=382106;function ae(a,b){var c=Y();try{C.get(a)(b)}catch(d){Z(c);if(d!==d+0)throw d;X(1,0)}}function Nd(a,b){var c=Y();try{return C.get(a)(b)}catch(d){Z(c);if(d!==d+0)throw d;X(1,0)}}function fe(a,b,c,d){var e=Y();try{C.get(a)(b,c,d)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}
function Od(a,b,c){var d=Y();try{return C.get(a)(b,c)}catch(e){Z(d);if(e!==e+0)throw e;X(1,0)}}function be(a,b,c){var d=Y();try{C.get(a)(b,c)}catch(e){Z(d);if(e!==e+0)throw e;X(1,0)}}function Qd(a,b,c,d,e){var f=Y();try{return C.get(a)(b,c,d,e)}catch(g){Z(f);if(g!==g+0)throw g;X(1,0)}}function Pd(a,b,c,d){var e=Y();try{return C.get(a)(b,c,d)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function he(a,b,c,d,e,f){var g=Y();try{C.get(a)(b,c,d,e,f)}catch(m){Z(g);if(m!==m+0)throw m;X(1,0)}}
function ie(a,b,c,d,e,f,g){var m=Y();try{C.get(a)(b,c,d,e,f,g)}catch(k){Z(m);if(k!==k+0)throw k;X(1,0)}}function Vd(a,b,c,d,e,f,g,m,k,n){var p=Y();try{return C.get(a)(b,c,d,e,f,g,m,k,n)}catch(q){Z(p);if(q!==q+0)throw q;X(1,0)}}function Sd(a,b,c,d,e,f){var g=Y();try{return C.get(a)(b,c,d,e,f)}catch(m){Z(g);if(m!==m+0)throw m;X(1,0)}}function de(a,b,c,d,e){var f=Y();try{C.get(a)(b,c,d,e)}catch(g){Z(f);if(g!==g+0)throw g;X(1,0)}}
function ge(a,b,c,d,e){var f=Y();try{C.get(a)(b,c,d,e)}catch(g){Z(f);if(g!==g+0)throw g;X(1,0)}}function ke(a,b,c,d,e,f,g,m,k,n){var p=Y();try{C.get(a)(b,c,d,e,f,g,m,k,n)}catch(q){Z(p);if(q!==q+0)throw q;X(1,0)}}function $d(a){var b=Y();try{C.get(a)()}catch(c){Z(b);if(c!==c+0)throw c;X(1,0)}}function Td(a,b,c,d,e,f,g){var m=Y();try{return C.get(a)(b,c,d,e,f,g)}catch(k){Z(m);if(k!==k+0)throw k;X(1,0)}}
function Rd(a,b,c,d,e,f){var g=Y();try{return C.get(a)(b,c,d,e,f)}catch(m){Z(g);if(m!==m+0)throw m;X(1,0)}}function Ud(a,b,c,d,e,f,g,m){var k=Y();try{return C.get(a)(b,c,d,e,f,g,m)}catch(n){Z(k);if(n!==n+0)throw n;X(1,0)}}function Ld(a,b,c,d){var e=Y();try{return C.get(a)(b,c,d)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function Kd(a,b,c,d){var e=Y();try{return C.get(a)(b,c,d)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}
function Md(a){var b=Y();try{return C.get(a)()}catch(c){Z(b);if(c!==c+0)throw c;X(1,0)}}function je(a,b,c,d,e,f,g,m){var k=Y();try{C.get(a)(b,c,d,e,f,g,m)}catch(n){Z(k);if(n!==n+0)throw n;X(1,0)}}function Wd(a,b,c,d,e,f,g,m,k,n,p,q){var r=Y();try{return C.get(a)(b,c,d,e,f,g,m,k,n,p,q)}catch(l){Z(r);if(l!==l+0)throw l;X(1,0)}}function le(a,b,c,d,e,f,g,m,k,n,p){var q=Y();try{C.get(a)(b,c,d,e,f,g,m,k,n,p)}catch(r){Z(q);if(r!==r+0)throw r;X(1,0)}}
function me(a,b,c,d,e,f,g,m,k,n,p,q,r,l,t,v){var z=Y();try{C.get(a)(b,c,d,e,f,g,m,k,n,p,q,r,l,t,v)}catch(H){Z(z);if(H!==H+0)throw H;X(1,0)}}function ee(a,b,c,d){var e=Y();try{C.get(a)(b,c,d)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function ce(a,b,c,d){var e=Y();try{C.get(a)(b,c,d)}catch(f){Z(e);if(f!==f+0)throw f;X(1,0)}}function Yd(a){var b=Y();try{return se(a)}catch(c){Z(b);if(c!==c+0)throw c;X(1,0)}}
function ne(a,b,c,d,e,f,g){var m=Y();try{re(a,b,c,d,e,f,g)}catch(k){Z(m);if(k!==k+0)throw k;X(1,0)}}function Xd(a,b,c,d,e,f,g){var m=Y();try{return te(a,b,c,d,e,f,g)}catch(k){Z(m);if(k!==k+0)throw k;X(1,0)}}function Zd(a,b,c,d,e){var f=Y();try{return ue(a,b,c,d,e)}catch(g){Z(f);if(g!==g+0)throw g;X(1,0)}}h.addRunDependency=Da;h.removeRunDependency=Ea;h.FS_createPath=bc;h.FS_createDataFile=ub;h.FS_createLazyFile=ec;h.FS_createDevice=N;h.FS_unlink=Tb;h.FS_createPreloadedFile=tb;var ve;
Ca=function we(){ve||xe();ve||(Ca=we)};function ye(a=[]){var b=pe;a.unshift(da);var c=a.length,d=qe(4*(c+1)),e=d>>2;a.forEach(g=>{var m=A,k=e++,n=cb(g)+1,p=qe(n);db(g,x,p,n);m[k]=p});A[e]=0;try{var f=b(c,d);zd(f,!0)}catch(g){g instanceof Oa||"unwind"==g||ea(1,g)}}
function xe(){var a=ca;function b(){if(!ve&&(ve=!0,h.calledRun=!0,!qa)){h.noFSInit||ac||(ac=!0,$b(),h.stdin=h.stdin,h.stdout=h.stdout,h.stderr=h.stderr,h.stdin?N("/dev","stdin",h.stdin):Rb("/dev/tty","/dev/stdin"),h.stdout?N("/dev","stdout",null,h.stdout):Rb("/dev/tty","/dev/stdout"),h.stderr?N("/dev","stderr",null,h.stderr):Rb("/dev/tty1","/dev/stderr"),Wb("/dev/stdin",0),Wb("/dev/stdout",1),Wb("/dev/stderr",1));Bb=!1;Pa(wa);Pa(xa);if(h.onRuntimeInitialized)h.onRuntimeInitialized();ze&&ye(a);if(h.postRun)for("function"==
typeof h.postRun&&(h.postRun=[h.postRun]);h.postRun.length;){var c=h.postRun.shift();ya.unshift(c)}Pa(ya)}}if(!(0<Aa)){if(h.preRun)for("function"==typeof h.preRun&&(h.preRun=[h.preRun]);h.preRun.length;)za();Pa(va);0<Aa||(h.setStatus?(h.setStatus("Running..."),setTimeout(function(){setTimeout(function(){h.setStatus("")},1);b()},1)):b())}}if(h.preInit)for("function"==typeof h.preInit&&(h.preInit=[h.preInit]);0<h.preInit.length;)h.preInit.pop()();var ze=!0;h.noInitialRun&&(ze=!1);xe();
