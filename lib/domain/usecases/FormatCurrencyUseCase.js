const Currency = require('../entities/Currency');

/**
 * FormatCurrencyUseCase
 * Use case for formatting a currency amount
 */
class FormatCurrencyUseCase {
  /**
   * Execute the use case
   * @param {number} amount - The amount to format
   * @param {string} currencyCode - The currency code (e.g., 'USD')
   * @returns {string} - The formatted currency amount
   */
  execute(amount, currencyCode = 'USD') {
    const currency = new Currency(amount, currencyCode);
    return currency.format();
  }
}

module.exports = FormatCurrencyUseCase;
