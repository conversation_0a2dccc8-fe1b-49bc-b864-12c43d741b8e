/**
 * GetSessionTokenUseCase
 * Use case for getting a session token from the authentication API
 */
class GetSessionTokenUseCase {
  /**
   * @param {Object} authRepository - The authentication repository
   */
  constructor(authRepository) {
    this.authRepository = authRepository;
  }

  /**
   * Execute the use case
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity
   */
  async execute(headers = {}) {
    return await this.authRepository.getSessionToken(headers);
  }
}

module.exports = GetSessionTokenUseCase;
