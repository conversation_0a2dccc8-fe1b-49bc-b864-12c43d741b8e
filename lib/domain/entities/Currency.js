/**
 * Currency entity
 * Represents a currency amount
 */
class Currency {
  /**
   * @param {number} amount - The amount
   * @param {string} currencyCode - The currency code (e.g., 'USD')
   */
  constructor(amount, currencyCode = 'USD') {
    this.amount = amount;
    this.currencyCode = currencyCode;
  }

  /**
   * Format the currency amount
   * @returns {string} - The formatted currency amount
   */
  format() {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: this.currencyCode
    }).format(this.amount);
  }

  /**
   * Get the amount
   * @returns {number} - The amount
   */
  getAmount() {
    return this.amount;
  }

  /**
   * Get the currency code
   * @returns {string} - The currency code
   */
  getCurrencyCode() {
    return this.currencyCode;
  }
}

module.exports = Currency;
