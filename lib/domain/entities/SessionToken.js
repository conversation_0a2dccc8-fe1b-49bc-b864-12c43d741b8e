/**
 * SessionToken entity
 * Represents a session token from the eKYC authentication API
 */
class SessionToken {
  /**
   * @param {Object} data - The session token data
   */
  constructor(data) {
    this.data = data;
  }

  /**
   * Get the token value
   * @returns {string|null} - The token value or null if not available
   */
  getToken() {
    return this.data?.token || null;
  }

  /**
   * Get the eKYC token value
   * @returns {string|null} - The eKYC token value or null if not available
   */
  getEkycToken() {
    // Handle the new response format with code, description, and data.ekycToken
    if (this.data?.data?.ekycToken) {
      return this.data.data.ekycToken;
    }
    // Fallback to the old format or return null
    return this.data?.ekycToken || null;
  }

  /**
   * Get the expiration time
   * @returns {string|null} - The expiration time or null if not available
   */
  getExpiresAt() {
    return this.data?.expiresAt || null;
  }

  /**
   * Get the response code
   * @returns {string|null} - The response code or null if not available
   */
  getCode() {
    return this.data?.code || null;
  }

  /**
   * Get the response description
   * @returns {string|null} - The response description or null if not available
   */
  getDescription() {
    return this.data?.description || null;
  }

  /**
   * Check if the token is valid
   * @returns {boolean} - True if the token is valid
   */
  isValid() {
    return !!this.getEkycToken();
  }

  /**
   * Get the raw data
   * @returns {Object} - The raw data
   */
  toJSON() {
    return this.data;
  }
}

module.exports = SessionToken;
