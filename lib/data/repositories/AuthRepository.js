const SessionToken = require('../../domain/entities/SessionToken');

/**
 * AuthRepository
 * Repository for authentication-related operations
 */
class AuthRepository {
  /**
   * @param {Object} authApiDataSource - The authentication API data source
   */
  constructor(authApiDataSource) {
    this.authApiDataSource = authApiDataSource;
  }

  /**
   * Get a session token
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity
   */
  async getSessionToken(headers = {}) {
    const data = await this.authApiDataSource.getSessionToken(headers);
    return new SessionToken(data);
  }

  /**
   * Get a FaceTec session token using the stored eKYC token
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<SessionToken>} - A promise that resolves to a SessionToken entity
   */
  async getFaceTecSessionTokenWithEkycToken(headers = {}) {
    const data = await this.authApiDataSource.getFaceTecSessionTokenWithEkycToken(headers);
    return new SessionToken(data);
  }
}

module.exports = AuthRepository;
