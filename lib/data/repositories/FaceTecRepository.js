const IDScanDataSource = require('../datasources/IDScanDataSource');
const IDScanFrontDataSource = require('../datasources/IDScanFrontDataSource');
const IDScanBackDataSource = require('../datasources/IDScanBackDataSource');
const LivenessCheckDataSource = require('../datasources/LivenessCheckDataSource');

/**
 * Repository for FaceTec related operations
 * Implements the repository pattern to abstract data access
 */
class FaceTecRepository {
    constructor() {
        this.idScanDataSource = new IDScanDataSource();
        this.idScanFrontDataSource = new IDScanFrontDataSource();
        this.idScanBackDataSource = new IDScanBackDataSource();
        this.livenessCheckDataSource = new LivenessCheckDataSource();
    }

    /**
     * Submit ID scan data for processing
     * @param {Object} scanData - The scan data including images and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitIDScan(scanData, headers = {}, onProgress = null) {
        try {
            // Call the data source to post ID scan data
            const response = await this.idScanDataSource.postIDScanOnly(scanData, headers, onProgress);

            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.

            return response;
        } catch (error) {
            console.error('FaceTecRepository - submitIDScan error:', error);
            throw error;
        }
    }

    /**
     * Submit front ID scan data for processing
     * @param {Object} scanData - The front scan data including front image and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitIDScanFront(scanData, headers = {}, onProgress = null) {
        const startTime = performance.now();

        try {
            console.log('=== FACETEC REPOSITORY - FRONT ID SCAN START ===');

            // Log request details
            this.logFrontScanRequest(scanData, headers);

            // Validate scan data and log validation results
            this.validateAndLogFrontScanData(scanData);

            // Call the front data source to post front ID scan data
            console.log('📤 Calling front data source...');
            const response = await this.idScanFrontDataSource.postIDScanFront(scanData, headers, onProgress);

            // Log successful response
            this.logFrontScanResponse(response, startTime);

            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.

            console.log('=== FACETEC REPOSITORY - FRONT ID SCAN SUCCESS ===');
            return response;
        } catch (error) {
            const endTime = performance.now();
            const duration = endTime - startTime;

            console.error('=== FACETEC REPOSITORY - FRONT ID SCAN ERROR ===');
            console.error('❌ Error Type:', error.constructor.name);
            console.error('❌ Error Message:', error.message);
            console.error('❌ Error Stack:', error.stack);
            console.error('⏱️ Duration before error:', `${duration.toFixed(2)}ms`);

            // Log additional error context
            if (error.response) {
                console.error('📥 Error Response Status:', error.response.status);
                console.error('📥 Error Response Headers:', error.response.headers);
                console.error('📥 Error Response Data:', error.response.data);
            }

            if (error.request) {
                console.error('📤 Failed Request Details:', {
                    url: error.request.url || 'Unknown URL',
                    method: error.request.method || 'Unknown Method',
                    timeout: error.request.timeout || 'No timeout set'
                });
            }

            console.error('=== END FRONT ID SCAN ERROR LOG ===');
            throw error;
        }
    }

    /**
     * Submit back ID scan data for processing
     * @param {Object} scanData - The back scan data including back image and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitIDScanBack(scanData, headers = {}, onProgress = null) {
        const startTime = performance.now();

        try {
            console.log('=== FACETEC REPOSITORY - BACK ID SCAN START ===');

            // Log request details
            this.logBackScanRequest(scanData, headers);

            // Validate scan data and log validation results
            this.validateAndLogBackScanData(scanData);

            // Call the back data source to post back ID scan data
            console.log('📤 Calling back data source...');
            const response = await this.idScanBackDataSource.postIDScanBack(scanData, headers, onProgress);

            // Log successful response
            this.logBackScanResponse(response, startTime);

            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.

            console.log('=== FACETEC REPOSITORY - BACK ID SCAN SUCCESS ===');
            return response;
        } catch (error) {
            const endTime = performance.now();
            const duration = endTime - startTime;

            console.error('=== FACETEC REPOSITORY - BACK ID SCAN ERROR ===');
            console.error('❌ Error Type:', error.constructor.name);
            console.error('❌ Error Message:', error.message);
            console.error('❌ Error Stack:', error.stack);
            console.error('⏱️ Duration before error:', `${duration.toFixed(2)}ms`);

            // Log additional error context
            if (error.response) {
                console.error('📥 Error Response Status:', error.response.status);
                console.error('📥 Error Response Headers:', error.response.headers);
                console.error('📥 Error Response Data:', error.response.data);
            }

            if (error.request) {
                console.error('📤 Failed Request Details:', {
                    url: error.request.url || 'Unknown URL',
                    method: error.request.method || 'Unknown Method',
                    timeout: error.request.timeout || 'No timeout set'
                });
            }

            console.error('=== END BACK ID SCAN ERROR LOG ===');
            throw error;
        }
    }

    /**
     * Submit liveness check data for processing
     * @param {Object} livenessData - The liveness data including face scan and audit trail
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - Processed response
     */
    async submitLivenessCheck(livenessData, headers = {}, onProgress = null) {
        try {
            // Call the data source to post liveness check data
            const response = await this.livenessCheckDataSource.postLivenessCheck(livenessData, headers, onProgress);
            
            // Repository can add additional business logic here if needed
            // For example: data transformation, caching, etc.
            
            return response;
        } catch (error) {
            console.error('FaceTecRepository - submitLivenessCheck error:', error);
            throw error;
        }
    }


    /**
     * Validate scan data before submission
     * @param {Object} scanData - The scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateScanData(scanData) {
        if (!scanData) {
            throw new Error('Scan data is required');
        }

        if (!scanData.idScan) {
            throw new Error('ID scan data is required');
        }

        // Add more validation rules as needed
        return true;
    }

    /**
     * Validate front scan data before submission
     * @param {Object} scanData - The front scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateFrontScanData(scanData) {
        return this.idScanFrontDataSource.validateFrontScanData(scanData);
    }

    /**
     * Validate back scan data before submission
     * @param {Object} scanData - The back scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateBackScanData(scanData) {
        return this.idScanBackDataSource.validateBackScanData(scanData);
    }


    /**
     * Validate liveness data before submission
     * @param {Object} livenessData - The liveness data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateLivenessData(livenessData) {
        if (!livenessData) {
            throw new Error('Liveness data is required');
        }

        if (!livenessData.faceScan) {
            throw new Error('Face scan data is required');
        }

        // Add more validation rules as needed
        return true;
    }

    /**
     * Log front scan request details for debugging
     * @param {Object} scanData - The scan data being sent
     * @param {Object} headers - The headers being sent
     */
    logFrontScanRequest(scanData, headers) {
        console.log('📤 FRONT SCAN REQUEST DETAILS:');
        console.log('🎯 Target Endpoint: /api/match-3d-2d-idscan/front');

        // Log headers with sensitive data masking
        console.log('📋 Request Headers:');
        Object.keys(headers).forEach(key => {
            const value = headers[key];
            if (key.toLowerCase().includes('token') || key.toLowerCase().includes('auth')) {
                console.log(`  ${key}: ${this.maskSensitiveData(value)}`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        });

        // Log scan data structure
        this.logScanDataStructure(scanData, 'FRONT');
    }

    /**
     * Log back scan request details for debugging
     * @param {Object} scanData - The scan data being sent
     * @param {Object} headers - The headers being sent
     */
    logBackScanRequest(scanData, headers) {
        console.log('📤 BACK SCAN REQUEST DETAILS:');
        console.log('🎯 Target Endpoint: /api/match-3d-2d-idscan/back');

        // Log headers with sensitive data masking
        console.log('📋 Request Headers:');
        Object.keys(headers).forEach(key => {
            const value = headers[key];
            if (key.toLowerCase().includes('token') || key.toLowerCase().includes('auth')) {
                console.log(`  ${key}: ${this.maskSensitiveData(value)}`);
            } else {
                console.log(`  ${key}: ${value}`);
            }
        });

        // Log scan data structure
        this.logScanDataStructure(scanData, 'BACK');
    }

    /**
     * Validate and log front scan data for debugging
     * @param {Object} scanData - The scan data to validate
     */
    validateAndLogFrontScanData(scanData) {
        console.log('🔍 FRONT SCAN DATA VALIDATION:');

        const validationResults = {
            hasIdScan: !!scanData?.idScan,
            hasFrontImage: !!scanData?.idScanFrontImage,
            hasBackImage: !!scanData?.idScanBackImage,
            hasEnableConfirmInfo: scanData?.enableConfirmInfo !== undefined,
            enableConfirmInfoValue: scanData?.enableConfirmInfo
        };

        console.log('✅ Validation Results:', validationResults);

        // Check for potential issues
        const issues = [];
        if (!validationResults.hasIdScan) {
            issues.push('❌ Missing idScan data');
        }
        if (!validationResults.hasFrontImage) {
            issues.push('❌ Missing idScanFrontImage (required for front scan)');
        }
        if (validationResults.hasBackImage) {
            console.log('ℹ️ Back image present in front scan request (may be intentional)');
        }

        if (issues.length > 0) {
            console.error('🚨 VALIDATION ISSUES FOUND:');
            issues.forEach(issue => console.error(issue));
        } else {
            console.log('✅ All required fields present for front scan');
        }

        // Validate using data source validation
        try {
            this.validateFrontScanData(scanData);
            console.log('✅ Data source validation passed');
        } catch (error) {
            console.error('❌ Data source validation failed:', error.message);
            throw error;
        }
    }

    /**
     * Validate and log back scan data for debugging
     * @param {Object} scanData - The scan data to validate
     */
    validateAndLogBackScanData(scanData) {
        console.log('🔍 BACK SCAN DATA VALIDATION:');

        const validationResults = {
            hasIdScan: !!scanData?.idScan,
            hasFrontImage: !!scanData?.idScanFrontImage,
            hasBackImage: !!scanData?.idScanBackImage,
            hasEnableConfirmInfo: scanData?.enableConfirmInfo !== undefined,
            enableConfirmInfoValue: scanData?.enableConfirmInfo
        };

        console.log('✅ Validation Results:', validationResults);

        // Check for potential issues
        const issues = [];
        if (!validationResults.hasIdScan) {
            issues.push('❌ Missing idScan data');
        }
        if (!validationResults.hasBackImage) {
            issues.push('❌ Missing idScanBackImage (required for back scan)');
        }
        if (!validationResults.hasFrontImage) {
            console.log('ℹ️ Front image missing in back scan request (may be optional)');
        }

        if (issues.length > 0) {
            console.error('🚨 VALIDATION ISSUES FOUND:');
            issues.forEach(issue => console.error(issue));
        } else {
            console.log('✅ All required fields present for back scan');
        }

        // Validate using data source validation
        try {
            this.validateBackScanData(scanData);
            console.log('✅ Data source validation passed');
        } catch (error) {
            console.error('❌ Data source validation failed:', error.message);
            throw error;
        }
    }

    /**
     * Log scan data structure with smart truncation
     * @param {Object} scanData - The scan data to log
     * @param {string} scanType - Type of scan (FRONT/BACK)
     */
    logScanDataStructure(scanData, scanType) {
        console.log(`📊 ${scanType} SCAN DATA STRUCTURE:`);

        if (!scanData) {
            console.error('❌ Scan data is null or undefined');
            return;
        }

        // Log basic structure
        const structure = {
            keys: Object.keys(scanData),
            hasIdScan: !!scanData.idScan,
            hasIdScanFrontImage: !!scanData.idScanFrontImage,
            hasIdScanBackImage: !!scanData.idScanBackImage,
            enableConfirmInfo: scanData.enableConfirmInfo
        };

        console.log('📋 Basic Structure:', structure);

        // Log image data details (without full content)
        if (scanData.idScanFrontImage) {
            console.log('🖼️ Front Image Details:', this.getImageDataInfo(scanData.idScanFrontImage));
        }

        if (scanData.idScanBackImage) {
            console.log('🖼️ Back Image Details:', this.getImageDataInfo(scanData.idScanBackImage));
        }

        // Log idScan data structure (truncated)
        if (scanData.idScan) {
            console.log('🆔 ID Scan Data:', this.getTruncatedData(scanData.idScan));
        }

        // Calculate total request size
        const requestBody = JSON.stringify(scanData);
        console.log(`📏 Total Request Size: ${requestBody.length} characters`);

        if (requestBody.length > 1000) {
            console.log('📄 Request Body (truncated):');
            console.log(this.smartTruncate(requestBody, 1000));
        } else {
            console.log('📄 Request Body:', requestBody);
        }
    }

    /**
     * Log front scan response details
     * @param {Object} response - The response from the API
     * @param {number} startTime - Start time for performance measurement
     */
    logFrontScanResponse(response, startTime) {
        const endTime = performance.now();
        const duration = endTime - startTime;

        console.log('📥 FRONT SCAN RESPONSE:');
        console.log(`⏱️ Duration: ${duration.toFixed(2)}ms`);
        console.log('✅ Response Structure:', {
            wasProcessed: response.wasProcessed,
            error: response.error,
            hasScanResultBlob: !!response.scanResultBlob,
            hasOriginalResponse: !!response.originalResponse,
            hasErrorMessage: !!response.errorMessage
        });

        if (response.scanResultBlob) {
            console.log(`📦 Scan Result Blob Length: ${response.scanResultBlob.length} characters`);
        }

        if (response.originalResponse) {
            console.log('📋 Original Response Keys:', Object.keys(response.originalResponse));
        }
    }

    /**
     * Log back scan response details
     * @param {Object} response - The response from the API
     * @param {number} startTime - Start time for performance measurement
     */
    logBackScanResponse(response, startTime) {
        const endTime = performance.now();
        const duration = endTime - startTime;

        console.log('📥 BACK SCAN RESPONSE:');
        console.log(`⏱️ Duration: ${duration.toFixed(2)}ms`);
        console.log('✅ Response Structure:', {
            wasProcessed: response.wasProcessed,
            error: response.error,
            hasScanResultBlob: !!response.scanResultBlob,
            hasOriginalResponse: !!response.originalResponse,
            hasErrorMessage: !!response.errorMessage,
            hasOcrData: !!(response.originalResponse?.data?.documentData)
        });

        if (response.scanResultBlob) {
            console.log(`📦 Scan Result Blob Length: ${response.scanResultBlob.length} characters`);
        }

        if (response.originalResponse?.data?.documentData) {
            console.log('📄 OCR Document Data Present: Yes');
            try {
                const documentData = JSON.parse(response.originalResponse.data.documentData);
                console.log('📊 OCR Data Structure:', {
                    hasScannedValues: !!documentData.scannedValues,
                    groupCount: documentData.scannedValues?.groups?.length || 0
                });
            } catch (error) {
                console.error('❌ Failed to parse OCR document data:', error.message);
            }
        }
    }

    /**
     * Mask sensitive data for logging
     * @param {string} data - The data to mask
     * @returns {string} - Masked data
     */
    maskSensitiveData(data) {
        if (!data || typeof data !== 'string') {
            return String(data);
        }

        if (data.length <= 8) {
            return '*'.repeat(data.length);
        }

        return data.substring(0, 4) + '*'.repeat(data.length - 8) + data.substring(data.length - 4);
    }

    /**
     * Get image data information without logging the full content
     * @param {string} imageData - Base64 image data
     * @returns {Object} - Image data information
     */
    getImageDataInfo(imageData) {
        if (!imageData || typeof imageData !== 'string') {
            return { type: 'invalid', length: 0 };
        }

        const info = {
            length: imageData.length,
            type: 'unknown'
        };

        // Detect image type from data URL prefix
        if (imageData.startsWith('data:image/')) {
            const mimeMatch = imageData.match(/data:image\/([^;]+)/);
            if (mimeMatch) {
                info.type = mimeMatch[1];
            }
        } else if (imageData.startsWith('/9j/')) {
            info.type = 'jpeg (base64)';
        } else if (imageData.startsWith('iVBORw0KGgo')) {
            info.type = 'png (base64)';
        } else {
            info.type = 'base64 data';
        }

        return info;
    }

    /**
     * Get truncated data for logging
     * @param {any} data - The data to truncate
     * @returns {any} - Truncated data
     */
    getTruncatedData(data) {
        if (typeof data === 'string') {
            return this.smartTruncate(data, 200);
        } else if (typeof data === 'object' && data !== null) {
            const stringified = JSON.stringify(data);
            if (stringified.length > 200) {
                return this.smartTruncate(stringified, 200);
            }
            return data;
        }
        return data;
    }

    /**
     * Smart truncation for large strings
     * @param {string} str - String to truncate
     * @param {number} maxLength - Maximum length
     * @returns {string} - Truncated string
     */
    smartTruncate(str, maxLength) {
        if (!str || str.length <= maxLength) {
            return str;
        }

        const halfLength = Math.floor(maxLength / 2) - 10;
        const start = str.substring(0, halfLength);
        const end = str.substring(str.length - halfLength);

        return `${start}...[${str.length - maxLength} chars truncated]...${end}`;
    }

}

module.exports = FaceTecRepository;
