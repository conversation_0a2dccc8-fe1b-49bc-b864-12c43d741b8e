const UuidGenerator = require('../../infrastructure/utils/UuidGenerator');
const { TokenStorage } = require('../../infrastructure/utils');

/**
 * AuthApiDataSource
 * Data source for authentication API
 */
class AuthApiDataSource {
  /**
   * Get a session token from the API
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<Object>} - A promise that resolves to the API response
   */
  async getSessionToken(headers = {}) {
    try {
      // Use the Next.js API route to proxy the request
      const url = '/api/session-token';

      // Generate UUIDs for headers if not provided
      const deviceId = headers['X-Ekyc-Device-Info'] ? null : UuidGenerator.getDeviceId();
      // Use getUniqueId() to generate a new UUID every time for session-related IDs
      const tId = UuidGenerator.getUniqueId();
      // Use provided session ID or generate a new one and store it
      const sessionId = headers['X-Session-Id'] || UuidGenerator.getUniqueId();
      // Store the session ID for future use
      if (!headers['X-Session-Id']) {
        TokenStorage.storeSessionId(sessionId);
      }

      const correlationid = UuidGenerator.getUniqueId();

      // Merge default headers with any additional headers
      const requestHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Ekyc-Sdk-Version': '1.0.0',
        'X-Ekyc-Device-Info': `browser|${deviceId}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,
        'X-Session-Id': `${sessionId}`,
        'X-Tid': `${tId}`,
        'correlationid': `${correlationid}`,
        ...headers
      };

      // Only add Authorization header if it exists and is not undefined
      if (headers['Authorization']) {
        requestHeaders['Authorization'] = headers['Authorization'];
      }

      // Make the GET request with headers only (no body)
      const response = await fetch(url, {
        method: 'GET',
        headers: requestHeaders,
      });

      // Check if the response is ok (status in the range 200-299)
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse and return the JSON response
      return await response.json();
    } catch (error) {
      console.error('Error getting session token:', error);
      throw error;
    }
  }

  /**
   * Get a FaceTec session token from the API
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<Object>} - A promise that resolves to the API response
   */
  async getFaceTecSessionToken(headers = {}) {
    try {
      // Use the Next.js API route to proxy the request
      const url = '/api/facetec-session-token';

      // Generate UUIDs for headers if not provided
      const deviceId = headers['X-Ekyc-Device-Info'] ? null : UuidGenerator.getDeviceId();
      // Use getUniqueId() to generate a new UUID every time for session-related IDs
      const tId = UuidGenerator.getUniqueId();
      // Use the stored session ID if available, or the one provided in headers, or generate a new one
      const storedSessionId = TokenStorage.getSessionId();
      const sessionId = headers['X-Session-Id'] || storedSessionId || UuidGenerator.getUniqueId();
      const correlationid = UuidGenerator.getUniqueId();

      // Merge default headers with any additional headers
      const requestHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Ekyc-Sdk-Version': '1.0.0',
        'X-Ekyc-Device-Info': `browser|${deviceId}|https://ekyc-internal-ekyc-browser-demo-dev.ekyc.np.aella.tech/|en|EN`,
        'X-Session-Id': `${sessionId}`,
        'X-Tid': `${tId}`,
        'correlationid': `${correlationid}`,
        ...headers
      };

      // Only add Authorization header if it exists and is not undefined
      if (headers['Authorization']) {
        requestHeaders['Authorization'] = headers['Authorization'];
      }

      // Make the GET request with headers only (no body)
      const response = await fetch(url, {
        method: 'GET',
        headers: requestHeaders,
      });

      // Check if the response is ok (status in the range 200-299)
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse and return the JSON response
      return await response.json();
    } catch (error) {
      console.error('Error getting FaceTec session token:', error);
      throw error;
    }
  }

  /**
   * Get a FaceTec session token from the API using the stored eKYC token
   * @param {Object} headers - Optional headers to include in the request
   * @returns {Promise<Object>} - A promise that resolves to the API response
   */
  async getFaceTecSessionTokenWithEkycToken(headers = {}) {
    try {
      // Use the Next.js API route to proxy the request
      const url = '/api/facetec-session-token';

      // Get the stored eKYC token
      const ekycToken = TokenStorage.getEkycToken();
      if (!ekycToken) {
        throw new Error('No eKYC token found. Please get a session token first.');
      }

      // Generate UUIDs for headers if not provided
      const deviceId = headers['X-Ekyc-Device-Info'] ? null : UuidGenerator.getDeviceId();
      // Use getUniqueId() to generate a new UUID every time for session-related IDs
      const tId = headers['X-Tid'] || UuidGenerator.getUniqueId();
      // Use the stored session ID if available, or the one provided in headers, or generate a new one
      const storedSessionId = TokenStorage.getSessionId();
      const sessionId = headers['X-Session-Id'] || storedSessionId || UuidGenerator.getUniqueId();
      const correlationid = headers['correlationid'] || UuidGenerator.getUniqueId();

      // Merge default headers with any additional headers
      const requestHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Ekyc-Sdk-Version': headers['X-Ekyc-Sdk-Version'] || '1.0.0',
        'X-Ekyc-Device-Info': headers['X-Ekyc-Device-Info'] || `browser|${deviceId}`,
        'X-Session-Id': `${sessionId}`,
        'X-Tid': `${tId}`,
        'correlationid': `${correlationid}`,
        'X-Ekyc-Token': ekycToken,
        ...headers
      };

      // Only add Authorization header if it exists and is not undefined
      if (headers['Authorization']) {
        requestHeaders['Authorization'] = headers['Authorization'];
      }
      // Make the GET request with headers only (no body)
      const response = await fetch(url, {
        method: 'GET',
        headers: requestHeaders,
      });

      // Check if the response is ok (status in the range 200-299)
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Parse and return the JSON response
      return await response.json();
    } catch (error) {
      console.error('Error getting FaceTec session token with eKYC token:', error);
      throw error;
    }
  }
}

module.exports = AuthApiDataSource;
