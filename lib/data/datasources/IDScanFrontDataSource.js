const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');

/**
 * Data source for front ID scan operations
 * Handles HTTP requests to the front ID scan API endpoint
 */
class IDScanFrontDataSource {
    constructor() {
        this.baseUrl = '/api'; // Use Next.js API routes as proxy
    }

    /**
     * Post front ID scan data to the API
     * @param {Object} scanData - The scan data including front image and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - API response
     */
    async postIDScanFront(scanData, headers = {}, onProgress = null) {
        const startTime = performance.now();
        
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            const url = `${this.baseUrl}/match-3d-2d-idscan/front`;

            DeveloperStatusMessages.logMessage(`Starting front ID scan request to: ${url}`);

            // Set up progress tracking
            if (onProgress && typeof onProgress === 'function') {
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const progress = (event.loaded / event.total) * 100;
                        onProgress(progress);
                        DeveloperStatusMessages.logData('Front scan upload progress', `${progress.toFixed(1)}%`);
                    }
                });
            }

            xhr.addEventListener('loadend', () => {
                const endTime = performance.now();
                
                if (xhr.readyState === XMLHttpRequest.DONE) {
                    try {
                        if (xhr.status >= 200 && xhr.status < 300) {
                            const responseData = JSON.parse(xhr.responseText);
                            DeveloperStatusMessages.logPerformance('IDScanFrontDataSource.postIDScanFront', startTime, endTime);
                            DeveloperStatusMessages.logApiCall(url, 'POST', `Success (${xhr.status})`);
                            DeveloperStatusMessages.logData('Front API Response', {
                                status: xhr.status,
                                wasProcessed: responseData.wasProcessed,
                                error: responseData.error,
                                hasScanResultBlob: !!responseData.scanResultBlob,
                                scanType: 'front'
                            });
                            resolve(responseData);
                        } else {
                            DeveloperStatusMessages.logPerformance('IDScanFrontDataSource.postIDScanFront (failed)', startTime, endTime);
                            DeveloperStatusMessages.logError(`Front API call failed with status ${xhr.status}`);
                            
                            // Try to parse error response
                            let errorData = null;
                            try {
                                errorData = JSON.parse(xhr.responseText);
                            } catch (parseError) {
                                DeveloperStatusMessages.logError('Failed to parse error response', parseError);
                            }
                            
                            reject(new Error(`HTTP error! status: ${xhr.status}, message: ${errorData?.errorMessage || 'Unknown error'}`));
                        }
                    } catch (error) {
                        DeveloperStatusMessages.logPerformance('IDScanFrontDataSource.postIDScanFront (parse error)', startTime, endTime);
                        DeveloperStatusMessages.logError('Error parsing front scan response:', error);
                        reject(error);
                    }
                }
            });

            xhr.addEventListener('error', () => {
                const endTime = performance.now();
                DeveloperStatusMessages.logPerformance('IDScanFrontDataSource.postIDScanFront (network error)', startTime, endTime);
                DeveloperStatusMessages.logError('Network error during front ID scan request');
                reject(new Error('Network error occurred during front ID scan'));
            });

            xhr.addEventListener('timeout', () => {
                const endTime = performance.now();
                DeveloperStatusMessages.logPerformance('IDScanFrontDataSource.postIDScanFront (timeout)', startTime, endTime);
                DeveloperStatusMessages.logError('Front ID scan request timed out');
                reject(new Error('Front ID scan request timed out'));
            });

            // Configure request
            xhr.open('POST', url, true);
            xhr.timeout = 60000; // 60 second timeout
            
            // Set headers
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('Accept', 'application/json');
            
            // Add custom headers
            Object.keys(headers).forEach(key => {
                if (headers[key] !== undefined && headers[key] !== null) {
                    xhr.setRequestHeader(key, headers[key]);
                    DeveloperStatusMessages.logData(`Front scan header: ${key}`, headers[key]);
                }
            });

            // Prepare request body
            const requestBody = JSON.stringify(scanData);
            DeveloperStatusMessages.logData('Front scan request body size', `${requestBody.length} bytes`);
            DeveloperStatusMessages.logData('Front scan data keys', Object.keys(scanData));

            // Send request
            xhr.send(requestBody);
        });
    }

    /**
     * Validate front scan data before submission
     * @param {Object} scanData - The scan data to validate
     * @returns {boolean} - True if valid, throws error if invalid
     */
    validateFrontScanData(scanData) {
        if (!scanData) {
            throw new Error('Front scan data is required');
        }

        if (!scanData.idScan) {
            throw new Error('ID scan data is required for front scan');
        }

        if (!scanData.idScanFrontImage) {
            throw new Error('Front image is required for front ID scan');
        }

        DeveloperStatusMessages.logMessage('Front scan data validation passed');
        return true;
    }
}

module.exports = IDScanFrontDataSource;
