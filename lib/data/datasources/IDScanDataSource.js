const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');

/**
 * Data source for ID Scan API operations
 * Handles direct API communication for ID scanning functionality
 */
class IDScanDataSource {
    constructor() {
        this.baseUrl = '/api'; // Use Next.js API routes as proxy
    }

    /**
     * Post ID scan data to the backend API
     * @param {Object} scanData - The scan data including images and metadata
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - API response
     */
    async postIDScanOnly(scanData, headers = {}, onProgress = null) {
        return new Promise((resolve, reject) => {
            const startTime = performance.now();
            
            try {
                const url = `${this.baseUrl}/idscan-only`;
                DeveloperStatusMessages.logApiCall(url, 'POST', 'Starting request');
                
                // Create XMLHttpRequest for progress tracking
                const xhr = new XMLHttpRequest();
                
                // Set up progress tracking
                if (onProgress && typeof onProgress === 'function') {
                    xhr.upload.onprogress = function(event) {
                        if (event.lengthComputable) {
                            const progress = event.loaded / event.total;
                            DeveloperStatusMessages.logIDScanProgress('Uploading', progress);
                            onProgress(progress);
                        }
                    };
                }

                // Set up response handlers
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === XMLHttpRequest.DONE) {
                        const endTime = performance.now();
                        
                        try {
                            if (xhr.status >= 200 && xhr.status < 300) {
                                const responseData = JSON.parse(xhr.responseText);
                                DeveloperStatusMessages.logPerformance('IDScanDataSource.postIDScanOnly', startTime, endTime);
                                DeveloperStatusMessages.logApiCall(url, 'POST', `Success (${xhr.status})`);
                                DeveloperStatusMessages.logData('API Response', {
                                    status: xhr.status,
                                    wasProcessed: responseData.wasProcessed,
                                    error: responseData.error,
                                    hasScanResultBlob: !!responseData.scanResultBlob
                                });
                                resolve(responseData);
                            } else {
                                DeveloperStatusMessages.logPerformance('IDScanDataSource.postIDScanOnly (failed)', startTime, endTime);
                                DeveloperStatusMessages.logError(`API call failed with status ${xhr.status}`);
                                reject(new Error(`HTTP error! status: ${xhr.status}`));
                            }
                        } catch (parseError) {
                            DeveloperStatusMessages.logError('Failed to parse API response', parseError);
                            reject(new Error('Failed to parse response JSON'));
                        }
                    }
                };

                xhr.onerror = function() {
                    const endTime = performance.now();
                    DeveloperStatusMessages.logPerformance('IDScanDataSource.postIDScanOnly (network error)', startTime, endTime);
                    DeveloperStatusMessages.logError('Network request failed');
                    reject(new Error('Network request failed'));
                };

                // Open the request
                xhr.open('POST', url);

                // Set headers
                xhr.setRequestHeader('Content-Type', 'application/json');
                Object.keys(headers).forEach(key => {
                    if (headers[key] !== undefined) {
                        xhr.setRequestHeader(key, headers[key]);
                    }
                });

                // Send the request
                const jsonData = JSON.stringify(scanData);
                DeveloperStatusMessages.logMessage(`Sending request to ${url} with ${Object.keys(scanData).length} data fields`);
                xhr.send(jsonData);

            } catch (error) {
                DeveloperStatusMessages.logError('IDScanDataSource - postIDScanOnly error', error);
                reject(error);
            }
        });
    }
}

module.exports = IDScanDataSource; 