const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');

/**
 * Data source for Liveness Check API operations
 * Handles direct API communication for liveness check functionality
 */
class LivenessCheckDataSource {
    constructor() {
        this.baseUrl = '/api'; // Use Next.js API routes as proxy
    }

    /**
     * Post liveness check data to the backend API
     * @param {Object} livenessData - The liveness data including face scan and audit trail
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - API response
     */
    async postLivenessCheck(livenessData, headers = {}, onProgress = null) {
        return new Promise((resolve, reject) => {
            const startTime = performance.now();

            try {
                const url = `${this.baseUrl}/enrollment-3d`;

                console.log('=== LIVENESS CHECK DATA SOURCE - STARTING REQUEST ===');
                console.log('URL:', url);
                console.log('Input livenessData:', JSON.stringify(livenessData, null, 2));
                console.log('Input headers:', JSON.stringify(headers, null, 2));
                console.log('Liveness data keys:', Object.keys(livenessData || {}));
                console.log('Liveness data structure check:', {
                    hasFunction: !!livenessData?.function,
                    hasFaceScan: !!livenessData?.faceScan,
                    hasAuditTrailImage: !!livenessData?.auditTrailImage,
                    hasLowQualityAuditTrailImage: !!livenessData?.lowQualityAuditTrailImage,
                    hasSessionId: !!livenessData?.sessionId
                });

                DeveloperStatusMessages.logApiCall(url, 'POST', 'Starting request');

                // Create XMLHttpRequest for progress tracking
                const xhr = new XMLHttpRequest();
                
                // Set up progress tracking
                if (onProgress && typeof onProgress === 'function') {
                    xhr.upload.onprogress = function(event) {
                        if (event.lengthComputable) {
                            const progress = event.loaded / event.total;
                            DeveloperStatusMessages.logLivenessProgress('Uploading', progress);
                            onProgress(progress);
                        }
                    };
                }

                // Set up response handlers
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === XMLHttpRequest.DONE) {
                        const endTime = performance.now();
                        
                        try {
                            console.log('=== LIVENESS CHECK DATA SOURCE - RESPONSE RECEIVED ===');
                            console.log('Response status:', xhr.status);
                            console.log('Response text:', xhr.responseText);

                            if (xhr.status >= 200 && xhr.status < 300) {
                                const responseData = JSON.parse(xhr.responseText);
                                console.log('✅ SUCCESS - Parsed response data:', JSON.stringify(responseData, null, 2));

                                DeveloperStatusMessages.logPerformance('LivenessCheckDataSource.postLivenessCheck', startTime, endTime);
                                DeveloperStatusMessages.logApiCall(url, 'POST', `Success (${xhr.status})`);
                                DeveloperStatusMessages.logData('API Response', {
                                    status: xhr.status,
                                    wasProcessed: responseData.wasProcessed,
                                    error: responseData.error,
                                    hasScanResultBlob: !!responseData.scanResultBlob
                                });
                                resolve(responseData);
                            } else {
                                console.log('❌ ERROR - HTTP error status:', xhr.status);
                                console.log('Error response text:', xhr.responseText);

                                DeveloperStatusMessages.logPerformance('LivenessCheckDataSource.postLivenessCheck (failed)', startTime, endTime);
                                DeveloperStatusMessages.logError(`API call failed with status ${xhr.status}`);
                                reject(new Error(`HTTP error! status: ${xhr.status}`));
                            }
                        } catch (error) {
                            DeveloperStatusMessages.logError('Error parsing response', error);
                            reject(error);
                        }
                    }
                };
                
                // Set up error handler
                xhr.onerror = function() {
                    DeveloperStatusMessages.logError('Network error occurred');
                    reject(new Error('Network error occurred'));
                };
                
                // Open and send the request
                xhr.open('POST', url, true);

                // Set headers
                xhr.setRequestHeader('Content-Type', 'application/json');
                console.log('Setting Content-Type header: application/json');

                for (const [key, value] of Object.entries(headers)) {
                    if (value !== undefined && value !== null) {
                        xhr.setRequestHeader(key, value);
                        console.log(`Setting header: ${key} = ${value}`);
                    } else {
                        console.log(`Skipping header (undefined/null): ${key} = ${value}`);
                    }
                }

                // Send the request
                const requestBody = JSON.stringify(livenessData);
                console.log('=== LIVENESS CHECK DATA SOURCE - SENDING REQUEST ===');
                console.log('Request body (stringified):', requestBody);
                console.log('Request body size (bytes):', requestBody.length);
                console.log('Request body keys from original data:', Object.keys(livenessData));

                DeveloperStatusMessages.logData('Request Body Keys', Object.keys(livenessData));

                console.log('📤 SENDING XMLHttpRequest...');
                xhr.send(requestBody);
            } catch (error) {
                DeveloperStatusMessages.logError('Error in postLivenessCheck', error);
                reject(error);
            }
        });
    }
}

module.exports = LivenessCheckDataSource;
