//
// Welcome to the annotated FaceTec Device SDK core code for performing secure Liveness Checks.
//
//
// This is an example self-contained class to perform Liveness Checks with the FaceTec SDK.
// You may choose to further componentize parts of this in your own Apps based on your specific requirements.
//

const PostLivenessCheckUseCase = require('../domain/usecases/PostLivenessCheckUseCase');
const DeveloperStatusMessages = require('../infrastructure/utils/DeveloperStatusMessages');

var LivenessCheckProcessor = /** @class */ (function () {
    function LivenessCheckProcessor(sessionToken, sampleAppControllerReference, deviceKey, additionalHeaders) {
        var _this = this;
        this.latestNetworkRequest = new XMLHttpRequest();
        this.deviceKey = deviceKey || null; // Store the device key
        this.additionalHeaders = additionalHeaders || {}; // get additional headers
        this.postLivenessCheckUseCase = new PostLivenessCheckUseCase(); // Initialize use case
        
        //
        // Part 2:  Handling the Result of a FaceScan
        //
        this.processSessionResultWhileFaceTecSDKWaits = function (sessionResult, faceScanResultCallback) {
            _this.latestSessionResult = sessionResult;
            //
            // Part 3:  Handles early exit scenarios where there is no FaceScan to handle -- i.e. User Cancellation, Timeouts, etc.
            //
            if (sessionResult.status !== FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully) {
                _this.latestNetworkRequest.abort();
                _this.latestNetworkRequest = new XMLHttpRequest();
                faceScanResultCallback.cancel();
                return;
            }
            
            // IMPORTANT:  FaceTecSDK.FaceTecSessionStatus.SessionCompletedSuccessfully DOES NOT mean the Liveness Check was Successful.
            // It simply means the User completed the Session and a 3D FaceScan was created.  You still need to perform the Liveness Check on your Servers.
            
            //
            // Part 4: Use Clean Architecture - Call UseCase instead of direct API
            //
            _this.executeLivenessCheckUseCase(sessionResult, faceScanResultCallback);
        };

        //
        // New method: Execute Liveness Check using Clean Architecture UseCase
        //
        this.executeLivenessCheckUseCase = async function(sessionResult, faceScanResultCallback) {
            try {
                // Create progress callback for upload tracking
                const onProgress = function(progress) {
                    faceScanResultCallback.uploadProgress(progress);
                };

                // Execute the use case
                const result = await _this.postLivenessCheckUseCase.execute({
                    sessionResult: sessionResult,
                    deviceKey: _this.deviceKey,
                    additionalHeaders: _this.additionalHeaders,
                    onProgress: onProgress
                });

                // Handle the result
                if (result.success) {
                    // Demonstrates dynamically setting the Success Screen Message.
                    FaceTecSDK.FaceTecCustomization.setOverrideResultScreenSuccessMessage("Face Scanned\n3D Liveness Proven");
                    
                    // Proceed to next step with scanResultBlob
                    faceScanResultCallback.proceedToNextStep(result.scanResultBlob);
                } else {
                    // Handle error case
                    _this.cancelDueToNetworkError(result.errorMessage || "Unexpected API response, cancelling out.", faceScanResultCallback);
                }
            } catch (error) {
                console.error('LivenessCheckProcessor - executeLivenessCheckUseCase error:', error);
                _this.cancelDueToNetworkError(error.message || "Exception while handling API response, cancelling out.", faceScanResultCallback);
            }
        };

        //
        // Part 9:  This function gets called after the FaceTec SDK is completely done.  There are no parameters because you have already been passed all data in the processSessionWhileFaceTecSDKWaits function and have already handled all of your own results.
        //
        this.onFaceTecSDKCompletelyDone = function () {
            //
            // DEVELOPER NOTE:  onFaceTecSDKCompletelyDone() is called after the Session has completed or you signal the FaceTec SDK with cancel().
            // Calling a custom function on the Sample App Controller is done for demonstration purposes to show you that here is where you get control back from the FaceTec SDK.
            //
            // If the Liveness Check was processed get the success result from isCompletelyDone
            if (_this.latestSessionResult !== null) {
                _this.success = _this.latestSessionResult.isCompletelyDone;
            }
            // Log success message
            if (_this.success) {
                DeveloperStatusMessages.logMessage("Liveness Check Complete");
            }
            
            // Pass the complete session result to the controller
            _this.sampleAppControllerReference.onComplete(_this.latestSessionResult, 200); // Use 200 as default status
        };

        // Helper function to ensure the session is only cancelled once
        this.cancelDueToNetworkError = function (networkErrorMessage, faceScanResultCallback) {
            if (_this.cancelledDueToNetworkError === false) {
                console.error(networkErrorMessage);
                _this.cancelledDueToNetworkError = true;
                faceScanResultCallback.cancel();
            }
        };

        //
        // DEVELOPER NOTE:  This public convenience method is for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In your code, you may not even want or need to do this.
        //
        this.isSuccess = function () {
            return _this.success;
        };

        //
        // DEVELOPER NOTE:  These properties are for demonstration purposes only so the Sample App can get information about what is happening in the processor.
        // In the code in your own App, you can pass around signals, flags, intermediates, and results however you would like.
        //
        this.success = false;
        this.sampleAppControllerReference = sampleAppControllerReference;
        this.latestSessionResult = null;
        this.cancelledDueToNetworkError = false;

        //
        // Part 1:  Starting the FaceTec Session
        //
        // Required parameters:
        // - FaceTecFaceScanProcessor:  A class that implements FaceTecFaceScanProcessor, which handles the FaceScan when the User completes a Session.  In this example, "this" implements the class.
        // - sessionToken:  A valid Session Token you just created by calling your API to get a Session Token from the Server SDK.
        //
        new FaceTecSDK.FaceTecSession(this, sessionToken);
    }
    return LivenessCheckProcessor;
}());

// Export the LivenessCheckProcessor class for use in Node.js/webpack environments
module.exports = LivenessCheckProcessor;
